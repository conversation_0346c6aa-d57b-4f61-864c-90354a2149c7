server:
  port: 8083
logging:
  level:
    root: INFO
    com.baomidou.mybatisplus: DEBUG
    com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor: DEBUG

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
spring:
  datasource:
#    url: **************************************/
    url: **************************************/
    # url: ${POSTGRESQL_NEW_HOST:**************************************}/
    username: ${POSTGRESQL_NEW_USERNAME:market}
    # password: ${POSTGRESQL_NEW_PASSWORD:marketdev}
    password: ${POSTGRESQL_NEW_PASSWORD:marketpg}
    driver-class-name: org.postgresql.Driver
    type: com.zaxxer.hikari.HikariDataSource
    maximum-pool-size: 30
    minimum-idle: 5
    connection-timeout: 30000
    idle-timeout: 600000
    max-lifetime: 1800000
  data:
    redis:
      host: ${I_REDIS_HOST:***************}
      port: 6379
      database: 0

# 出库 扣减库存接口
outbound:
  create-order:
    host: ${BMS_INVENTORY_SIT_HOST:https://bms-inventory-sit.holderzone.cn}
inventory-service:
  intranet-host: ${BMS_INVENTORY_SIT_INTRANET_HOST:http://holder-fsgen-inventory.market:8384}

    # path: ${OUT_BOUND_CREATE_ORDER_PATH:/api/sale_outbound/create_order}

#
# store-info:
#   config:
#     details:
#       # host: ${BMS_MARKET_SIT_HOST:https://market-sit.holderzone.cn}
#       host: ${BMS_MARKET_SIT_HOST:http://***************:8000}
#       path: ${STORE_INFO_DETAILS_PATH:/api/v1/device/retail/store_info/config/details}
#     zt-channel-name: ${ZT_CHANNEL_NAME:POS}


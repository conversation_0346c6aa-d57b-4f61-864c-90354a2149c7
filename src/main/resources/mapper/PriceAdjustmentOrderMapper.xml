<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.PriceAdjustmentOrderMapper">

    <select id="queryPriceAdjustmentOrderListByGoodsName"
            resultType="com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderPO">
        select distinct priceAdjustmentOrder.id,
                        priceAdjustmentOrder.store_id,
                        priceAdjustmentOrder.store_name,
                        priceAdjustmentOrder.channel_id,
                        priceAdjustmentOrder.channel_name,
                        priceAdjustmentOrder.source_type,
                        priceAdjustmentOrder.user_id,
                        priceAdjustmentOrder.username,
                        priceAdjustmentOrder.account,
                        priceAdjustmentOrder.disabled,
                        priceAdjustmentOrder.created_at,
                        priceAdjustmentOrder.updated_at
        from price_adjustment_order priceAdjustmentOrder
                 left join price_adjustment_order_goods priceAdjustmentOrderGoods
                           on priceAdjustmentOrder.id = priceAdjustmentOrderGoods.price_adjustment_order_id
                 left join price_adjustment_order_goods_change_info priceAdjustmentOrderGoodsChangeInfo
                           on priceAdjustmentOrderGoodsChangeInfo.price_adjustment_order_id = priceAdjustmentOrder.id
        where priceAdjustmentOrder.source_type = 2
          and (priceAdjustmentOrderGoods.goods_name Ilike #{goodsName} or
               priceAdjustmentOrderGoodsChangeInfo.goods_sale_name Ilike #{goodsName})
          and priceAdjustmentOrder.disabled = false
          and priceAdjustmentOrderGoods.disabled = false
          and priceAdjustmentOrderGoodsChangeInfo.disabled = false
    </select>
</mapper>

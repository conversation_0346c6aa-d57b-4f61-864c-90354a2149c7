<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holderpaasmdm.mapper.OrdersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.holderzone.holderpaasmdm.model.po.OrdersPO">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="order_number" property="orderNumber" />
        <result column="version" property="version" />
        <result column="before_state" property="beforeState" />
        <result column="member_name" property="memberName" />
        <result column="member_phone" property="memberPhone" />
        <result column="member_guid" property="memberGuid" />
        <result column="business_day" property="businessDay" />
        <result column="business_day_start" property="businessDayStart" />
        <result column="business_day_end" property="businessDayEnd" />
        <result column="operater_id" property="operaterId" />
        <result column="operater_name" property="operaterName" />
        <result column="operater_phone" property="operaterPhone" />
        <result column="shopping_guide_id" property="shoppingGuideId" />
        <result column="shopping_guide_name" property="shoppingGuideName" />
        <result column="store_id" property="storeId" />
        <result column="store_team_info_id" property="storeTeamInfoId" />
        <result column="holder_store_id" property="holderStoreId" />
        <result column="store_name" property="storeName" />
        <result column="channel_id" property="channelId" />
        <result column="channel_name" property="channelName" />
        <result column="company_id" property="companyId" />
        <result column="receive_price" property="receivePrice" />
        <result column="origin_price" property="originPrice" />
        <result column="total_origin_price" property="totalOriginPrice" />
        <result column="discount_price" property="discountPrice" />
        <result column="paid_at" property="paidAt" />
        <result column="change_money" property="changeMoney" />
        <result column="actually_paid_amount" property="actuallyPaidAmount" />
        <result column="item_total_price" property="itemTotalPrice" />
        <result column="device_number" property="deviceNumber" />
        <result column="external_discounts" property="externalDiscounts" typeHandler="com.holderzone.holderpaasmdm.common.handler.JacksonTypeForListHandler" />
        <result column="external_shopcar" property="externalShopcar" />
        <result column="state" property="state" />
        <result column="order_source" property="orderSource" />
        <result column="disabled" property="disabled" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_at_start" property="createAtStart" />
        <result column="create_at_end" property="createAtEnd" />
        <result column="member_card_info_guid" property="memberCardInfoGuid" />
        <result column="coupon_rollback" property="couponRollback" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, order_number, version, before_state, member_name, member_phone, member_guid, business_day, business_day_start, business_day_end, operater_id, operater_name, operater_phone, shopping_guide_id, shopping_guide_name, store_id, store_team_info_id, holder_store_id, store_name, channel_id, channel_name, company_id, receive_price, origin_price, total_origin_price, discount_price, paid_at, change_money, actually_paid_amount, item_total_price, device_number, external_discounts, external_shopcar, state, order_source, disabled, create_at, update_at, create_at_start, create_at_end, member_card_info_guid, coupon_rollback, created_at, updated_at
    </sql>
    <select id="queryOrdersByConditions" resultType="com.holderzone.holderpaasmdm.model.po.OrdersPO">
        SELECT <include refid="Base_Column_List"/>
        from orders
        <where>
            <if test="storeDailySettleDTO.businessDayStart != null and storeDailySettleDTO.businessDayStart != ''">
                and business_day::DATE <![CDATA[ >= ]]> CAST(#{storeDailySettleDTO.businessDayStart} AS date)
            </if>
            <if test="storeDailySettleDTO.businessDayEnd != null and storeDailySettleDTO.businessDayEnd != ''">
                and business_day::DATE <![CDATA[ <= ]]> CAST(#{storeDailySettleDTO.businessDayEnd} AS date)
            </if>

            <if test="storeDailySettleDTO.storeIdList != null and storeDailySettleDTO.storeIdList.size() > 0">
                and store_team_info_id in
                <foreach collection="storeDailySettleDTO.storeIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="companyId != null">
                and company_id = #{companyId}
            </if>

        </where>

    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.StoreSalesProgramGoodsMapper">

    <update id="batchUpdateSellingPrice">
        UPDATE store_sales_program_goods
        set updated_at = #{timestamp},
            selling_price = CASE
            <foreach collection="paramList" item="param">
                    WHEN goods_package_sku_id = #{param.conditionId} THEN #{param.adjustmentPrice}
            </foreach>
            END
        where store_sales_program_id in
        <foreach collection="allSalesProgramIdSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and goods_package_sku_id in
        <foreach collection="paramList" item="param" open="(" separator="," close=")">
                #{param.conditionId}
        </foreach>
    </update>
</mapper>

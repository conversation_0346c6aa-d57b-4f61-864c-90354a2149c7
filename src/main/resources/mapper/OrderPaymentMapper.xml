<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holderpaasmdm.mapper.OrderPaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.holderzone.holderpaasmdm.model.po.OrderPaymentPO">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="order_id" property="orderId" />
        <result column="order_number" property="orderNumber" />
        <result column="company_id" property="companyId" />
        <result column="payment_number" property="paymentNumber" />
        <result column="payment_type" property="paymentType" />
        <result column="payment_method_name" property="paymentMethodName" />
        <result column="payment_method_id" property="paymentMethodId" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="payment_auth_code" property="paymentAuthCode" />
        <result column="payment_terminal_id" property="paymentTerminalId" />
        <result column="is_pay_success" property="isPaySuccess" />
        <result column="is_pre_pay_success" property="isPrePaySuccess" />
        <result column="change_amount" property="changeAmount" />
        <result column="member_payment_args" property="memberPaymentArgs" />
        <result column="pay_success_time" property="paySuccessTime" />
        <result column="pay_success_time_start" property="paySuccessTimeStart" />
        <result column="pay_success_time_end" property="paySuccessTimeEnd" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="sort" property="sort" />
        <result column="disabled" property="disabled" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, order_id, order_number, company_id, payment_number, payment_type, payment_method_name, payment_method_id, payment_amount, payment_auth_code, payment_terminal_id, is_pay_success, is_pre_pay_success, change_amount, member_payment_args, pay_success_time, pay_success_time_start, pay_success_time_end, create_at, update_at, sort, disabled, created_at, updated_at
    </sql>

</mapper>

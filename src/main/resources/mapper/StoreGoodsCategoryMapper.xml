<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.StoreGoodsCategoryMapper">

    <select id="queryGenerationsVOListByIdList" resultType="com.holderzone.holderpaasmdm.model.po.StoreGoodsCategoryPO">
        WITH RECURSIVE tree AS (SELECT id, name, parent_id, sort, store_id, level
                                FROM store_goods_category
                                WHERE id IN
                                <foreach collection="category" item="id" open="(" separator="," close=")">
                                    #{id}
                                </foreach>
                                and disabled = false
                                UNION ALL
                                -- 递归部分：查找所有子节点的父节点
                                SELECT s.id, s.name, s.parent_id, s.sort, s.store_id, s.level
                                FROM store_goods_category s
                                         INNER JOIN tree t ON s.id = t.parent_id
                                where s.disabled = false)
        SELECT distinct *
        FROM tree
        ORDER BY sort;
    </select>
</mapper>

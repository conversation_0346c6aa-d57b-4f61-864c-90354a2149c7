<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.StoreSalesProgramGoodsPriceMapper">

    <update id="batchUpdateSellingPrice">
        UPDATE store_sales_program_goods_price
        set updated_at = #{timestamp},
            time_section_price = CASE
            <foreach collection="paramList" item="param">
                    WHEN id = #{param.conditionId} THEN #{param.adjustmentPrice}
            </foreach>
            END,
            time_section_price_compute = CASE
            <foreach collection="paramList" item="param">
                    WHEN id = #{param.conditionId} THEN #{param.adjustmentPrice}
            </foreach>
            END
        where id in
        <foreach collection="paramList" item="param" open="(" separator="," close=")">
           #{param.conditionId}
        </foreach>
    </update>
</mapper>

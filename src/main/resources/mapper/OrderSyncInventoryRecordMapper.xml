<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holderpaasmdm.mapper.OrderSyncInventoryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.holderzone.holderpaasmdm.model.po.OrderSyncInventoryRecordPO">
        <id column="id" property="id" />
        <result column="order_number" property="orderNumber" />
        <result column="refund_number" property="refundNumber" />
        <result column="type" property="type" />
        <result column="param" property="param" />
        <result column="flag" property="flag" />
        <result column="response" property="response" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_number, refund_number, type, param, flag, response, created_at, updated_at
    </sql>

</mapper>

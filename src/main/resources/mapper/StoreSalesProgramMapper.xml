<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.StoreSalesProgramMapper">

    <select id="queryStoreSalesProgramByStoreSalesProgramGoodsIdList"
            resultType="com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO">
        select distinct storeSalesProgram.id,
                        storeSalesProgram.time_section
        from store_sales_program storeSalesProgram
                 left join store_sales_program_goods storeSalesProgramGoods
                           on storeSalesProgramGoods.store_sales_program_id = storeSalesProgram.id
        where storeSalesProgramGoods.id in
        <foreach collection="storeSalesProgramGoodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
          and storeSalesProgram.is_enable = true
          and storeSalesProgram.disabled = false
          and storeSalesProgramGoods.disabled = false
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holderpaasmdm.mapper.OrderRefundPaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.holderzone.holderpaasmdm.model.po.OrderRefundPaymentPO">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="order_refund_id" property="orderRefundId" />
        <result column="order_number" property="orderNumber" />
        <result column="refund_number" property="refundNumber" />
        <result column="company_id" property="companyId" />
        <result column="refund_payment_type" property="refundPaymentType" />
        <result column="refund_payment_id" property="refundPaymentId" />
        <result column="refund_payment_method_id" property="refundPaymentMethodId" />
        <result column="refund_payment_name" property="refundPaymentName" />
        <result column="refund_payment_number" property="refundPaymentNumber" />
        <result column="refund_payment_amount" property="refundPaymentAmount" />
        <result column="is_refund_success" property="isRefundSuccess" />
        <result column="is_pre_refund_success" property="isPreRefundSuccess" />
        <result column="refund_success_time" property="refundSuccessTime" />
        <result column="refund_success_time_start" property="refundSuccessTimeStart" />
        <result column="refund_success_time_end" property="refundSuccessTimeEnd" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="sort" property="sort" />
        <result column="disabled" property="disabled" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, order_refund_id, order_number, refund_number, company_id, refund_payment_type, refund_payment_id, refund_payment_method_id, refund_payment_name, refund_payment_number, refund_payment_amount, is_refund_success, is_pre_refund_success, refund_success_time, refund_success_time_start, refund_success_time_end, create_at, update_at, sort, disabled, created_at, updated_at
    </sql>

</mapper>

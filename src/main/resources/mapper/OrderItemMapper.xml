<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holderpaasmdm.mapper.OrderItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.holderzone.holderpaasmdm.model.po.OrderItemPO">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="order_id" property="orderId" />
        <result column="order_number" property="orderNumber" />
        <result column="company_id" property="companyId" />
        <result column="purchase_quantity" property="purchaseQuantity" />
        <result column="origin_total_price_in_shopcaritem" property="originTotalPriceInShopcaritem" />
        <result column="shop_price" property="shopPrice" />
        <result column="actual_receive_price" property="actualReceivePrice" />
        <result column="store_sales_program_id" property="storeSalesProgramId" />
        <result column="store_sales_program_goods_id" property="storeSalesProgramGoodsId" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_uuid_in_shopcar" property="goodsUuidInShopcar" />
        <result column="retail_discount_amount" property="retailDiscountAmount" />
        <result column="member_discount_amount" property="memberDiscountAmount" />
        <result column="discount_amount_all" property="discountAmountAll" />
        <result column="mercandise_type" property="mercandiseType" />
        <result column="costs" property="costs" />
        <result column="vip_price" property="vipPrice" />
        <result column="spu_code" property="spuCode" />
        <result column="spu_id" property="spuId" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="picture_url" property="pictureUrl" />
        <result column="goods_sale_name" property="goodsSaleName" />
        <result column="barcode" property="barcode" />
        <result column="goods_custom_code" property="goodsCustomCode" />
        <result column="combo_type" property="comboType" />
        <result column="combo_type_name" property="comboTypeName" />
        <result column="selling_price" property="sellingPrice" />
        <result column="discount_price_in_shopcar" property="discountPriceInShopcar" />
        <result column="quantity" property="quantity" />
        <result column="goods_unit_name" property="goodsUnitName" />
        <result column="goods_unit_id" property="goodsUnitId" />
        <result column="goods_specification" property="goodsSpecification" />
        <result column="goods_property" property="goodsProperty" />
        <result column="tag_name" property="tagName" />
        <result column="plu_code" property="pluCode" />
        <result column="is_upsell" property="isUpsell" />
        <result column="upsell_time" property="upsellTime" />
        <result column="discount_total_price_item" property="discountTotalPriceItem" />
        <result column="extra" property="extra" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="disabled" property="disabled" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, order_id, order_number, company_id, purchase_quantity, origin_total_price_in_shopcaritem, shop_price, actual_receive_price, store_sales_program_id, store_sales_program_goods_id, goods_id, goods_uuid_in_shopcar, retail_discount_amount, member_discount_amount, discount_amount_all, mercandise_type, costs, vip_price, spu_code, spu_id, category_id, category_name, picture_url, goods_sale_name, barcode, goods_custom_code, combo_type, combo_type_name, selling_price, discount_price_in_shopcar, quantity, goods_unit_name, goods_unit_id, goods_specification, goods_property, tag_name, plu_code, is_upsell, upsell_time, discount_total_price_item, extra, create_at, update_at, disabled, created_at, updated_at
    </sql>

</mapper>

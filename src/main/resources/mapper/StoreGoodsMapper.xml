<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.StoreGoodsMapper">

    <select id="queryStoreGoodsForAvailableForSaleWeighList"
            resultType="com.holderzone.holderpaasmdm.model.po.StoreGoodsPO">
        select distinct storeGoods.id,
                storeGoods.goods_name,
                storeGoods.store_id,
                storeGoods.goods_id,
                storeGoods.pinyin_code,
                storeGoods.spu_code,
                storeGoods.combo_type,
                storeGoods.goods_features,
                storeGoods.goods_type,
                storeGoods.inventory_property,
                storeGoods.valuation_method,
                storeGoods.cover,
                storeGoods.category,
                storeGoods.goods_unit,
                storeGoods.goods_custom_code,
                storeGoods.brand,
                storeGoods.barcode,
                storeGoods.goods_label,
                storeGoods.costs,
                storeGoods.selling_price,
                storeGoods.provider,
                storeGoods.production_date,
                storeGoods.warranty_date,
                storeGoods.manufacturer,
                storeGoods.origin,
                storeGoods.introduction_text,
                storeGoods.status,
                storeGoods.listing_status,
                storeGoods.goods_uuid,
                storeGoods.related_goods_uuid,
                storeGoods.sort,
                storeGoods.goods_spec,
                storeGoods.created_at,
                storeGoods.updated_at
        from store_sales_program storeSalesProgram
                 left join store_sales_program_goods storeSalesProgramGoods
                           on storeSalesProgramGoods.store_sales_program_id = storeSalesProgram.id
                 left join store_goods storeGoods
                           on storeGoods.id = storeSalesProgramGoods.goods_id
        where storeSalesProgram.is_enable = true
          and storeSalesProgram.store_id = #{storeId}
          and storeSalesProgram.channel_id = #{channelId}
          and storeGoods.status = 1
          <if test="barcode != null and barcode != ''">
              and storeGoods.barcode ILIKE #{barcode}
          </if>
          <if test="goodsValuationMethodIdList != null and goodsValuationMethodIdList.size() > 0">
              and (
                  <foreach collection="goodsValuationMethodIdList" item="goodsValuationMethodId" separator="or" >
                    storeGoods.valuation_method = #{goodsValuationMethodId}
                  </foreach>
              )
          </if>
            <if test="storeGoodsIdList != null and storeGoodsIdList.size() > 0">
                and storeGoods.id in
                <foreach collection="storeGoodsIdList" item="storeGoodsId" separator="," open="(" close=")">
                    #{storeGoodsId}
                </foreach>
            </if>
          and storeSalesProgram.disabled = false
          and storeGoods.disabled = false
          and storeSalesProgramGoods.disabled = false
    </select>

    <select id="queryStoreGoodsByScaleCustomCode" resultType="com.holderzone.holderpaasmdm.model.po.StoreGoodsPO">
        select storeGoods.id,
               storeGoods.updated_at,
               storeGoods.id,
               storeGoods.goods_name,
               storeGoods.store_id,
               storeGoods.goods_id,
               storeGoods.pinyin_code,
               storeGoods.spu_code,
               storeGoods.combo_type,
               storeGoods.goods_features,
               storeGoods.goods_type,
               storeGoods.inventory_property,
               storeGoods.valuation_method,
               storeGoods.cover,
               storeGoods.category,
               storeGoods.goods_unit,
               storeGoods.goods_custom_code,
               storeGoods.brand,
               storeGoods.barcode,
               storeGoods.goods_label,
               storeGoods.costs,
               storeGoods.selling_price,
               storeGoods.provider,
               storeGoods.production_date,
               storeGoods.warranty_date,
               storeGoods.manufacturer,
               storeGoods.origin,
               storeGoods.introduction_text,
               storeGoods.status,
               storeGoods.listing_status,
               storeGoods.goods_uuid,
               storeGoods.related_goods_uuid,
               storeGoods.sort,
               storeGoods.goods_spec,
               storeGoods.disabled,
               storeGoods.created_at,
               storeGoods.updated_at
        from goods_scale_code goodsScaleCode
                 left join store_goods storeGoods on goodsScaleCode.goods_id = storeGoods.id
        where goodsScaleCode.scale_custom_code = #{scaleCustomCode}
          and goodsScaleCode.scale_type = #{scaleType}
          and goodsScaleCode.store_id = #{storeId}
          and storeGoods.disabled = false
          and goodsScaleCode.disabled = false
    </select>

    <select id="queryStoreGoodsByIdListAndKeywords"
            resultType="com.holderzone.holderpaasmdm.model.po.StoreGoodsPO">
        select distinct storeGoods.id,
               storeGoods.updated_at,
               storeGoods.id,
               storeGoods.goods_name,
               storeGoods.store_id,
               storeGoods.goods_id,
               storeGoods.pinyin_code,
               storeGoods.spu_code,
               storeGoods.combo_type,
               storeGoods.goods_features,
               storeGoods.goods_type,
               storeGoods.inventory_property,
               storeGoods.valuation_method,
               storeGoods.cover,
               storeGoods.category,
               storeGoods.goods_unit,
               storeGoods.goods_custom_code,
               storeGoods.brand,
               storeGoods.barcode,
               storeGoods.goods_label,
               storeGoods.costs,
               storeGoods.selling_price,
               storeGoods.provider,
               storeGoods.production_date,
               storeGoods.warranty_date,
               storeGoods.manufacturer,
               storeGoods.origin,
               storeGoods.introduction_text,
               storeGoods.status,
               storeGoods.listing_status,
               storeGoods.goods_uuid,
               storeGoods.related_goods_uuid,
               storeGoods.sort,
               storeGoods.goods_spec,
               storeGoods.disabled,
               storeGoods.created_at,
               storeGoods.updated_at
        from store_goods storeGoods
        where storeGoods.id in
          <foreach collection="idList" item="id" separator="," open="(" close=")">
                  #{id}
          </foreach>
          and (storeGoods.goods_custom_code ILIKE #{keywords} or storeGoods.pinyin_code ILIKE #{keywords})
          and storeGoods.disabled = false
        union all
        select distinct storeGoods.id,
               storeGoods.updated_at,
               storeGoods.id,
               storeGoods.goods_name,
               storeGoods.store_id,
               storeGoods.goods_id,
               storeGoods.pinyin_code,
               storeGoods.spu_code,
               storeGoods.combo_type,
               storeGoods.goods_features,
               storeGoods.goods_type,
               storeGoods.inventory_property,
               storeGoods.valuation_method,
               storeGoods.cover,
               storeGoods.category,
               storeGoods.goods_unit,
               storeGoods.goods_custom_code,
               storeGoods.brand,
               storeGoods.barcode,
               storeGoods.goods_label,
               storeGoods.costs,
               storeGoods.selling_price,
               storeGoods.provider,
               storeGoods.production_date,
               storeGoods.warranty_date,
               storeGoods.manufacturer,
               storeGoods.origin,
               storeGoods.introduction_text,
               storeGoods.status,
               storeGoods.listing_status,
               storeGoods.goods_uuid,
               storeGoods.related_goods_uuid,
               storeGoods.sort,
               storeGoods.goods_spec,
               storeGoods.disabled,
               storeGoods.created_at,
               storeGoods.updated_at
        from store_goods storeGoods
                 left join goods_package_sku goodsPackageSku on goodsPackageSku.store_goods_id = storeGoods.id
        where storeGoods.id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
          and goodsPackageSku.barcode ILIKE #{keywords}
          and goodsPackageSku.disabled = false
          and storeGoods.disabled = false
        union all
        select distinct storeGoods.id,
               storeGoods.updated_at,
               storeGoods.id,
               storeGoods.goods_name,
               storeGoods.store_id,
               storeGoods.goods_id,
               storeGoods.pinyin_code,
               storeGoods.spu_code,
               storeGoods.combo_type,
               storeGoods.goods_features,
               storeGoods.goods_type,
               storeGoods.inventory_property,
               storeGoods.valuation_method,
               storeGoods.cover,
               storeGoods.category,
               storeGoods.goods_unit,
               storeGoods.goods_custom_code,
               storeGoods.brand,
               storeGoods.barcode,
               storeGoods.goods_label,
               storeGoods.costs,
               storeGoods.selling_price,
               storeGoods.provider,
               storeGoods.production_date,
               storeGoods.warranty_date,
               storeGoods.manufacturer,
               storeGoods.origin,
               storeGoods.introduction_text,
               storeGoods.status,
               storeGoods.listing_status,
               storeGoods.goods_uuid,
               storeGoods.related_goods_uuid,
               storeGoods.sort,
               storeGoods.goods_spec,
               storeGoods.disabled,
               storeGoods.created_at,
               storeGoods.updated_at
        from store_goods storeGoods
                 left join store_sales_program_goods storeSalesProgramGoods on storeSalesProgramGoods.goods_id = storeGoods.id
        where storeGoods.id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
          and storeSalesProgramGoods.goods_sale_name ILIKE #{keywords}
          and storeSalesProgramGoods.store_sales_program_id = #{storeSalesProgramId}
          and storeSalesProgramGoods.disabled = false
          and storeGoods.disabled = false
    </select>

    <select id="queryStoreGoodsByCode" resultType="com.holderzone.holderpaasmdm.model.po.StoreGoodsPO">
        SELECT *
        FROM (SELECT storeGoods.id,
                     storeGoods.updated_at,
                     storeGoods.id,
                     storeGoods.goods_name,
                     storeGoods.store_id,
                     storeGoods.goods_id,
                     storeGoods.pinyin_code,
                     storeGoods.spu_code,
                     storeGoods.combo_type,
                     storeGoods.goods_features,
                     storeGoods.goods_type,
                     storeGoods.inventory_property,
                     storeGoods.valuation_method,
                     storeGoods.cover,
                     storeGoods.category,
                     storeGoods.goods_unit,
                     storeGoods.goods_custom_code,
                     storeGoods.brand,
                     storeGoods.barcode,
                     storeGoods.goods_label,
                     storeGoods.costs,
                     storeGoods.selling_price,
                     storeGoods.provider,
                     storeGoods.production_date,
                     storeGoods.warranty_date,
                     storeGoods.manufacturer,
                     storeGoods.origin,
                     storeGoods.introduction_text,
                     storeGoods.status,
                     storeGoods.listing_status,
                     storeGoods.goods_uuid,
                     storeGoods.related_goods_uuid,
                     storeGoods.sort,
                     storeGoods.goods_spec,
                     storeGoods.disabled,
                     storeGoods.created_at,
                     storeGoods.updated_at
              FROM store_goods storeGoods
              WHERE storeGoods.store_id = #{storeId}
                and storeGoods.disabled = false
                and goods_custom_code ILIKE #{code}
              UNION
              SELECT storeGoods.id,
                     storeGoods.updated_at,
                     storeGoods.id,
                     storeGoods.goods_name,
                     storeGoods.store_id,
                     storeGoods.goods_id,
                     storeGoods.pinyin_code,
                     storeGoods.spu_code,
                     storeGoods.combo_type,
                     storeGoods.goods_features,
                     storeGoods.goods_type,
                     storeGoods.inventory_property,
                     storeGoods.valuation_method,
                     storeGoods.cover,
                     storeGoods.category,
                     storeGoods.goods_unit,
                     storeGoods.goods_custom_code,
                     storeGoods.brand,
                     storeGoods.barcode,
                     storeGoods.goods_label,
                     storeGoods.costs,
                     storeGoods.selling_price,
                     storeGoods.provider,
                     storeGoods.production_date,
                     storeGoods.warranty_date,
                     storeGoods.manufacturer,
                     storeGoods.origin,
                     storeGoods.introduction_text,
                     storeGoods.status,
                     storeGoods.listing_status,
                     storeGoods.goods_uuid,
                     storeGoods.related_goods_uuid,
                     storeGoods.sort,
                     storeGoods.goods_spec,
                     storeGoods.disabled,
                     storeGoods.created_at,
                     storeGoods.updated_at
              FROM store_goods storeGoods
                       left join goods_package_sku goodsPackageSku on storeGoods.id = goodsPackageSku.store_goods_id
              WHERE storeGoods.store_id = #{storeId}
                and storeGoods.disabled = false
                and goodsPackageSku.store_id = #{storeId}
                and goodsPackageSku.disabled = false
                and (goodsPackageSku.barcode ILIKE #{code} or goodsPackageSku.sku_code ILIKE #{code})
              UNION
              SELECT storeGoods.id,
                     storeGoods.updated_at,
                     storeGoods.id,
                     storeGoods.goods_name,
                     storeGoods.store_id,
                     storeGoods.goods_id,
                     storeGoods.pinyin_code,
                     storeGoods.spu_code,
                     storeGoods.combo_type,
                     storeGoods.goods_features,
                     storeGoods.goods_type,
                     storeGoods.inventory_property,
                     storeGoods.valuation_method,
                     storeGoods.cover,
                     storeGoods.category,
                     storeGoods.goods_unit,
                     storeGoods.goods_custom_code,
                     storeGoods.brand,
                     storeGoods.barcode,
                     storeGoods.goods_label,
                     storeGoods.costs,
                     storeGoods.selling_price,
                     storeGoods.provider,
                     storeGoods.production_date,
                     storeGoods.warranty_date,
                     storeGoods.manufacturer,
                     storeGoods.origin,
                     storeGoods.introduction_text,
                     storeGoods.status,
                     storeGoods.listing_status,
                     storeGoods.goods_uuid,
                     storeGoods.related_goods_uuid,
                     storeGoods.sort,
                     storeGoods.goods_spec,
                     storeGoods.disabled,
                     storeGoods.created_at,
                     storeGoods.updated_at
              FROM store_goods storeGoods
                       INNER JOIN goods_scale_code gsc
                                  ON gsc.goods_id = storeGoods.id
                                      AND gsc.store_id = #{storeId}
                                      AND gsc.scale_type = #{scaleType}
                                      AND gsc.disabled = false
                                      AND gsc.plu_code ILIKE #{code}) AS combined_results
    </select>
    <select id="queryAllSkuGoodsByTag"
            resultType="com.holderzone.holderpaasmdm.model.dto.SqlQueryGoodsByTagListDTO">
        SELECT
        gps."id" as goodsPackageSkuId,
        gps.barcode as goodsPackageSkuBarCode,
        sspg."id" as storeSalesProgramGoodsId,
        sspg.goods_sale_name as storeSalesProgramGoodsName,
        sspgs."id" as storeSalesProgramGoodsPriceId,
        ssp."id" as storeSalesProgramId,
        ssp.is_default as storeSalesProgramIsDefault,
        ssp.time_section as storeSalesProgramTimeSection,
        sg.*
        FROM store_goods sg
        JOIN goods_package_sku gps ON gps.store_goods_id = sg."id"
        LEFT JOIN store_sales_program_goods sspg ON sspg.goods_package_sku_id = gps."id"
        LEFT JOIN store_sales_program_goods_price sspgs ON sspgs.store_sales_program_goods_id = sspg."id"
        LEFT JOIN store_sales_program ssp ON sspgs.store_sales_program_id = ssp."id"
        WHERE sspg.id in
        <foreach collection="allStoreSalesProgramGoodsIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and ssp.id in
        <foreach collection="storeSalesProgramIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND sg.disabled = FALSE
        AND gps.disabled = FALSE
        AND sspg.disabled = FALSE
        AND sspgs.disabled = FALSE
        and ssp.disabled = FALSE
    </select>
</mapper>

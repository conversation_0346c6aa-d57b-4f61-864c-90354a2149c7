<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.SaleCategoryMapper">

    <select id="queryGenerationsVOListByIdListAndChannelId"
            resultType="com.holderzone.holderpaasmdm.model.po.SaleCategoryPO">
        WITH RECURSIVE tree AS (SELECT id, name, parent_id, sort, store_id, channel_id, level
                                FROM sale_category
                                WHERE id IN
                                <foreach item="item" collection="idList" open="(" separator="," close=")">
                                        #{item}
                                </foreach>
                                  and channel_id = #{channelId} and disabled = false
                                UNION ALL
                                -- 递归部分：查找所有子节点的父节点
                                SELECT s.id, s.name, s.parent_id, s.sort, s.store_id, s.channel_id, s.level
                                FROM sale_category s
                                         INNER JOIN tree t ON s.id = t.parent_id
                                where s.parent_id = 0 and s.disabled = false)
        SELECT distinct *
        FROM tree
        ORDER BY sort;
    </select>

    <select id="querySaleCategoryIdBySaleProgramIdAndChannelIdAndStoreId" resultType="Integer">
        select sale_category.id
        from sale_category
                 left join sale_category_and_store_goods on sale_category.id = sale_category_and_store_goods.sale_category_id
                 left join store_sales_program_goods
                           on store_sales_program_goods.goods_id = sale_category_and_store_goods.store_goods_id
                 left join store_sales_program on store_sales_program_goods.store_sales_program_id = store_sales_program.id
        where store_sales_program.id = #{storeSalesProgramId}
          and store_sales_program.store_id = #{storeId}
          and store_sales_program.channel_id = #{channelId}
          and store_sales_program.is_enable = true
          and sale_category.store_id = #{storeId}
          and sale_category.channel_id = #{channelId}
          and store_sales_program.disabled = false
          and sale_category.disabled = false
          and store_sales_program_goods.disabled = false
          and sale_category_and_store_goods.disabled = false
    </select>
</mapper>

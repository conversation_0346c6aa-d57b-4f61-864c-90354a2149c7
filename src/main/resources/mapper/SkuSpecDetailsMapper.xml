<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holderpaasmdm.mapper.SkuSpecDetailsMapper">

    <select id="queryMatchSuccessfulSkuIdByKeywords" resultType="java.lang.Integer">
        select distinct skuSpecDetails.sku_id
        from spec
                 left join spec_detail specDetail on spec.id = specDetail.spec_id
                 left join goods_spec_detail goodsSpecDetail on goodsSpecDetail.spec_detail_id = specDetail.id
                 left join sku_spec_details skuSpecDetails on goodsSpecDetail.id = skuSpecDetails.goods_spec_detail_id
        where spec.store_id = #{storeId}
          and specDetail.name Ilike #{keywords}
          and spec.disabled = false
          and specDetail.disabled = false
          and goodsSpecDetail.disabled = false
          and skuSpecDetails.disabled = false
    </select>

    <select id="queryBySkuIdIn" resultType="com.holderzone.holderpaasmdm.model.dto.SqlQuerySkuSpecInfoDTO">
        SELECT
        ssd.sku_id,
        gsd."id" as goodsSpecDetailId,
        sd."id" as specDetailId,
        sd."name" as specDetailName
        FROM sku_spec_details ssd
        JOIN goods_spec_detail gsd ON ssd.goods_spec_detail_id = gsd."id"
        join goods_spec gs on gs.id = gsd.goods_spec_id
        JOIN spec_detail sd ON gsd.spec_detail_id = sd."id"
        WHERE
        ssd.sku_id IN
        <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and ssd.disabled = FALSE
        and gsd.disabled = FALSE
        and gs.disabled = FALSE
        and sd.disabled = FALSE
        order by ssd.sku_id, gs.sort, gsd.sort
    </select>
</mapper>

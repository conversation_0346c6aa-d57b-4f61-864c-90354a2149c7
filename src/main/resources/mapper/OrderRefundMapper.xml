<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.holderpaasmdm.mapper.OrderRefundMapper">
    <select id="queryOrderRefundList" resultType="com.holderzone.holderpaasmdm.model.po.OrderRefundPO">
        select distinct order_refund.id,
                        order_refund.record_id,
                        order_refund.order_id,
                        order_refund.order_number,
                        order_refund.refund_number,
                        order_refund.refund_amount,
                        order_refund.actually_refund_amount,
                        order_refund.company_id,
                        order_refund.operater_id,
                        order_refund.operater_name,
                        order_refund.operater_phone,
                        order_refund.store_id,
                        order_refund.refund_reason,
                        order_refund.refund_type_alias,
                        order_refund.device_number,
                        order_refund.refund_type,
                        order_refund.refund_payment_method,
                        order_refund.create_at,
                        order_refund.update_at
        from order_refund
                 left join orders on orders.order_number = order_refund.order_number
                 left join order_refund_payment on order_refund_payment.refund_number = order_refund.refund_number
        where
          order_refund.disabled = false
          and orders.disabled = false
          <if test = "params.storeId != null">
              and orders.store_id = #{params.storeId}
          </if>
          <if test = "params.channelId != null">
              and orders.channel_id = #{params.channelId}
          </if>
          <if test = "params.refundMethodList != null">
              and order_refund_payment.refund_payment_method_id in
              <foreach item="item" index="index" collection="params.refundMethodList" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <!-- 退款类型分为3种：全部退款、部分退款、异常退款 -->
          <if test = "params.refundTypeList != null and params.refundTypeList.size > 0">
              and (
                  <if test = "params.getRefundTypeIdList != null and params.getRefundTypeIdList.size > 0">
                      <!-- 1,3 是全部退款， 2,4 是部分退款 -->
                      order_refund.refund_type in
                      <foreach item="item" index="index" collection="params.refundTypeIdList" open="(" separator="," close=")">
                          #{item}
                      </foreach>
                  </if>
                  <choose>
                      <!-- 除了异常退款就是全部和部分，异常退款是单独判断，根据字符串 "异常退款" 来判断 -->
                      <when test="params.hasExceptionRefund()">
                          <!-- 如果存在前面的条件，则需要用or连接，因为这两者是或者的关系 -->
                          <if test = "params.getRefundTypeIdList != null and params.getRefundTypeIdList.size > 0">
                              or
                          </if>
                          order_refund.refund_type_alias = #{params.exceptionRefundType}
                      </when>
                      <!-- 1,3 是全部退款， 2,4 是部分退款 -->
                      <otherwise>
                          <!-- 当不需要查询异常退款时，需要将异常退款的类型排除，所以是并且的关系 -->
                          and order_refund.refund_type_alias != #{params.exceptionRefundType}
                      </otherwise>
                  </choose>
              )
          </if>

          <!-- 退款状态分为3种：超时、失败、成功 -->
          <if test = "params.refundStatus != null">
              <choose>
                  <!-- 查询退款超时的 -->
                  <when test="params.refundStatus.name() == 'REFUND_TIMEOUT'">
                      and order_refund_payment.refund_payment_type = 3
                      and order_refund_payment.is_pre_refund_success = true
                      and order_refund_payment.is_refund_success is null
                      and order_refund_payment.disabled = false
                  </when>
                  <!-- 查询退款失败的 -->
                  <when test="params.refundStatus.name() == 'REFUND_FAILED'">
                      and (order_refund_payment.is_pre_refund_success = false
                      or (order_refund_payment.is_pre_refund_success = true
                      and order_refund_payment.is_refund_success = false))
                  </when>
                  <!-- 查询退款成功的 -->
                  <otherwise>
                      and ((order_refund_payment.is_pre_refund_success = true
                      and order_refund_payment.is_refund_success = true
                      and order_refund_payment.disabled = false) or order_refund_payment.disabled is null)
                  </otherwise>
              </choose>
          </if>
          <if test = "params.startTime != null">
              and order_refund.create_at &gt;= #{params.startTime}
          </if>
          <if test = "params.endTime != null">
              and order_refund.create_at &lt; #{params.endTime}
          </if>
          <if test = "params.deviceNumber != null and params.deviceNumber != ''">
              and orders.device_number = #{params.deviceNumber}
          </if>
          <if test = "params.orderSourceList != null and params.orderSourceList.size > 0">
              and orders.order_source in
              <foreach item="item" index="index" collection="params.orderSourceList" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <!-- 根据操作人名称或电话 退款单号 订单号 会员名称或电话 模糊匹配 -->
          <if test = "params.keywords != null and params.keywords != ''">
              and (order_refund.operater_name like #{params.keywords} or order_refund.operater_phone like #{params.keywords} or
                  order_refund.refund_number like #{params.keywords} or orders.order_number like #{params.keywords} or
                  orders.member_name like #{params.keywords} or orders.member_phone like #{params.keywords}
                  <if test = "params.orderRefundNumberList != null and params.orderRefundNumberList.size > 0">
                      or order_refund.refund_number in
                      <foreach item="item" index="index" collection="params.orderRefundNumberList" open="(" separator="," close=")">
                          #{item}
                      </foreach>
                  </if>
              )
          </if>
          order by order_refund.create_at desc limit #{params.limit} offset #{params.offset}
    </select>

    <select id="queryOrderRefundExceptionNumber" resultType="java.lang.Integer">
        select count(distinct order_refund.id)
        from order_refund
                 left join orders on orders.order_number = order_refund.order_number
                 left join order_refund_payment on order_refund_payment.refund_number = order_refund.refund_number
        where
          order_refund.disabled = false
          and orders.disabled = false
          and order_refund_payment.disabled = false
          and order_refund_payment.refund_payment_type = 3
          and order_refund_payment.is_pre_refund_success = true
          and order_refund_payment.is_refund_success is null
          <if test = "params.storeId != null">
              and orders.store_id = #{params.storeId}
          </if>
          <if test = "params.channelId != null">
              and orders.channel_id = #{params.channelId}
          </if>
          <if test = "params.refundMethodList != null">
              and order_refund_payment.refund_payment_method_id in
              <foreach item="item" index="index" collection="params.refundMethodList" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <!-- 退款类型分为3种：全部退款、部分退款、异常退款 -->
          <if test = "params.refundTypeList != null and params.refundTypeList.size > 0">
              and (
              <if test = "params.getRefundTypeIdList != null and params.getRefundTypeIdList.size > 0">
                  <!-- 1,3 是全部退款， 2,4 是部分退款 -->
                  order_refund.refund_type in
                  <foreach item="item" index="index" collection="params.refundTypeIdList" open="(" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
              <choose>
                  <!-- 除了异常退款就是全部和部分，异常退款是单独判断，根据字符串 "异常退款" 来判断 -->
                  <when test="params.hasExceptionRefund()">
                      <!-- 如果存在前面的条件，则需要用or连接，因为这两者是或者的关系 -->
                      <if test = "params.getRefundTypeIdList != null and params.getRefundTypeIdList.size > 0">
                          or
                      </if>
                      order_refund.refund_type_alias = #{params.exceptionRefundType}
                  </when>
                  <!-- 1,3 是全部退款， 2,4 是部分退款 -->
                  <otherwise>
                      <!-- 当不需要查询异常退款时，需要将异常退款的类型排除，所以是并且的关系 -->
                      and order_refund.refund_type_alias != #{params.exceptionRefundType}
                  </otherwise>
              </choose>
              )
          </if>
          <if test = "params.startTime != null">
              and order_refund.create_at &gt;= #{params.startTime}
          </if>
          <if test = "params.endTime != null">
              and order_refund.create_at &lt; #{params.endTime}
          </if>
          <if test = "params.deviceNumber != null and params.deviceNumber != ''">
              and orders.device_number = #{params.deviceNumber}
          </if>
          <if test = "params.orderSourceList != null and params.orderSourceList.size > 0">
              and orders.order_source in
              <foreach item="item" index="index" collection="params.orderSourceList" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <!-- 根据操作人名称或电话 退款单号 订单号 会员名称或电话 模糊匹配 -->
          <if test = "params.keywords != null and params.keywords != ''">
              and (order_refund.operater_name like #{params.keywords} or order_refund.operater_phone like #{params.keywords} or
              order_refund.refund_number like #{params.keywords} or orders.order_number like #{params.keywords} or
              orders.member_name like #{params.keywords} or orders.member_phone like #{params.keywords})
          </if>
    </select>

    <select id="queryOrderRefundListCount" resultType="java.lang.Integer">
        select count(distinct order_refund.refund_number)
        from order_refund
            left join orders on orders.order_number = order_refund.order_number
            left join order_refund_payment on order_refund_payment.refund_number = order_refund.refund_number
        where
        order_refund.disabled = false
        and orders.disabled = false
        <if test = "params.storeId != null">
            and orders.store_id = #{params.storeId}
        </if>
        <if test = "params.channelId != null">
            and orders.channel_id = #{params.channelId}
        </if>
        <if test = "params.refundMethodList != null">
            and order_refund_payment.refund_payment_method_id in
            <foreach item="item" index="index" collection="params.refundMethodList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 退款类型分为3种：全部退款、部分退款、异常退款 -->
        <if test = "params.refundTypeList != null and params.refundTypeList.size > 0">
            and (
            <if test = "params.getRefundTypeIdList != null and params.getRefundTypeIdList.size > 0">
                <!-- 1,3 是全部退款， 2,4 是部分退款 -->
                order_refund.refund_type in
                <foreach item="item" index="index" collection="params.refundTypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <!-- 除了异常退款就是全部和部分，异常退款是单独判断，根据字符串 "异常退款" 来判断 -->
                <when test="params.hasExceptionRefund()">
                    <!-- 如果存在前面的条件，则需要用or连接，因为这两者是或者的关系 -->
                    <if test = "params.getRefundTypeIdList != null and params.getRefundTypeIdList.size > 0">
                        or
                    </if>
                    order_refund.refund_type_alias = #{params.exceptionRefundType}
                </when>
                <!-- 1,3 是全部退款， 2,4 是部分退款 -->
                <otherwise>
                    <!-- 当不需要查询异常退款时，需要将异常退款的类型排除，所以是并且的关系 -->
                    and order_refund.refund_type_alias != #{params.exceptionRefundType}
                </otherwise>
            </choose>
            )
        </if>

        <!-- 退款状态分为3种：超时、失败、成功 -->
        <if test = "params.refundStatus != null">
            <choose>
                <!-- 查询退款超时的 -->
                <when test="params.refundStatus.name() == 'REFUND_TIMEOUT'">
                    and order_refund_payment.refund_payment_type = 3
                    and order_refund_payment.is_pre_refund_success = true
                    and order_refund_payment.is_refund_success is null
                    and order_refund_payment.disabled = false
                </when>
                <!-- 查询退款失败的 -->
                <when test="params.refundStatus.name() == 'REFUND_FAILED'">
                    and (order_refund_payment.is_pre_refund_success = false
                    or (order_refund_payment.is_pre_refund_success = true
                    and order_refund_payment.is_refund_success = false))
                </when>
                <!-- 查询退款成功的 -->
                <otherwise>
                    and ((order_refund_payment.is_pre_refund_success = true
                    and order_refund_payment.is_refund_success = true
                    and order_refund_payment.disabled = false) or order_refund_payment.disabled is null)
                </otherwise>
            </choose>
        </if>
        <if test = "params.startTime != null">
            and order_refund.create_at &gt;= #{params.startTime}
        </if>
        <if test = "params.endTime != null">
            and order_refund.create_at &lt; #{params.endTime}
        </if>
        <if test = "params.deviceNumber != null and params.deviceNumber != ''">
            and orders.device_number = #{params.deviceNumber}
        </if>
        <if test = "params.orderSourceList != null and params.orderSourceList.size > 0">
            and orders.order_source in
            <foreach item="item" index="index" collection="params.orderSourceList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 根据操作人名称或电话 退款单号 订单号 会员名称或电话 模糊匹配 -->
        <if test = "params.keywords != null and params.keywords != ''">
            and (order_refund.operater_name like #{params.keywords} or order_refund.operater_phone like #{params.keywords} or
            order_refund.refund_number like #{params.keywords} or orders.order_number like #{params.keywords} or
            orders.member_name like #{params.keywords} or orders.member_phone like #{params.keywords}
            <if test = "params.orderRefundNumberList != null and params.orderRefundNumberList.size > 0">
                or order_refund.refund_number in
                <foreach item="item" index="index" collection="params.orderRefundNumberList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
    </select>

    <select id="queryOrderRefundNumberListByKeywordsForNameOrBarcode" resultType="java.lang.String">
        select order_refund_item.refund_number
        from order_refund_item
                 left join order_item on order_refund_item.order_item_id = order_item.record_id
        where order_item.goods_sale_name like #{keywords}
           or order_item.barcode like #{keywords}
    </select>
</mapper>

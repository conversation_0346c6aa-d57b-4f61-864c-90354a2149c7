<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.SaleCategoryAndStoreGoodsMapper">

    <select id="querySaleCategoryByStoreGoodsIdListAndChannelId"
            resultType="com.holderzone.holderpaasmdm.model.po.SaleCategoryAndStoreGoodsPO">
        select distinct relation.sale_category_id,
                        relation.store_goods_id
        from sale_category saleCategory
                 left join sale_category_and_store_goods relation on saleCategory.id = relation.sale_category_id
        where saleCategory.channel_id = #{channelId}
          and saleCategory.disabled = false
          and relation.store_goods_id in
          <foreach collection="storeGoodsIdList" item="item" open="(" separator="," close=")">
                #{item}
          </foreach>
          and relation.disabled = false
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.holderzone.holderpaasmdm.mapper.GoodsBrandMapper">

    <select id="queryGoodsBrandExtendListByIdList"
            resultType="com.holderzone.holderpaasmdm.model.po.GoodsBrandExtendPO">
        select gooodsBrand.id,
               gooodsBrand.name,
               gooodsBrand.logo,
               uploadFiles.url
        from goods_brand gooodsBrand
                 left join upload_files uploadFiles on gooodsBrand.logo = uploadFiles.id AND uploadFiles.disabled = false
        where gooodsBrand.id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and gooodsBrand.disabled = false
    </select>
</mapper>

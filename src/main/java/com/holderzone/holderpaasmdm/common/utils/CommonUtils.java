package com.holderzone.holderpaasmdm.common.utils;


import org.apache.commons.lang3.StringUtils;

/**
 * Description: 公共工具类
 * Author: 向超
 * Date: 2024/12/18 15:34
 */
public class CommonUtils {
    private CommonUtils() {
        // 私有构造函数
    }

    private static final String SYMBOL_PER_CENT = "%";

    /**
     * 组装SQL的模糊查询字符串
     *
     * @param value 模糊查询字符串
     * @return SQL模糊查询字符串
     */
    public static String assembleSqlLikeString(String value) {
        return SYMBOL_PER_CENT + value + SYMBOL_PER_CENT;
    }

    /**
     * 模糊匹配商品条码列表中是否包含关键字，商品条码暂时是 多个组合在一起的，例如：
     * "123456,789012,345678"
     *
     * @param barcodeList 模糊查询字符串
     * @param keywords    关键字
     * @return 过滤标志，true：包含关键字，false：不包含关键字
     */
    public static boolean filterLikeBarcodeListByKeywords(String barcodeList, String keywords) {
        if (StringUtils.isEmpty(barcodeList)) {
            return false;
        }
        if (StringUtils.isEmpty(keywords)) {
            return true;
        }
        String lowerCaseKeywords = keywords.toLowerCase();
        String lowerCaseBarcodeList = barcodeList.toLowerCase();
        for (String barcode : lowerCaseBarcodeList.split(",")) {
            if (StringUtils.isEmpty(barcode)) {
                continue;
            }
            if (barcode.contains(lowerCaseKeywords)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 精确匹配商品条码列表中是否包含关键字，商品条码暂时是 多个组合在一起的，例如：
     * "123456,789012,345678"
     *
     * @param barcodeList 模糊查询字符串
     * @param assignBarcode    关键字
     * @return 过滤标志，true：包含关键字，false：不包含关键字
     */
    public static boolean filterPreciseBarcodeListByKeywords(String barcodeList, String assignBarcode) {
        if (StringUtils.isEmpty(barcodeList) || StringUtils.isEmpty(assignBarcode)) {
            return false;
        }
        String lowerCaseAssignBarcode = assignBarcode.toLowerCase();
        String lowerCaseBarcodeList = barcodeList.toLowerCase();
        for (String barcode : lowerCaseBarcodeList.split(",")) {
            if (StringUtils.isEmpty(barcode)) {
                continue;
            }
            if (lowerCaseAssignBarcode.equals(barcode)) {
                return true;
            }
        }
        return false;
    }
}

package com.holderzone.holderpaasmdm.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import java.util.List;
import java.util.Map;

/**
 * Description: FastJson工具类
 *
 * <AUTHOR>
 * @date 2024/12/23
 * @since 1.8
 */
public class FastJsonUtils {

    private FastJsonUtils() {
        throw new IllegalStateException("FastJsonUtils class");
    }

    /**
     * 将Object转为String
     *
     * @param obj 要转换的对象
     * @return 转换后的JSON字符串
     */
    public static String objectToString(Object obj) {
        return JSON.toJSONString(obj);
    }

    /**
     * 将String转为Object
     *
     * @param jsonStr JSON字符串
     * @param clazz   目标对象的Class类型
     * @param <T>     目标对象的泛型类型
     * @return 转换后的对象
     */
    public static <T> T stringToObject(String jsonStr, Class<T> clazz) {
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     * 将Object转为Map
     *
     * @param obj 要转换的对象
     * @param <K> Map的键的泛型类型
     * @param <V> Map的值的泛型类型
     * @return 转换后的Map
     */
    public static <K, V> Map<K, V> objectToMap(Object obj) {
        return JSONObject.parseObject(JSON.toJSONString(obj), new TypeReference<Map<K, V>>() {});
    }

    /**
     * 将Map转为Object
     *
     * @param map   要转换的Map
     * @param clazz 目标对象的Class类型
     * @param <T>   目标对象的泛型类型
     * @return 转换后的对象
     */
    public static <T> T mapToObject(Map<?, ?> map, Class<T> clazz) {
        return JSON.parseObject(JSON.toJSONString(map), clazz);
    }

    /**
     * 将Object转为List
     *
     * @param obj   要转换的对象
     * @param clazz List中元素的Class类型
     * @param <T>   List中元素的泛型类型
     * @return 转换后的List
     */
    public static <T> List<T> objectToList(Object obj, Class<T> clazz) {
        return JSONObject.parseArray(JSON.toJSONString(obj), clazz);
    }

    /**
     * 将List转为JSONArray
     *
     * @param list 要转换的List
     * @param <T>  List中元素的泛型类型
     * @return 转换后的JSONArray
     */
    public static <T> JSONArray listToJSONArray(List<T> list) {
        return JSONArray.parseArray(JSON.toJSONString(list));
    }

    /**
     * 将JSONArray转为List
     *
     * @param array JSONArray对象
     * @param clazz List中元素的Class类型
     * @param <T>   List中元素的泛型类型
     * @return 转换后的List
     */
    public static <T> List<T> jsonArrayToList(JSONArray array, Class<T> clazz) {
        return JSONObject.parseArray(array.toString(), clazz);
    }

}

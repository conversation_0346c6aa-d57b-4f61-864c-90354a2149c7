package com.holderzone.holderpaasmdm.common.utils;

/**
 * desc 基于Redission实现的分布式ID生成器
 *
 * <AUTHOR>
 * @date 2024/12/19
 * @since 1.8
 */
import lombok.RequiredArgsConstructor;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DistributedIdGenerator {

    private final RedissonClient redissonClient;

    public long generateId(String name) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(name);
        return atomicLong.incrementAndGet();
    }
}

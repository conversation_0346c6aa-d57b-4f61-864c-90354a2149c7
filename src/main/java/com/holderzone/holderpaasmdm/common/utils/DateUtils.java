package com.holderzone.holderpaasmdm.common.utils;

import com.holderzone.holderpaasmdm.enumeraton.TimeShortcutSearchType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 时间工具类
 * Author: 向超
 * Date: 2024/11/15 15:34
 */
public class DateUtils {
    private DateUtils() {
        // 私有构造函数
    }

    private static final String TIME_FORMATTER_HH_MM = "HH:mm";

    /**
     * 获取当前日期
     *
     * @return LocalDate 当前日期
     */
    public static LocalDate getCurrentLocalDate() {
        return LocalDateTime.now().toLocalDate();
    }

    /**
     * 获取当前日期是属于周几，1表示周一，以此类推
     *
     * @return int 当前日期是属于周几，1表示周一，以此类推
     */
    public static int getCurrentDayOfWeek() {
        return getCurrentLocalDate().getDayOfWeek().getValue();
    }

    /**
     * 查询当前日期是属于周几，0表示周日，1表示周一，以此类推
     *
     * @return int 当前日期是属于周几，0表示周日，1表示周一，以此类推
     */
    public static int getCurrentDayOfWeekInChinese() {
        int dayOfWeek = getCurrentDayOfWeek();
        return dayOfWeek == 7 ? 0 : dayOfWeek;
    }

    /**
     * 获取昨天的日期
     *
     * @return LocalDate 昨天的日期
     */
    public static LocalDate getYesterdayLocalDate() {
        return getCurrentLocalDate().minusDays(1);
    }

    /**
     * 获取当前时间
     *
     * @return LocalTime 当前时间
     */
    public static LocalTime getCurrentLocalTime() {
        return LocalDateTime.now().toLocalTime();
    }

    /**
     * 获取当前时间，只保留小时和分钟
     *
     * @return LocalTime 当前时间（只保留小时和分钟）
     */
    public static LocalTime getCurrentLocalTimeWithoutSeconds() {
        LocalTime now = LocalDateTime.now().toLocalTime();
        return now.withSecond(0).withNano(0);
    }

    /**
     * 格式化LocalTime为HH:mm格式
     *
     * @param date LocalTime
     * @return String 格式化后的字符串
     */
    public static String formatLocalTimeToHHmm(LocalTime date) {
        return date.format(DateTimeFormatter.ofPattern(TIME_FORMATTER_HH_MM));
    }

    /**
     * 根据时间快捷方式获取开始时间和结束时间
     *
     * @param type 时间快捷方式
     * @return Map<String, Date> 开始时间和结束时间
     */
    public static Map<String, Date> getStartAndEndDateTimeByTimeShortcut(TimeShortcutSearchType type) {
        LocalDate now = LocalDate.now();
        LocalDateTime startTime;
        LocalDateTime endTime;

        switch (type) {
            case TODAY:
                // 开始时间为当天的 00:00:00
                startTime = now.atTime(LocalTime.MIDNIGHT);
                // 结束时间为当天的 23:59:59
                endTime = now.atTime(23, 59, 59);
                break;
            case YESTERDAY:
                // 昨天的 00:00:00
                startTime = now.minusDays(1).atTime(LocalTime.MIDNIGHT);
                // 昨天的 23:59:59
                endTime = now.minusDays(1).atTime(23, 59, 59);
                break;
            case THIS_WEEK:
                startTime = now.with(java.time.temporal.TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atTime(LocalTime.MIDNIGHT);
                endTime = now.with(java.time.temporal.TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY)).atTime(23, 59, 59);
                break;
            case THIS_MONTH:
                startTime = now.withDayOfMonth(1).atTime(LocalTime.MIDNIGHT);
                endTime = now.withDayOfMonth(now.lengthOfMonth()).atTime(23, 59, 59);
                break;
            default:
                throw new IllegalArgumentException("Unknown enum type: " + type);
        }

        // 使用系统默认时区转换为Date对象。
        Map<String, Date> result = new HashMap<>();
        result.put("startTime", Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));
        result.put("endTime", Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant()));

        return result;
    }

    /**
     * 获取前两年的当前时间
     *
     * @return Date 前两年的当前时间
     */
    public static Date getTwoYearsAgoDate() {
        LocalDateTime twoYearsAgo = LocalDateTime.now().minusYears(2);
        return Date.from(twoYearsAgo.atZone(ZoneId.systemDefault()).toInstant());
    }
}

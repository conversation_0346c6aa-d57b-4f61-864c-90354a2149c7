package com.holderzone.holderpaasmdm.common.exception;

import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpStatusCode;

/**
 * Description: 异常基类
 * Author: 向超
 * Date: 2024/11/13 15:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseException extends RuntimeException {
    private final HttpStatusCode status;
    private final ResponseCode code;
    private final String message;
    private final String devMessage;

    public BaseException(HttpStatusCode status, ResponseCode code) {
        this(status, code, null, null);
    }

    public BaseException(HttpStatusCode status, ResponseCode code, String message) {
        this(status, code, message, null);
    }

    public BaseException(HttpStatusCode status, ResponseCode code, String message, String devMessage) {
        super(message);
        this.status = status;
        this.code = code;
        this.message = message;
        this.devMessage = devMessage;
    }
}
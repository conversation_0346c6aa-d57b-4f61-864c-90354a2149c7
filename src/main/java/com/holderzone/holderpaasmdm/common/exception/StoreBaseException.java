package com.holderzone.holderpaasmdm.common.exception;

import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import org.springframework.http.HttpStatus;

/**
 * Description: 商品异常基类，需要提示给用户的异常继承此类
 * Author: 向超
 * Date: 2024/12/18 15:33
 */
public class StoreBaseException extends BaseException {
    public StoreBaseException(ResponseCode code, String message) {
        super(HttpStatus.OK ,code, message);
    }
}
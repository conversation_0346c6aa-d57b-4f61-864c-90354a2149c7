package com.holderzone.holderpaasmdm.common.exception.handler;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.model.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

import static com.holderzone.holderpaasmdm.enumeraton.ResponseCode.COMMON_INTERNAL_SERVER_ERROR;


/**
 * Description: 全局异常处理器
 * Author: 向超
 * Date: 2024/11/13 15:33
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {
    /**
     * 默认异常处理，所有未被处理的异常或者错误都会在本方法里被处理。
     *
     * @param ex 异常或者错误
     * @return Result 响应结果
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<Void>> handleUnexpectedExceptions(Exception ex) {
        log.error("Exception error occurs: ", ex);
        return new ResponseEntity<>(Result.fail(COMMON_INTERNAL_SERVER_ERROR.getValue()),
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 接口入参校验异常处理，所有未被处理的异常或者错误都会在本方法里被处理。
     *
     * @param ex 异常或者错误
     * @return Result 响应结果
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Result<Void>> handleBindExceptions(BindException ex) {
        log.error("BindException error occurs: ", ex);
        StringBuilder message = new StringBuilder();
        List<FieldError> fieldErrors = ex.getFieldErrors();
        fieldErrors.forEach(fieldError -> message.append(fieldError.getDefaultMessage()).append(";"));
        return new ResponseEntity<>(Result.fail(COMMON_INTERNAL_SERVER_ERROR.getValue(), message.toString()),
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 业务异常处理，所有继承自{@link BaseException}的异常对象都会在本方法里被处理。
     *
     * @param ex 异常或者错误
     * @return Result 响应结果
     */
    @ExceptionHandler(BaseException.class)
    public ResponseEntity<Result<Void>> handleBaseException(BaseException ex) {
        log.warn("Base exception occurs: ", ex);
        HttpStatusCode httpStatusCode = ex.getStatus();
        return new ResponseEntity<>(Result.fail(ex.getCode().getValue(), ex.getMessage()), httpStatusCode);
    }

    /**
     * 业务异常处理，所有继承自{@link BaseException}的异常对象都会在本方法里被处理。
     *
     * @param ex 异常或者错误
     * @return Result 响应结果
     */
    @ExceptionHandler(StoreBaseException.class)
    public ResponseEntity<Result<Void>> handleBaseException(StoreBaseException ex) {
        log.warn("Store Base exception occurs: ", ex);
        HttpStatusCode httpStatusCode = ex.getStatus();
        return new ResponseEntity<>(Result.fail(ex.getCode().getValue(), ex.getMessage()), httpStatusCode);
    }
}

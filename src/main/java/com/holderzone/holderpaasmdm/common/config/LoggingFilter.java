package com.holderzone.holderpaasmdm.common.config;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.ArrayUtil;
import com.holderzone.holderpaasmdm.common.utils.IdentityCreator;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.holderpaasmdm.common.constant.DataSourceConstants.COMPANY_ID_HEADER_KEY;

/**
 * 此过滤器将会打印请求和响应信息，以及处理器的处理时长
 *
 * <AUTHOR>
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
public class LoggingFilter implements Filter, HandlerInterceptor, WebMvcConfigurer {
    private static final String ACTUATOR_HEALTH_URI = "/actuator/health";

    private static final Logger log = LoggerFactory.getLogger(LoggingFilter.class);

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        ContentCachingRequestWrapper requestToUse = new ContentCachingRequestWrapper(httpRequest);
        ContentCachingResponseWrapper responseToUse = new ContentCachingResponseWrapper(httpResponse);

        String traceId = createTraceId();
        String tid = httpRequest.getHeader("tid");
        String companyId = httpRequest.getHeader(COMPANY_ID_HEADER_KEY);
        if (StringUtils.isNotBlank(tid)) {
            //请求方已有tid
            traceId = tid;
        }
        httpResponse.setHeader("X-Trace-ID", traceId);

        // 不用清理跟踪标识，因为如果后续有异常的情况下日志会缺失跟踪标识
        MDC.put("tid", traceId);
        long startTime = System.currentTimeMillis();
        String clientIP = getClientIP(httpRequest);
        String requestContent;
        String elapsedTime;
        try {
            chain.doFilter(requestToUse, responseToUse);
        } catch (Exception ex) {
            // 健康监测接口不记录日志
            if (!ACTUATOR_HEALTH_URI.equals(httpRequest.getRequestURI())) {
                requestContent = getRequestContent(requestToUse);
                elapsedTime = determineRequestSpeed(System.currentTimeMillis() - startTime);
                log.info("========================== URI:{} {}, companyId:{}, get-params:{}, IP:{}, post-params:{}," +
                                " executionTime：{}", httpRequest.getMethod(), httpRequest.getRequestURI(), companyId,
                        httpRequest.getQueryString(), clientIP, requestContent, elapsedTime);
            }
            throw ex;
        }
        String responseContent = getResponseContent(responseToUse);
        responseContent = limitContentLength(httpRequest.getRequestURI(), responseContent);
        String logResponseContent = limitResponseContent(responseContent);
        // 健康监测接口不记录日志
        if (!ACTUATOR_HEALTH_URI.equals(httpRequest.getRequestURI())) {
            requestContent = getRequestContent(requestToUse);
            elapsedTime = determineRequestSpeed(System.currentTimeMillis() - startTime);
            log.info("========================== URI:{} {}, companyId:{}, get-params:{}, IP:{}, post-params:{}," +
                            " response: {}, executionTime：{}", httpRequest.getMethod(), httpRequest.getRequestURI(),
                    companyId, httpRequest.getQueryString(), clientIP, requestContent, logResponseContent, elapsedTime);
        }
        responseToUse.copyBodyToResponse();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(this);
    }

    protected String createTraceId() {
        return IdentityCreator.createDefault();
    }

    protected String getRequestContent(ContentCachingRequestWrapper request) {
        if (StringUtils.containsIgnoreCase(request.getContentType(), "json")) {
            byte[] contentBytes = request.getContentAsByteArray();
            if (contentBytes.length > 0) {
                List<String> contentLines = IOUtils.readLines(new ByteArrayInputStream(contentBytes), StandardCharsets.UTF_8);
                return contentLines.stream().map(StringUtils::trim).collect(Collectors.joining(""));
            }
        }
        return "None";
    }

    protected String getResponseContent(ContentCachingResponseWrapper response) {
        if (StringUtils.containsIgnoreCase(response.getContentType(), "json")) {
            byte[] resultBytes = response.getContentAsByteArray();
            if (resultBytes.length > 0) {
                return new String(resultBytes, StandardCharsets.UTF_8);
            }
        }
        return "None";
    }

    private String limitContentLength(String uri, String responseContent) {
        //限制长度的接口
        List<String> limitInterfaceList = new ArrayList<>();
        limitInterfaceList.add("pageQuerySystemNotices");

        boolean limitLength = limitInterfaceList.stream().anyMatch(uri::contains);
        if (limitLength && StringUtils.isNotBlank(responseContent) && responseContent.length() > 2048) {
            responseContent = responseContent.substring(0, 2045) + "...";
        }
        return responseContent;
    }

    public static String getClientIP(HttpServletRequest request, String... otherHeaderNames) {
        String[] headers = new String[]{"X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"};
        if (ArrayUtil.isNotEmpty(otherHeaderNames)) {
            headers = ArrayUtil.addAll(headers, otherHeaderNames);
        }

        return getClientIpByHeader(request, headers);
    }

    public static String getClientIpByHeader(HttpServletRequest request, String... headerNames) {
        for (String header : headerNames) {
            String ip = request.getHeader(header);
            if (!NetUtil.isUnknown(ip)) {
                return NetUtil.getMultistageReverseProxyIp(ip);
            }
        }

        String ip = request.getRemoteAddr();
        return NetUtil.getMultistageReverseProxyIp(ip);
    }

    /**
     * 截取请求内容, 只保留1万字符
     *
     * @param responseContent 响应内容
     * @return 日志信息
     */
    public static String limitResponseContent(String responseContent) {
        if (responseContent.length() <= 10000) {
            return responseContent;
        } else {
            return "数据过长，只记录前10000个字符，实际长度为：" + responseContent.substring(0, 10000) + " ...";
        }
    }

    /**
     * 记录日志信息
     *
     * @param elapsedTime 执行时间
     * @return 日志信息
     */
    public static String determineRequestSpeed(long elapsedTime) {
        if (elapsedTime <= 500) {
            return "(fast) " + elapsedTime + "ms";
        } else if (elapsedTime <= 1000) {
            return "(normal) " + elapsedTime + "ms";
        } else if (elapsedTime <= 3000) {
            return "(slow) " + elapsedTime + "ms";
        } else if (elapsedTime <= 5000) {
            return "(verySlow) " + elapsedTime + "ms";
        } else {
            return "(unacceptable) " + elapsedTime + "ms";
        }
    }
}

package com.holderzone.holderpaasmdm.common.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * desc RestTemplate配置类
 *
 * <AUTHOR>
 * @date 2024/12/20
 * @since 1.8
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        builder.setConnectTimeout(Duration.ofMillis(500000));
        builder.setReadTimeout(Duration.ofMillis(500000));
        return builder.build();
    }

    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        // 设置退避策略：固定间隔时间重试
        retryTemplate.setBackOffPolicy(new FixedBackOffPolicy());
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        // 配置的重试次数
        retryPolicy.setMaxAttempts(3);
        retryTemplate.setRetryPolicy(retryPolicy);
        return retryTemplate;
    }

}

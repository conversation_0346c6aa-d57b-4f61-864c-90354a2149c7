package com.holderzone.holderpaasmdm.common.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * Description: Redisson客户端配置
 * Author: 向超
 * Date: 2024/12/13 15:36
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.data.redis.host}")
    private String redisHost;

    @Value("${spring.data.redis.port}")
    private int redisPort;

    @Value("${spring.data.redis.database}")
    private int database;

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
              .setAddress("redis://" + redisHost + ":" + redisPort)
              .setDatabase(database)
              .setConnectionPoolSize(64)
              .setConnectionMinimumIdleSize(10)
              .setConnectTimeout(2000)
                .setTimeout(2000)
                .setRetryAttempts(3).setRetryInterval(1000);
        return Redisson.create(config);
    }
}
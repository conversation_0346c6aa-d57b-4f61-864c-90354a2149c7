package com.holderzone.holderpaasmdm.common.context.impl;

import com.holderzone.holderpaasmdm.common.context.StoreGoodsAnalysisContext;
import com.holderzone.holderpaasmdm.converter.GoodsBrandConverter;
import com.holderzone.holderpaasmdm.converter.GoodsLabelConverter;
import com.holderzone.holderpaasmdm.converter.GoodsPictureConverter;
import com.holderzone.holderpaasmdm.converter.GoodsScaleCodeConverter;
import com.holderzone.holderpaasmdm.converter.SaleCategoryConverter;
import com.holderzone.holderpaasmdm.converter.StoreSalesProgramConverter;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpand;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpandQueryType;
import com.holderzone.holderpaasmdm.mapper.service.GoodsBrandMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsLabelMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsScaleCodeMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleCategoryAndStoreGoodsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleCategoryMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleUnitMeasurementMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsPriceMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramMapperService;
import com.holderzone.holderpaasmdm.mapper.service.UploadFilesMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsScaleCodePO;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryAndStoreGoodsPO;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryPO;
import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPricePO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import com.holderzone.holderpaasmdm.model.po.UploadFilesPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsBrandVO;
import com.holderzone.holderpaasmdm.model.vo.GoodsLabelVO;
import com.holderzone.holderpaasmdm.model.vo.GoodsPictureVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsBaseExtendVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsPrintLabelExtendVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * Description: 店铺商品分组组装上下文实现类
 * 这个类主要作用于 进行商品扩展字段查询 的场景
 * 是一个数据提供者，数据组装前置化
 * Author: 向超
 * Date: 2024/12/12 15:34
 */
@Slf4j
public class StoreGoodsAnalysisContextImpl implements StoreGoodsAnalysisContext {
    private final StoreGoodsExpandQueryType storeGoodsExpandQueryType;
    private final List<StoreGoodsExtendVO> storeGoodsExtendVOList;
    private final List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList;
    private final List<StoreGoodsExpand> storeGoodsExpandList;
    private final Integer storeId;
    private final Integer channelId;
    private final Integer scaleType;
    private final GoodsLabelConverter goodsLabelConverter;
    private final SaleCategoryConverter saleCategoryConverter;
    private final SaleCategoryAndStoreGoodsMapperService saleCategoryAndStoreGoodsMapperService;
    private final GoodsBrandMapperService goodsBrandMapperService;
    private final GoodsLabelMapperService goodsLabelMapperService;
    private final UploadFilesMapperService uploadFilesMapperService;
    private final SaleCategoryMapperService saleCategoryMapperService;
    private final GoodsBrandConverter goodsBrandConverter;
    private final GoodsPictureConverter goodsPictureConverter;
    private final GoodsScaleCodeConverter goodsScaleCodeConverter;
    private final StoreSalesProgramConverter storeSalesProgramConverter;
    private final SaleUnitMeasurementMapperService saleUnitMeasurementMapperService;
    private final GoodsScaleCodeMapperService goodsScaleCodeMapperService;
    private final StoreSalesProgramMapperService storeSalesProgramMapperService;
    private final StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService;
    private final StoreSalesProgramGoodsPriceMapperService storeSalesProgramGoodsPriceMapperService;

    /**
     * 扩展查询标识：需要查询品牌信息
     */
    private boolean needQueryBrand;

    /**
     * 扩展查询标识：需要查询封面信息
     */
    private boolean needQueryCover;

    /**
     * 扩展查询标识：需要查询标签信息
     */
    private boolean needQueryLabel;

    /**
     * 扩展查询标识：需要查询单位信息
     */
    private boolean needQueryUnit;

    /**
     * 扩展查询标识：需要查询销售分类信息
     */
    private boolean needQuerySaleCategory;

    /**
     * 扩展查询标识：需要查询秤码信息
     */
    private boolean needQueryGoodsScaleCode;

    /**
     * 扩展查询标识：需要查询销售方案时段信息
     */
    private boolean needQueryTimeSection;

    /**
     * 店铺品牌ID和品牌信息的映射
     */
    private Map<Integer, GoodsBrandVO> storeBrandIdAndInfoMap;

    /**
     * 店铺封面ID和封面信息的映射
     */
    private Map<Integer, GoodsPictureVO> storeCoverIdAndInfoMap;

    /**
     * 店铺标签ID和标签信息的映射
     */
    private Map<Integer, GoodsLabelVO> storeLabelIdAndInfoMap;

    /**
     * 店铺单位ID和单位名称的映射
     */
    private Map<Integer, String> storeUnitIdAndNameMap;

    /**
     * 店铺销售分组ID和销售分组信息的映射
     */
    private Map<Integer, List<StoreGoodsBaseExtendVO.SaleCategoryVO>> storeSaleCategoryIdAndInfoMap;

    /**
     * 店铺销售方案商品ID和销售方案时段信息的映射
     */
    private Map<Integer, List<StoreGoodsExtendVO.SectionVO>> storeSalesProgramGoodsIdAndInfoMap;

    /**
     * 店铺商品ID和商品秤码信息的映射
     */
    private Map<Integer, List<StoreGoodsBaseExtendVO.GoodsScaleCodeVO>> storeGoodsIdAndGoodsScaleCodeVOInfoMap;


    public static class Builder {
        // 构建器属性
        private StoreGoodsExpandQueryType storeGoodsExpandQueryType;
        private List<StoreGoodsExtendVO> storeGoodsExtendVOList;
        private List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList;
        private List<StoreGoodsExpand> storeGoodsExpandList = Collections.emptyList();
        private Integer storeId;
        private Integer channelId;
        private Integer scaleType;
        private GoodsLabelConverter goodsLabelConverter;
        private SaleCategoryConverter saleCategoryConverter;
        private SaleCategoryAndStoreGoodsMapperService saleCategoryAndStoreGoodsMapperService;
        private GoodsBrandMapperService goodsBrandMapperService;
        private GoodsLabelMapperService goodsLabelMapperService;
        private UploadFilesMapperService uploadFilesMapperService;
        private SaleCategoryMapperService saleCategoryMapperService;
        private GoodsBrandConverter goodsBrandConverter;
        private GoodsPictureConverter goodsPictureConverter;
        private GoodsScaleCodeConverter goodsScaleCodeConverter;
        private StoreSalesProgramConverter storeSalesProgramConverter;
        private SaleUnitMeasurementMapperService saleUnitMeasurementMapperService;
        private GoodsScaleCodeMapperService goodsScaleCodeMapperService;
        private StoreSalesProgramMapperService storeSalesProgramMapperService;
        private StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService;
        private StoreSalesProgramGoodsPriceMapperService storeSalesProgramGoodsPriceMapperService;

        // 构建器方法 兼容初始化方法能在构造阶段调用
        public Builder setStoreGoodsExpandQueryType(StoreGoodsExpandQueryType storeGoodsExpandQueryType) {
            this.storeGoodsExpandQueryType = storeGoodsExpandQueryType;
            return this;
        }

        public Builder setStoreGoodsExtendVOList(List<StoreGoodsExtendVO> storeGoodsExtendVOList) {
            this.storeGoodsExtendVOList = storeGoodsExtendVOList;
            this.storeGoodsPrintLabelExtendVOList = Collections.emptyList();
            return this;
        }

        public Builder setStoreGoodsPrintLabelExtendVOList(List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList) {
            this.storeGoodsExtendVOList = Collections.emptyList();
            this.storeGoodsPrintLabelExtendVOList = storeGoodsPrintLabelExtendVOList;
            return this;
        }

        public Builder setStoreGoodsExpandList(List<StoreGoodsExpand> storeGoodsExpandList) {
            this.storeGoodsExpandList = storeGoodsExpandList == null ? Collections.emptyList() : storeGoodsExpandList;
            return this;
        }

        public Builder setStoreId(Integer storeId) {
            this.storeId = storeId;
            return this;
        }

        public Builder setChannelId(Integer channelId) {
            this.channelId = channelId;
            return this;
        }

        public Builder setScaleType(Integer scaleType) {
            this.scaleType = scaleType;
            return this;
        }

        public Builder setGoodsLabelConverter(GoodsLabelConverter goodsLabelConverter) {
            this.goodsLabelConverter = goodsLabelConverter;
            return this;
        }

        public Builder setSaleCategoryConverter(SaleCategoryConverter saleCategoryConverter) {
            this.saleCategoryConverter = saleCategoryConverter;
            return this;
        }

        public Builder setSaleCategoryAndStoreGoodsMapperService(SaleCategoryAndStoreGoodsMapperService saleCategoryAndStoreGoodsMapperService) {
            this.saleCategoryAndStoreGoodsMapperService = saleCategoryAndStoreGoodsMapperService;
            return this;
        }

        public Builder setGoodsBrandMapperService(GoodsBrandMapperService goodsBrandMapperService) {
            this.goodsBrandMapperService = goodsBrandMapperService;
            return this;
        }

        public Builder setGoodsLabelMapperService(GoodsLabelMapperService goodsLabelMapperService) {
            this.goodsLabelMapperService = goodsLabelMapperService;
            return this;
        }

        public Builder setUploadFilesMapperService(UploadFilesMapperService uploadFilesMapperService) {
            this.uploadFilesMapperService = uploadFilesMapperService;
            return this;
        }

        public Builder setSaleCategoryMapperService(SaleCategoryMapperService saleCategoryMapperService) {
            this.saleCategoryMapperService = saleCategoryMapperService;
            return this;
        }

        public Builder setGoodsBrandConverter(GoodsBrandConverter goodsBrandConverter) {
            this.goodsBrandConverter = goodsBrandConverter;
            return this;
        }

        public Builder setGoodsPictureConverter(GoodsPictureConverter goodsPictureConverter) {
            this.goodsPictureConverter = goodsPictureConverter;
            return this;
        }

        public Builder setGoodsScaleCodeConverter(GoodsScaleCodeConverter goodsScaleCodeConverter) {
            this.goodsScaleCodeConverter = goodsScaleCodeConverter;
            return this;
        }

        public Builder setStoreSalesProgramConverter(StoreSalesProgramConverter storeSalesProgramConverter) {
            this.storeSalesProgramConverter = storeSalesProgramConverter;
            return this;
        }

        public Builder setSaleUnitMeasurementMapperService(SaleUnitMeasurementMapperService saleUnitMeasurementMapperService) {
            this.saleUnitMeasurementMapperService = saleUnitMeasurementMapperService;
            return this;
        }

        public Builder setGoodsScaleCodeMapperService(GoodsScaleCodeMapperService goodsScaleCodeMapperService) {
            this.goodsScaleCodeMapperService = goodsScaleCodeMapperService;
            return this;
        }

        public Builder setStoreSalesProgramMapperService(StoreSalesProgramMapperService storeSalesProgramMapperService) {
            this.storeSalesProgramMapperService = storeSalesProgramMapperService;
            return this;
        }

        public Builder setStoreSalesProgramGoodsMapperService(StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService) {
            this.storeSalesProgramGoodsMapperService = storeSalesProgramGoodsMapperService;
            return this;
        }

        public Builder setStoreSalesProgramGoodsPriceMapperService(StoreSalesProgramGoodsPriceMapperService storeSalesProgramGoodsPriceMapperService) {
            this.storeSalesProgramGoodsPriceMapperService = storeSalesProgramGoodsPriceMapperService;
            return this;
        }

        public StoreGoodsAnalysisContextImpl build() {
            return new StoreGoodsAnalysisContextImpl(this);
        }
    }

    private StoreGoodsAnalysisContextImpl(Builder builder) {
        this.storeGoodsExpandQueryType = builder.storeGoodsExpandQueryType;
        this.storeGoodsExtendVOList = builder.storeGoodsExtendVOList;
        this.storeGoodsPrintLabelExtendVOList = builder.storeGoodsPrintLabelExtendVOList;
        this.storeGoodsExpandList = builder.storeGoodsExpandList;
        this.storeId = builder.storeId;
        this.channelId = builder.channelId;
        this.scaleType = builder.scaleType;
        this.goodsLabelConverter = builder.goodsLabelConverter;
        this.saleCategoryConverter = builder.saleCategoryConverter;
        this.saleCategoryAndStoreGoodsMapperService = builder.saleCategoryAndStoreGoodsMapperService;
        this.goodsBrandMapperService = builder.goodsBrandMapperService;
        this.goodsLabelMapperService = builder.goodsLabelMapperService;
        this.uploadFilesMapperService = builder.uploadFilesMapperService;
        this.saleCategoryMapperService = builder.saleCategoryMapperService;
        this.goodsBrandConverter = builder.goodsBrandConverter;
        this.goodsPictureConverter = builder.goodsPictureConverter;
        this.goodsScaleCodeConverter = builder.goodsScaleCodeConverter;
        this.storeSalesProgramConverter = builder.storeSalesProgramConverter;
        this.saleUnitMeasurementMapperService = builder.saleUnitMeasurementMapperService;
        this.goodsScaleCodeMapperService = builder.goodsScaleCodeMapperService;
        this.storeSalesProgramMapperService = builder.storeSalesProgramMapperService;
        this.storeSalesProgramGoodsMapperService = builder.storeSalesProgramGoodsMapperService;
        this.storeSalesProgramGoodsPriceMapperService = builder.storeSalesProgramGoodsPriceMapperService;
        init();
    }

    public static Builder builder() {
        return new Builder();
    }


    /**
     * 初始化上下文
     */
    public void init() {
        Set<Integer> storeBrandIdList = new HashSet<>();
        Set<Integer> storeCoverIdList = new HashSet<>();
        Set<Integer> storeLabelIdList = new HashSet<>();
        Set<Integer> storeUnitIdList = new HashSet<>();
        Set<Integer> storeGoodsIdList = new HashSet<>();
        Set<Integer> storeSalesProgramGoodsIdList = new HashSet<>();
        needQueryBrand = storeGoodsExpandList.contains(StoreGoodsExpand.BRAND);
        needQueryCover = storeGoodsExpandList.contains(StoreGoodsExpand.COVER);
        needQueryLabel = storeGoodsExpandList.contains(StoreGoodsExpand.LABEL);
        needQueryUnit = storeGoodsExpandList.contains(StoreGoodsExpand.UNIT);
        needQuerySaleCategory = storeGoodsExpandList.contains(StoreGoodsExpand.SALE_CATEGORY);
        needQueryGoodsScaleCode = storeGoodsExpandList.contains(StoreGoodsExpand.GOODS_SCALE_CODE);
        needQueryTimeSection = storeGoodsExpandList.contains(StoreGoodsExpand.SECTION);
        if (storeGoodsExpandQueryType == StoreGoodsExpandQueryType.STORE_GOODS_SINGLE_TIMESLOT_SALES_SCHEME) {
            assembleForStoreGoodsSingleTimeslotSalesScheme(storeLabelIdList, storeUnitIdList, storeGoodsIdList,
                    storeCoverIdList, storeSalesProgramGoodsIdList);
        } else if (storeGoodsExpandQueryType == StoreGoodsExpandQueryType.GROUP_BY_STORE_GOODS_MULTI_PERIOD) {
            assembleForGroupByStoreGoodsMultiPeriod(storeBrandIdList, storeUnitIdList, storeGoodsIdList, storeCoverIdList, storeLabelIdList);
        }
        storeBrandIdAndInfoMap = getStoreBrandIdAndInfoMap(storeBrandIdList, needQueryBrand);
        storeCoverIdAndInfoMap = getStoreCoverIdAndInfoMap(storeCoverIdList, needQueryCover);
        storeLabelIdAndInfoMap = getStoreLabelIdAndInfoMap(storeLabelIdList, needQueryLabel);
        storeUnitIdAndNameMap = getStoreUnitIdAndNameMap(storeUnitIdList, needQueryUnit);
        storeSaleCategoryIdAndInfoMap =
                getStoreSaleCategoryIdAndInfoMap(storeGoodsIdList, needQuerySaleCategory, storeId, channelId);
        storeGoodsIdAndGoodsScaleCodeVOInfoMap =
                getStoreGoodsIdAndGoodsScaleCodeVOInfoMap(storeGoodsIdList, needQueryGoodsScaleCode, scaleType);
        storeSalesProgramGoodsIdAndInfoMap = getStoreSalesProgramGoodsIdAndInfoMap(storeSalesProgramGoodsIdList,
                needQueryTimeSection);
    }

    /**
     * 组装查询条件(按店铺商品分组，多时段)
     *
     * @param storeBrandIdList 品牌ID列表
     * @param storeUnitIdList  单位ID列表
     * @param storeGoodsIdList 商品ID列表
     * @param storeCoverIdList 封面ID列表
     * @param storeLabelIdList 标签ID列表
     */
    private void assembleForGroupByStoreGoodsMultiPeriod(Set<Integer> storeBrandIdList,
                                                         Set<Integer> storeUnitIdList,
                                                         Set<Integer> storeGoodsIdList,
                                                         Set<Integer> storeCoverIdList,
                                                         Set<Integer> storeLabelIdList) {
        for (StoreGoodsPrintLabelExtendVO storeGoodsPrintLabelExtendVO : storeGoodsPrintLabelExtendVOList) {
            assembleQueryCondition(storeGoodsPrintLabelExtendVO, storeBrandIdList,
                    storeUnitIdList, storeGoodsIdList);
            List<StoreGoodsPrintLabelExtendVO.StoreSalesProgramGoodVO> goodList = storeGoodsPrintLabelExtendVO.getStoreSalesProgramGoodList();
            if (Objects.isNull(goodList)) {
                addCover(storeCoverIdList, storeGoodsPrintLabelExtendVO, null);
                addLabel(storeLabelIdList, storeGoodsPrintLabelExtendVO, null);
            } else {
                for (StoreGoodsPrintLabelExtendVO.StoreSalesProgramGoodVO storeSalesProgramGoodVO : storeGoodsPrintLabelExtendVO.getStoreSalesProgramGoodList()) {
                    addCover(storeCoverIdList, null, storeSalesProgramGoodVO);
                    addLabel(storeLabelIdList, null, storeSalesProgramGoodVO);
                }
            }
        }
    }

    /**
     * 新增商品头像
     * @param storeCoverIdList id集合
     * @param storeGoodsPrintLabelExtendVO 商品列表
     * @param storeSalesProgramGoodVO 商品列表
     */
    private void addCover(Set<Integer> storeCoverIdList, StoreGoodsPrintLabelExtendVO storeGoodsPrintLabelExtendVO,
                          StoreGoodsPrintLabelExtendVO.StoreSalesProgramGoodVO storeSalesProgramGoodVO) {
        if (needQueryCover) {
            if (Objects.nonNull(storeSalesProgramGoodVO) && Objects.nonNull(storeSalesProgramGoodVO.getCoverPicture())) {
                storeCoverIdList.add(storeSalesProgramGoodVO.getCoverPicture());
            }
            if (Objects.nonNull(storeGoodsPrintLabelExtendVO) && Objects.nonNull(storeGoodsPrintLabelExtendVO.getCoverPicture())) {
                storeCoverIdList.add(storeGoodsPrintLabelExtendVO.getCoverPicture());
            }
        }
    }

    /**
     * 新增标签信息
     * @param storeLabelIdList id集合
     * @param storeGoodsPrintLabelExtendVO 商品列表
     * @param storeSalesProgramGoodVO 商品列表
     */
    private void addLabel(Set<Integer> storeLabelIdList, StoreGoodsPrintLabelExtendVO storeGoodsPrintLabelExtendVO,
                          StoreGoodsPrintLabelExtendVO.StoreSalesProgramGoodVO storeSalesProgramGoodVO) {
        if (needQueryLabel) {
            if (Objects.nonNull(storeSalesProgramGoodVO) && Objects.nonNull(storeSalesProgramGoodVO.getLabel())) {
                storeLabelIdList.add(storeSalesProgramGoodVO.getLabel());
            }
            if (Objects.nonNull(storeGoodsPrintLabelExtendVO) && Objects.nonNull(storeGoodsPrintLabelExtendVO.getLabel())) {
                storeLabelIdList.add(storeGoodsPrintLabelExtendVO.getLabel());
            }
        }
    }

    /**
     * 组装查询条件(单时间段销售方案)
     *
     * @param storeLabelIdList             标签ID列表
     * @param storeUnitIdList              单位ID列表
     * @param storeGoodsIdList             商品ID列表
     * @param storeCoverIdList             封面ID列表
     * @param storeSalesProgramGoodsIdList 店铺销售方案商品ID列表
     */
    private void assembleForStoreGoodsSingleTimeslotSalesScheme(Set<Integer> storeLabelIdList,
                                                                Set<Integer> storeUnitIdList,
                                                                Set<Integer> storeGoodsIdList,
                                                                Set<Integer> storeCoverIdList,
                                                                Set<Integer> storeSalesProgramGoodsIdList) {
        for (StoreGoodsExtendVO storeGoodsExtendVO : storeGoodsExtendVOList) {
            assembleQueryCondition(storeGoodsExtendVO, storeLabelIdList,
                    storeUnitIdList, storeGoodsIdList);
            if (needQueryCover && storeGoodsExtendVO.getCover() != null) {
                storeCoverIdList.add(storeGoodsExtendVO.getCover());
            }
            if (needQueryLabel && storeGoodsExtendVO.getGoodsLabel() != null) {
                storeLabelIdList.add(storeGoodsExtendVO.getGoodsLabel());
            }
            // 查询时段信息，目前只支持商品基础扩展信息查询
            if (needQueryTimeSection) {
                storeSalesProgramGoodsIdList.add(storeGoodsExtendVO.getStoreSalesProgramGoodsId());
            }
        }
    }

    /**
     * 组装查询条件
     *
     * @param storeGoodsExtendVO 商品扩展信息
     * @param storeBrandIdList   品牌ID列表
     * @param storeUnitIdList    单位ID列表
     * @param storeGoodsIdList   商品ID列表
     */
    private void assembleQueryCondition(StoreGoodsBaseExtendVO storeGoodsExtendVO,
                                        Set<Integer> storeBrandIdList,
                                        Set<Integer> storeUnitIdList,
                                        Set<Integer> storeGoodsIdList) {
        if (needQueryBrand && storeGoodsExtendVO.getBrand() != null) {
            storeBrandIdList.add(storeGoodsExtendVO.getBrand());
        }
        if (needQueryUnit && storeGoodsExtendVO.getGoodsUnit() != null) {
            storeUnitIdList.add(storeGoodsExtendVO.getGoodsUnit());
        }
        if (needQuerySaleCategory || needQueryGoodsScaleCode) {
            storeGoodsIdList.add(storeGoodsExtendVO.getId());
        }
    }


    /**
     * 获取店铺销售方案商品ID和销售方案时段信息的映射
     *
     * @param storeSalesProgramGoodsIdList 店铺销售方案商品ID列表
     * @param needQueryTimeSection         是否需要查询销售方案时段信息
     * @return 店铺销售方案商品ID和销售方案时段信息的映射
     */
    private Map<Integer, List<StoreGoodsExtendVO.SectionVO>> getStoreSalesProgramGoodsIdAndInfoMap(
            Set<Integer> storeSalesProgramGoodsIdList, boolean needQueryTimeSection) {
        if (!needQueryTimeSection || CollectionUtils.isEmpty(storeSalesProgramGoodsIdList)) {
            return Collections.emptyMap();
        }
        // 查询店铺销售方案信息
        Map<Integer, StoreSalesProgramPO> storeSalesProgramIdAndInfoMap =
                storeSalesProgramMapperService.queryStoreSalesProgramByStoreSalesProgramGoodsIdList(storeSalesProgramGoodsIdList)
                        .stream().collect(Collectors.toMap(StoreSalesProgramPO::getId, Function.identity()));
        // 查询店铺销售方案商品信息
        Map<Integer, List<StoreGoodsExtendVO.SectionVO>> storeSalesProgramGoodsIdAndInfoMapResult = storeSalesProgramGoodsMapperService.lambdaQuery()
                .select(StoreSalesProgramGoodsPO::getId, StoreSalesProgramGoodsPO::getStoreSalesProgramId)
                .in(StoreSalesProgramGoodsPO::getId, storeSalesProgramGoodsIdList)
                .list().stream().collect(Collectors.toMap(
                        StoreSalesProgramGoodsPO::getGoodsId,
                        storeSalesProgramGoodsPO -> toStoreGoodsExtendVOSectionVOList(storeSalesProgramGoodsPO.getStoreSalesProgramId(), storeSalesProgramIdAndInfoMap)
                ));
        // 查询店铺销售方案时段信息
        Map<Integer, Map<Integer, StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndSerialNumberAndPriceMap =
                storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                        .in(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, storeSalesProgramGoodsIdList)
                        .list().stream()
                        .collect(Collectors.groupingBy(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId,
                                Collectors.toMap(StoreSalesProgramGoodsPricePO::getSerialNumber, Function.identity())));
        for (Map.Entry<Integer, List<StoreGoodsExtendVO.SectionVO>> item : storeSalesProgramGoodsIdAndInfoMapResult.entrySet()) {
            Map<Integer, StoreSalesProgramGoodsPricePO> serialNumberAndPriceListMap = storeSalesProgramGoodsIdAndSerialNumberAndPriceMap.get(item.getKey());
            if (serialNumberAndPriceListMap == null) {
                continue;
            }
            for (StoreGoodsExtendVO.SectionVO sectionVO : item.getValue()) {
                sectionVO.setSellingPrice(serialNumberAndPriceListMap.get(sectionVO.getSerialNumber()).getTimeSectionPriceCompute());
            }
        }
        return storeSalesProgramGoodsIdAndInfoMap;
    }

    /**
     * 将店铺销售方案ID转换为店铺商品扩展信息中的销售方案信息
     *
     * @param storeSalesProgramId           店铺销售方案ID
     * @param storeSalesProgramIdAndInfoMap 店铺销售方案ID和信息的映射
     * @return 店铺商品扩展信息中的销售方案信息
     */
    private List<StoreGoodsExtendVO.SectionVO> toStoreGoodsExtendVOSectionVOList(Integer storeSalesProgramId,
                                                                                 Map<Integer, StoreSalesProgramPO> storeSalesProgramIdAndInfoMap) {
        StoreSalesProgramPO storeSalesProgramPO = storeSalesProgramIdAndInfoMap.get(storeSalesProgramId);
        if (storeSalesProgramPO == null) {
            log.warn("店铺销售方案信息不存在，storeSalesProgramId:{}", storeSalesProgramId);
            return Collections.emptyList();
        }
        return storeSalesProgramConverter.toStoreGoodsExtendVOSectionVOList(storeSalesProgramPO.getTimeSection());
    }

    /**
     * 获取店铺商品ID和商品秤码信息的映射
     *
     * @param storeGoodsIdList        商品ID列表
     * @param needQueryGoodsScaleCode 是否需要查询秤码信息
     * @param scaleType               秤码类型
     * @return 商品ID和商品秤码信息的映射
     */
    private Map<Integer, List<StoreGoodsBaseExtendVO.GoodsScaleCodeVO>> getStoreGoodsIdAndGoodsScaleCodeVOInfoMap(Set<Integer> storeGoodsIdList,
                                                                                                                  boolean needQueryGoodsScaleCode,
                                                                                                                  Integer scaleType) {
        if (!needQueryGoodsScaleCode || CollectionUtils.isEmpty(storeGoodsIdList)) {
            return Collections.emptyMap();
        }
        return goodsScaleCodeConverter.toStoreGoodsScaleCodeVOList(
                goodsScaleCodeMapperService.lambdaQuery()
                        .eq(GoodsScaleCodePO::getScaleType, scaleType)
                        .in(GoodsScaleCodePO::getGoodsId, storeGoodsIdList)
                        .list()).stream().collect(Collectors.groupingBy(StoreGoodsBaseExtendVO.GoodsScaleCodeVO::getGoodsId));
    }

    /**
     * 组装商品ID和销售信息集合的映射
     *
     * @param storeGoodsIdList      商品ID列表
     * @param needQuerySaleCategory 是否需要查询销售分类信息
     * @param storeId               店铺ID
     * @param channelId             渠道ID
     * @return 商品ID和销售信息集合的映射
     */
    private Map<Integer, List<StoreGoodsBaseExtendVO.SaleCategoryVO>> getStoreSaleCategoryIdAndInfoMap(
            Set<Integer> storeGoodsIdList, boolean needQuerySaleCategory, Integer storeId, Integer channelId) {
        if (!needQuerySaleCategory || CollectionUtils.isEmpty(storeGoodsIdList)) {
            return Collections.emptyMap();
        }
        Map<Integer, StoreGoodsBaseExtendVO.SaleCategoryVO> saleCategoryIdAndInfoMap =
                saleCategoryConverter.toStoreGoodsExtendVOSaleCategoryVOList(
                        saleCategoryMapperService.lambdaQuery().eq(SaleCategoryPO::getStoreId, storeId)
                                .eq(SaleCategoryPO::getChannelId, channelId).list()).stream().collect(
                        Collectors.toMap(StoreGoodsBaseExtendVO.SaleCategoryVO::getId, Function.identity()));
        return saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                .in(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, storeGoodsIdList)
                .list()
                .stream()
                .filter(po -> saleCategoryIdAndInfoMap.containsKey(po.getSaleCategoryId()))
                .collect(Collectors.groupingBy(
                        SaleCategoryAndStoreGoodsPO::getStoreGoodsId,
                        Collectors.mapping(
                                po -> saleCategoryIdAndInfoMap.get(po.getSaleCategoryId()),
                                Collectors.toList()
                        )
                ));
    }

    /**
     * 获取店铺标签ID和标签信息的映射
     *
     * @param storeLabelIdList 店铺标签ID列表
     * @param needQueryLabel   是否需要查询标签信息
     * @return 店铺标签ID和标签信息的映射
     */
    private Map<Integer, GoodsLabelVO> getStoreLabelIdAndInfoMap(Set<Integer> storeLabelIdList, boolean needQueryLabel) {
        if (!needQueryLabel || CollectionUtils.isEmpty(storeLabelIdList)) {
            return Collections.emptyMap();
        }
        return goodsLabelConverter.toGoodsLabelVOList(
                        goodsLabelMapperService.listByIds(storeLabelIdList))
                .stream().collect(Collectors.toMap(GoodsLabelVO::getId, Function.identity()));
    }

    /**
     * 获取店铺品牌ID和品牌信息的映射
     *
     * @param storeUnitIdList 店铺品牌ID列表
     * @param needQueryUnit   是否需要查询品牌信息
     * @return 店铺品牌ID和品牌信息的映射
     */
    private Map<Integer, String> getStoreUnitIdAndNameMap(Set<Integer> storeUnitIdList, boolean needQueryUnit) {
        if (!needQueryUnit || CollectionUtils.isEmpty(storeUnitIdList)) {
            return Collections.emptyMap();
        }
        return saleUnitMeasurementMapperService.lambdaQuery()
                .select(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName)
                .in(SaleUnitMeasurementPO::getId, storeUnitIdList)
                .list().stream().collect(Collectors.toMap(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName));
    }

    /**
     * 获取店铺封面ID和封面信息的映射
     *
     * @param storeCoverIdList 店铺封面图片ID列表
     * @param needQueryCover   是否需要查询封面信息
     * @return 店铺封面ID和封面信息的映射
     */
    private Map<Integer, GoodsPictureVO> getStoreCoverIdAndInfoMap(Set<Integer> storeCoverIdList, boolean needQueryCover) {
        if (!needQueryCover || CollectionUtils.isEmpty(storeCoverIdList)) {
            return Collections.emptyMap();
        }
        return goodsPictureConverter.toGoodsPictureVOList(
                uploadFilesMapperService.lambdaQuery()
                        .in(UploadFilesPO::getId, storeCoverIdList)
                        .list()).stream().collect(Collectors.toMap(GoodsPictureVO::getId, Function.identity()));
    }

    /**
     * 获取店铺品牌ID和品牌信息的映射
     *
     * @param storeBrandIdList 店铺品牌ID列表
     * @param needQueryBrand   是否需要查询品牌信息
     * @return 店铺品牌ID和品牌信息的映射
     */
    private Map<Integer, GoodsBrandVO> getStoreBrandIdAndInfoMap(Set<Integer> storeBrandIdList, boolean needQueryBrand) {
        if (!needQueryBrand || CollectionUtils.isEmpty(storeBrandIdList)) {
            return Collections.emptyMap();
        }
        return goodsBrandConverter.toGoodsBrandVOList(
                        goodsBrandMapperService.queryGoodsBrandExtendListByIdList(storeBrandIdList))
                .stream().collect(Collectors.toMap(GoodsBrandVO::getId, Function.identity()));
    }

    @Override
    public StoreGoodsExpandQueryType getStoreGoodsExpandQueryType() {
        return this.storeGoodsExpandQueryType;
    }

    @Override
    public boolean getNeedQueryBrandFlag() {
        return this.needQueryBrand;
    }

    @Override
    public boolean getNeedQueryCoverFlag() {
        return this.needQueryCover;
    }

    @Override
    public boolean getNeedQueryLabelFlag() {
        return this.needQueryLabel;
    }

    @Override
    public boolean getNeedQueryUnitFlag() {
        return this.needQueryUnit;
    }

    @Override
    public boolean getNeedQuerySaleCategoryFlag() {
        return this.needQuerySaleCategory;
    }

    @Override
    public boolean getNeedQueryGoodsScaleCodeFlag() {
        return this.needQueryGoodsScaleCode;
    }

    @Override
    public boolean getNeedQueryTimeSectionFlag() {
        return this.needQueryTimeSection;
    }

    @Override
    public Map<Integer, GoodsBrandVO> getStoreBrandIdAndInfoMap() {
        return this.storeBrandIdAndInfoMap;
    }

    @Override
    public Map<Integer, GoodsPictureVO> getStoreCoverIdAndInfoMap() {
        return this.storeCoverIdAndInfoMap;
    }

    @Override
    public Map<Integer, GoodsLabelVO> getStoreLabelIdAndInfoMap() {
        return this.storeLabelIdAndInfoMap;
    }

    @Override
    public Map<Integer, String> getStoreUnitIdAndNameMap() {
        return this.storeUnitIdAndNameMap;
    }

    @Override
    public Map<Integer, List<StoreGoodsBaseExtendVO.SaleCategoryVO>> getStoreSaleCategoryIdAndInfoMap() {
        return this.storeSaleCategoryIdAndInfoMap;
    }

    @Override
    public Map<Integer, List<StoreGoodsExtendVO.SectionVO>> getStoreSalesProgramGoodsIdAndTimeSectionInfoMap() {
        return this.storeSalesProgramGoodsIdAndInfoMap;
    }

    @Override
    public Map<Integer, List<StoreGoodsBaseExtendVO.GoodsScaleCodeVO>> getStoreGoodsIdAndGoodsScaleCodeVOInfoMap() {
        return this.storeGoodsIdAndGoodsScaleCodeVOInfoMap;
    }
}

package com.holderzone.holderpaasmdm.common.context;


import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpandQueryType;
import com.holderzone.holderpaasmdm.model.vo.GoodsBrandVO;
import com.holderzone.holderpaasmdm.model.vo.GoodsLabelVO;
import com.holderzone.holderpaasmdm.model.vo.GoodsPictureVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsBaseExtendVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendVO;

import java.util.List;
import java.util.Map;

/**
 * Description: 店铺商品分组组装上下文
 * Author: 向超
 * Date: 2024/12/12 15:33
 */
public interface StoreGoodsAnalysisContext {
    /**
     * 扩展查询类型
     *
     * @return 扩展查询类型
     */
    StoreGoodsExpandQueryType getStoreGoodsExpandQueryType();
    /**
     * 是否需要查询品牌信息
     *
     * @return true:需要，false:不需要
     */
    boolean getNeedQueryBrandFlag();

    /**
     * 是否需要查询封面信息
     *
     * @return true:需要，false:不需要
     */
    boolean getNeedQueryCoverFlag();

    /**
     * 是否需要查询标签信息
     *
     * @return true:需要，false:不需要
     */
    boolean getNeedQueryLabelFlag();

    /**
     * 是否需要查询单位信息
     *
     * @return true:需要，false:不需要
     */
    boolean getNeedQueryUnitFlag();

    /**
     * 是否需要查询销售分组信息
     *
     * @return true:需要，false:不需要
     */
    boolean getNeedQuerySaleCategoryFlag();

    /**
     * 是否需要查询商品秤码信息
     *
     * @return true:需要，false:不需要
     */
    boolean getNeedQueryGoodsScaleCodeFlag();

    /**
     * 是否需要查询销售方案时段信息
     *
     * @return true:需要，false:不需要
     */
    boolean getNeedQueryTimeSectionFlag();

    /**
     * 获取店铺品牌ID和信息映射
     *
     * @return 店铺品牌ID和信息映射
     */
    Map<Integer, GoodsBrandVO> getStoreBrandIdAndInfoMap();

    /**
     * 获取店铺封面ID和信息映射
     *
     * @return 店铺封面ID和信息映射
     */
    Map<Integer, GoodsPictureVO> getStoreCoverIdAndInfoMap();

    /**
     * 获取店铺标签ID和信息映射
     *
     * @return 店铺标签ID和信息映射
     */
    Map<Integer, GoodsLabelVO> getStoreLabelIdAndInfoMap();

    /**
     * 获取店铺单位ID和名称映射
     *
     * @return 店铺单位ID和名称映射
     */
    Map<Integer, String> getStoreUnitIdAndNameMap();

    /**
     * 获取店铺销售分类ID和信息映射
     *
     * @return 店铺销售分类ID和信息映射
     */
    Map<Integer, List<StoreGoodsBaseExtendVO.SaleCategoryVO>> getStoreSaleCategoryIdAndInfoMap();

    /**
     * 获取店铺销售方案商品ID和销售方案时段信息的映射
     *
     * @return 店铺销售方案商品ID和销售方案时段信息的映射
     */
    Map<Integer, List<StoreGoodsExtendVO.SectionVO>> getStoreSalesProgramGoodsIdAndTimeSectionInfoMap();

     /**
      * 获取商品ID和秤码信息映射
      *
      * @return 商品ID和秤码信息映射
      */
    Map<Integer, List<StoreGoodsBaseExtendVO.GoodsScaleCodeVO>> getStoreGoodsIdAndGoodsScaleCodeVOInfoMap();
}

package com.holderzone.holderpaasmdm.common.datasource;

import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.holderzone.holderpaasmdm.common.constant.DataSourceConstants.DATASOURCE_DEFAULT;
import static com.holderzone.holderpaasmdm.common.constant.DataSourceConstants.DATASOURCE_DEFAULT_KEY;

/**
 * Description: 动态数据源
 * Author: 向超
 * Date: 2024/11/12 15:33
 */
@Component
@RequiredArgsConstructor
public class DynamicDataSource extends AbstractRoutingDataSource {
    private final DataSourceConfig dataSourceConfig;

    private final Map<Object, Object> dataSourceMap = new ConcurrentHashMap<>();

    @Override
    protected Object determineCurrentLookupKey() {
        return DataSourceContextHolder.getTenant();
    }

    @Override
    public void afterPropertiesSet() {
        // 默认数据源
        DataSource bmsPlatformDataSource = dataSourceConfig.createDataSource(DATASOURCE_DEFAULT);
        dataSourceMap.put(DATASOURCE_DEFAULT_KEY, bmsPlatformDataSource);
        super.setTargetDataSources(dataSourceMap);
        super.afterPropertiesSet();
    }

    public void addDataSource(String tenantId, DataSource dataSource) {
        dataSourceMap.put(tenantId, dataSource);
        super.setTargetDataSources(dataSourceMap);
        super.afterPropertiesSet();
    }

    public boolean containsDataSource(String tenantId) {
        return !dataSourceMap.containsKey(tenantId);
    }
}

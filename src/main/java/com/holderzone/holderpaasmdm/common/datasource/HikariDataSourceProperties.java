package com.holderzone.holderpaasmdm.common.datasource;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Description: 动态数据源配置类
 * Author: 向超
 * Date: 2024/11/13 15:33
 */
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "spring.datasource")
public class HikariDataSourceProperties {
    private String url;
    private String username;
    private String password;
    private String driverClassName;
    private String type;
    private int maximumPoolSize;
    private int minimumIdle;
    private long connectionTimeout;
    private long idleTimeout;
    private long maxLifetime;
}
   
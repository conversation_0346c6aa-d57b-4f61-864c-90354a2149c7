package com.holderzone.holderpaasmdm.common.datasource;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.List;


/**
 * Description: 数据源配置
 * Author: 向超
 * Date: 2024/11/12 15:33
 */
@Configuration
@RequiredArgsConstructor
public class DataSourceConfig {
    private final HikariDataSourceProperties dataSourceProperties;
    // 以下是需要操作默认数据源的URL列表，如果需要处理默认数据源，则需要在该列表中添加对应的URL
    public static final List<String> NEED_HANDLE_DEFAULT_DATASOURCE_URL_LIST = List.of("/api/v1/sale-channel/pos",
            "/api/v1/sale-channel/list");

    public DataSource createDataSource(String tenantId) {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(dataSourceProperties.getUrl() + tenantId);
        config.setUsername(dataSourceProperties.getUsername());
        config.setPassword(dataSourceProperties.getPassword());
        config.setDriverClassName(dataSourceProperties.getDriverClassName());
        config.setMaximumPoolSize(dataSourceProperties.getMaximumPoolSize());
        config.setMinimumIdle(dataSourceProperties.getMinimumIdle());
        config.setConnectionTimeout(dataSourceProperties.getConnectionTimeout());
        config.setIdleTimeout(dataSourceProperties.getIdleTimeout());
        config.setMaxLifetime(dataSourceProperties.getMaxLifetime());
        return new HikariDataSource(config);
    }
}

package com.holderzone.holderpaasmdm.common.datasource;

/**
 * Description: 数据源上下文
 * Author: 向超
 * Date: 2024/11/12 15:33
 */
public class DataSourceContextHolder {
    private DataSourceContextHolder() {
        // 私有构造方法
    }

    private static final ThreadLocal<String> CONTEXT_HOLDER = new ThreadLocal<>();

    public static void setTenant(String tenant) {
        CONTEXT_HOLDER.set(tenant);
    }

    public static String getTenant() {
        return CONTEXT_HOLDER.get();
    }

    public static void clear() {
        CONTEXT_HOLDER.remove();
    }
}

package com.holderzone.holderpaasmdm.common.datasource;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.sql.DataSource;

import java.util.concurrent.ConcurrentHashMap;

import static com.holderzone.holderpaasmdm.common.constant.DataSourceConstants.COMPANY_ID_HEADER_KEY;
import static com.holderzone.holderpaasmdm.common.constant.DataSourceConstants.DATASOURCE_DEFAULT_KEY;
import static com.holderzone.holderpaasmdm.common.constant.DataSourceConstants.DATASOURCE_PREFIX;

/**
 * Description: 数据源拦截器，在请求处理之前设置数据源
 * Author: 向超
 * Date: 2024/11/12 15:33
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataSourceInterceptor implements HandlerInterceptor {
    private final DynamicDataSource dynamicDataSource;
    private final DataSourceConfig dataSourceConfig;
    /**
     * 使用 ConcurrentHashMap 存储每个 companyId 对应的锁对象
     */
    private final ConcurrentHashMap<String, Object> lockMap = new ConcurrentHashMap<>();

    /**
     * 请求拦截器，在请求处理之前设置数据源
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param handler  处理器对象
     * @return true：继续执行；false：中断执行
     */
    @Override
    public boolean preHandle(HttpServletRequest request, @Nullable HttpServletResponse response, @Nullable Object handler) {
        String companyId = request.getHeader(COMPANY_ID_HEADER_KEY);
        if (DataSourceConfig.NEED_HANDLE_DEFAULT_DATASOURCE_URL_LIST.contains(request.getRequestURI())) {
            // 需要操作默认数据源的需要手动设置数据源
            DataSourceContextHolder.setTenant(DATASOURCE_DEFAULT_KEY);
            return true;
        } else if (StringUtils.isBlank(companyId)) {
            log.error("请求头中未包含企业id，无法设置数据源");
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INVALID_PARAMETER, "请求头中需要传入企业id");
        }
        companyId = DATASOURCE_PREFIX + companyId;
        DataSourceContextHolder.setTenant(companyId);
        // 如果数据源不存在，则动态创建并添加
        if (dynamicDataSource.containsDataSource(companyId)) {
            // 避免多线程环境下重复创建
            // 获取或创建锁对象
            Object lock = lockMap.computeIfAbsent(companyId, k -> new Object());
            synchronized (lock) {
                if (dynamicDataSource.containsDataSource(companyId)) {
                    log.info("数据源不存在，需要动态创建：{}", companyId);
                    DataSource newDataSource = dataSourceConfig.createDataSource(companyId);
                    dynamicDataSource.addDataSource(companyId, newDataSource);
                    log.info("数据源创建成功：{}", companyId);
                }
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(@Nullable HttpServletRequest request, @Nullable HttpServletResponse response,
                                @Nullable Object handler, Exception ex) {
        DataSourceContextHolder.clear();
    }
}

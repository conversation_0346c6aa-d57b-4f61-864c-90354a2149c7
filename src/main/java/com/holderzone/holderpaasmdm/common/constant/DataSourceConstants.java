package com.holderzone.holderpaasmdm.common.constant;


/**
 * Description: 数据源常量
 * Author: 向超
 * Date: 2024/11/13 15:33
 */
public class DataSourceConstants {
    private DataSourceConstants() {
        // 无参构造函数
    }

    /**
     * 最大空闲时间
     */
    public static final long MAX_IDLE_TIME = 30 * 60 * 1000L;
    /**
     * 企业id请求头key
     */
    public static final String COMPANY_ID_HEADER_KEY = "company_id";
    /**
     * 数据源前缀
     */
    public static final String DATASOURCE_PREFIX = "tenant_";
    /**
     * 默认数据源对应的Key
     */
    public static final String DATASOURCE_DEFAULT_KEY = "default";
    /**
     * 默认数据源: 平台库
     */
    public static final String DATASOURCE_DEFAULT = "bms_platform";
}

package com.holderzone.holderpaasmdm.common.constant;


/**
 * Description: 接口相应常量
 * Author: 向超
 * Date: 2025/01/15 15:33
 */
public class RequestConstants {
    private RequestConstants() {
        // 无参构造函数
    }

    /**
     * 企业id请求头key
     */
    public static final String COMPANY_ID_HEADER_KEY = "company_id";
    /**
     * 响应体基础结构Key - code
     */
    public static final String RESPONSE_BASE_CODE = "code";
    /**
     * 响应体基础结构Key - data
     */
    public static final String RESPONSE_BASE_DATA = "data";
    /**
     * 请求入参结构Key - refund_number
     */
    public static final String REQUEST_PARAM_REFUND_NUMBER = "refund_number";
    /**
     * 响应体业务结构Key - id
     */
    public static final String RESPONSE_BUSINESS_ID = "id";
    /**
     * 响应体业务结构Key - code
     */
    public static final String RESPONSE_BUSINESS_CODE = "code";
}

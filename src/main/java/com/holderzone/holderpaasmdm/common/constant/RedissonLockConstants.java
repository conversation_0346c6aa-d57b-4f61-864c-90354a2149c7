package com.holderzone.holderpaasmdm.common.constant;


/**
 * Description: Redisson锁相关的常量
 * Author: 向超
 * Date: 2024/12/13 15:33
 */
public class RedissonLockConstants {
    private RedissonLockConstants() {
        // 无参构造函数
    }

    /**
     * 零售商品传秤 - 秤内码 相关锁的前缀
     */
    public static final String RETAIL_GOODS_PLU_CODE_LOCK_PREFIX = "retail_goods_plu_code";

    /**
     * 零售商品传秤 - 秤内自编码 相关锁的前缀
     */
    public static final String RETAIL_GOODS_SCALE_CUSTOM_CODE_LOCK_PREFIX = "retail_goods_scale_custom__code";
}

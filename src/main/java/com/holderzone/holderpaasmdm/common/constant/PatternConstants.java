package com.holderzone.holderpaasmdm.common.constant;


/**
 * Description: 正则校验相关的常量
 * Author: 向超
 * Date: 2024/12/13 15:33
 */
public class PatternConstants {
    private PatternConstants() {
        // 无参构造函数
    }

    /**
     * 纯4位数字的正则表达式
     */
    public static final String PURE_FOUR_DIGIT_CODE = "^[0-9]{1,4}$";

    /**
     * 位置填充 - 4位
     */
    public static final String POSITION_FILLING_FOUR = "%04d";

    /**
     * 位置填充 - 5位
     */
    public static final String POSITION_FILLING_FIVE = "%05d";
}
package com.holderzone.holderpaasmdm.common.constant;


/**
 * Description: 错误信息键值对
 * Author: 向超
 * Date: 2024/11/13 15:33
 */
public class ErrorMessageKeys {
    private ErrorMessageKeys() {
        // 无参构造函数
    }
    public static final String FIELD_NOT_NULL = "字段不能为空";
    public static final String FIELD_RANGE_ERROR = "字段范围错误";
    public static final String GOODS_NOT_EXIST = "商品不存在";
    public static final String GOODS_IS_DELETED = "当前商品已被删除";
    public static final String GOODS_IS_DISABLE = "当前商品已被禁用";
    public static final String GOODS_NOT_IN_SALES_PROGRAM = "当前商品不可售";
    public static final String NO_SALE_PROGRAM = "没有可用的门店销售方案";
    public static final String SALES_PROGRAM_IS_DELETED = "当前商品所属策略单已被删除";
    public static final String SALES_PROGRAM_IS_DISABLE = "当前商品所属策略单已被禁用";
    public static final String UNAVAILABLE_FOR_SALE = "该商品当前时间不可售!";
    public static final String GOODS_SKU_NOT_EXIST = "该商品不存在规格数据!";
    public static final String GOODS_SKU_OVER = "该商品规格数据超过一条!";
}

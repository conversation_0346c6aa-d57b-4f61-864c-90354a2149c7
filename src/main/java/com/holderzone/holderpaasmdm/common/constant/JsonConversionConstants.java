package com.holderzone.holderpaasmdm.common.constant;


/**
 * Description: JSON转换常量类
 * Author: 向超
 * Date: 2024/11/14 15:33
 */
public class JsonConversionConstants {
    private JsonConversionConstants() {
        // 无参构造函数
    }

    /**
     * map转json失败的消息提示
     */
    public static final String MAP_JSON_CONVERSION_FAILED_MESSAGE = "JSON字符转换Map失败!";

    /**
     * list<基础类型>转json失败的消息提示
     */
    public static final String UNDERLYING_TYPE_LIST_JSON_CONVERSION_FAILED_MESSAGE = "JSON字符转换List<基础类型>失败!";

    /**
     * json序列化失败
     */
    public static final String JSON_CONVERSION_FAILED_MESSAGE = "序列化JSON时出错";
}

package com.holderzone.holderpaasmdm.common.constant;

/**
 * Description: 第三方接口请求路径常量
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
public class UrlPathConstants {

    private UrlPathConstants() {
    }

    /**
     * 出库 扣减库存接口
     */
    public static final String OUT_BOUND_CREATE_ORDER_PATH = "/api/sale_outbound/create_order";


    public static final String STORE_INFO_DETAILS_PATH = "/api/v1/device/retail/store_info/config/details";

    /**
     * 根据退款单号查询对应的入库单号（批量查询）
     */
    public static final String SELECT_GOODS_RECEIPT_NOTE_LIST = "/api/v1/order_refund/select_goods_receipt_note_list";

    /**
     * 查询渠道库存数据
     */
    public static final String SELECT_CHANNEL_INVENTORY = "/api/pos/query_channel_inventory";
}

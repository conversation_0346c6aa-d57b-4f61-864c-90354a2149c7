package com.holderzone.holderpaasmdm.common.handler;

import lombok.SneakyThrows;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;


/**
 * Description: 自定义TypeHandler用于处理List<Integer></>字段
 * Author: 向超
 * Date: 2024/12/02 15:34
 */
public class JsonTypIntegerListHandler implements TypeHandler<Object> {

    @SneakyThrows
    @Override
    public void setParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) {
        if (parameter == null) {
            ps.setNull(i, JdbcType.VARCHAR.ordinal());
        } else {
            Connection conn = ps.getConnection();
            Array array = conn.createArrayOf("integer", ((List<Integer>) parameter).toArray(new Integer[0]));
            ps.setArray(i, array);
        }
    }

    @Override
    public Object getResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        if (json == null) {
            return null;
        }
        // 将字符串转换为有效的 JSON 数组字符串
        if (json.startsWith("{") && json.endsWith("}")) {
            json = json.substring(1, json.length() - 1).replace("\"", "");
        }
        return Arrays.stream(json.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .toList();
    }

    @Override
    public Object getResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        if (json == null) {
            return null;
        }
        // 将字符串转换为有效的 JSON 数组字符串
        if (json.startsWith("{") && json.endsWith("}")) {
            json = json.substring(1, json.length() - 1).replace("\"", "");
        }
        return Arrays.stream(json.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .toList();
    }

    @Override
    public Object getResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        if (json == null) {
            return null;
        }
        // 将字符串转换为有效的 JSON 数组字符串
        if (json.startsWith("{") && json.endsWith("}")) {
            json = json.substring(1, json.length() - 1).replace("\"", "");
        }
        return Arrays.stream(json.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .toList();
    }
}
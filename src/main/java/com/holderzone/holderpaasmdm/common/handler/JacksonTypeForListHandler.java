package com.holderzone.holderpaasmdm.common.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

import static com.holderzone.holderpaasmdm.common.constant.JsonConversionConstants.JSON_CONVERSION_FAILED_MESSAGE;

/**
 * Description: 这个序列化器主要针对于List<基本类型>的字段且在数据库中存的是JSON的情况，
 * Author: 向超
 * Date: 2024/11/27 15:36
 */
public class JacksonTypeFor<PERSON>ist<PERSON><PERSON><PERSON> extends BaseTypeHandler<Object> {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setObject(i, objectMapper.writeValueAsString(parameter), Types.OTHER);
        } catch (JsonProcessingException e) {
            throw new SQLException(JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            String json = rs.getString(columnName);
            return objectMapper.readValue(json, Object.class);
        } catch (IOException e) {
            throw new SQLException(JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            String json = rs.getString(columnIndex);
            return objectMapper.readValue(json, Object.class);
        } catch (IOException e) {
            throw new SQLException(JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            String json = cs.getString(columnIndex);
            return objectMapper.readValue(json, Object.class);
        } catch (IOException e) {
            throw new SQLException(JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }
}

package com.holderzone.holderpaasmdm.common.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.SneakyThrows;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static com.holderzone.holderpaasmdm.common.constant.JsonConversionConstants.MAP_JSON_CONVERSION_FAILED_MESSAGE;

/**
 * Description: 自定义TypeHandler用于处理Map字段
 * Author: 向超
 * Date: 2024/11/14 15:34
 */
public class JsonTypeMapHandler implements TypeHandler<Object> {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    @Override
    public void setParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) {
        if (parameter == null) {
            ps.setNull(i, JdbcType.VARCHAR.ordinal());
        } else {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        }
    }

    @Override
    public Object getResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        if (json == null) {
            return null;
        }
        try {
            return objectMapper.readValue(json, TypeFactory.defaultInstance().constructType(Object.class));
        } catch (Exception e) {
            throw new SQLException(MAP_JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }

    @Override
    public Object getResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        if (json == null) {
            return null;
        }
        try {
            return objectMapper.readValue(json, TypeFactory.defaultInstance().constructType(Object.class));
        } catch (Exception e) {
            throw new SQLException(MAP_JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }

    @Override
    public Object getResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        if (json == null) {
            return null;
        }
        try {
            return objectMapper.readValue(json, TypeFactory.defaultInstance().constructType(Object.class));
        } catch (Exception e) {
            throw new SQLException(MAP_JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }
}
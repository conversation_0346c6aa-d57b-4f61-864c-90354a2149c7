package com.holderzone.holderpaasmdm.common.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Setter;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.io.IOException;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.List;

import static com.holderzone.holderpaasmdm.common.constant.JsonConversionConstants.JSON_CONVERSION_FAILED_MESSAGE;


/**
 * Description: 这个序列化器主要针对于List<自定义实体类>的字段且在数据库中存的是JSON的情况，
 * 这个类仅仅是基类，需要按自定义实体类来继承这个类，并赋值TAG_CLASS变量，告知JSON转换器反序列化的目标类型。
 * Author: 向超
 * Date: 2024/11/27 15:36
 */
@Setter
public class JacksonTypeHandler<T> extends BaseTypeHandler<Object> {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private Class<T> tagClass;

    static {
        // 注册 Java 8 时间支持
        objectMapper.registerModule(new JavaTimeModule())
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 使用 Jackson 序列化传入的对象为 JSON 字符串
            String json = objectMapper.writeValueAsString(parameter);
            ps.setObject(i, json, Types.OTHER);
        } catch (JsonProcessingException e) {
            throw new SQLException(JSON_CONVERSION_FAILED_MESSAGE, e);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getJsonResult(rs.getString(columnName));
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getJsonResult(rs.getString(columnIndex));
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getJsonResult(cs.getString(columnIndex));
    }

    // 处理 JSON 字符串，反序列化为目标类型
    private Object getJsonResult(String json) throws SQLException {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            // 如果目标类型是一个集合，处理 List、Map 等集合类型
            // 使用 TypeReference 确保反序列化为正确的 List<Section> 类型
            return objectMapper.readValue(json, TypeFactory.defaultInstance().constructCollectionType(List.class, tagClass));
        } catch (IOException e) {
            throw new SQLException("JSON 转换失败", e);
        }
    }

}

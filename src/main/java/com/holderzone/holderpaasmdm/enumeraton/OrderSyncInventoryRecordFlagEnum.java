package com.holderzone.holderpaasmdm.enumeraton;

/**
 * desc 订单同步库存记录Flag状态枚举
 *
 * <AUTHOR>
 * @date 2024/12/20
 * @since 1.8
 */
public enum OrderSyncInventoryRecordFlagEnum {

    SUCCESSFUL("SUCCESSFUL", "成功"),

    FAIL("FAILED", "失败");

    private String code;

    private String desc;

    OrderSyncInventoryRecordFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 门店商品上下架状态
 * Author: 向超
 * Date: 2024/11/25 15:33
 */
@Getter
@Slf4j
public enum StoreGoodsListingStatus {
    /**
     * 已上架
     */
    ON_SALE(1),
    /**
     * 已下架
     */
    OFF_SALE(2);

    private final Integer value;

    StoreGoodsListingStatus(Integer value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举对象
     *
     * @param value 值
     * @return 枚举对象
     */
    public static StoreGoodsListingStatus getEnum(Integer value) {
        for (StoreGoodsListingStatus item : StoreGoodsListingStatus.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        log.error("门店商品上下架状态枚举，未找到枚举值：{}", value);
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR);
    }
}

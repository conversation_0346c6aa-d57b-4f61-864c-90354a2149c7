package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 门店商品组合类型枚举类
 * Author: 向超
 * Date: 2024/12/02 15:33
 */
@Getter
@Slf4j
public enum StoreComboType {
    SINGLE_ITEM(1, "单品"),
    COMBO(2, "套餐");

    private final int id;
    private final String name;

    StoreComboType(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public static StoreComboType fromCode(Integer id) {
        if (id == null) {
            return null;
        }
        for (StoreComboType type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "无效的组合类型码: " + id);
    }

    public static String getNameFromCode(Integer id) {
        if (id == null) {
            return null;
        }
        return fromCode(id).getName();
    }
}

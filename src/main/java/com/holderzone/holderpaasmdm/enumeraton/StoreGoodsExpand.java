package com.holderzone.holderpaasmdm.enumeraton;


/**
 * Description: 门店商品的扩展查询条件
 * Author: 向超
 * Date: 2024/11/29 15:33
 */
public enum StoreGoodsExpand {
    /**
     * 销售分组
     */
    SALE_CATEGORY,
    /**
     * 商品分类
     */
    GOODS_CATEGORY,
    /**
     * 品牌
     */
    BRAND,
    /**
     * 标签
     */
    LABEL,
    /**
     * 单位
     */
    UNIT,
    /**
     * 封面
     */
    COVER,
    /**
     * 秤码
     */
    GOODS_SCALE_CODE,
    /**
     * 方案时段
     */
    SECTION
}

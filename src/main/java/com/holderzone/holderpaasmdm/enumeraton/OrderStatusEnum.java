package com.holderzone.holderpaasmdm.enumeraton;

/**
 * desc 订单昨天枚举
 *
 * <AUTHOR>
 * @date 2024/12/19
 * @since 1.8
 */
public enum OrderStatusEnum {

    UN_CREATED(0, "未创建"),
    CREATED(1, "已创建"),
    UNPAID(2, "待支付"),
    PAYING(3, "支付中"),
    PAID(4, "已支付"),
    DONE(5, "已完成"),

    CANCELED(6, "已取消"),
    CANCELING(7, "取消中"),
    FULL_REFUNDED(8, "已全部退款"),
    PART_REFUNDED(9, "已部分退款"),
    PAY_FAILED(10, "支付失败"),
    ANOMALY(11, "支付异常"),
    CANCEL_UN_CHECK(12, "取消未审核"),

    MEMBER_RECHARGING(13, "会员充值中"),
    MEMBER_RECHARGED(14, "会员充值成功"),
    MEMBER_RECHARGE_FAILED(15, "会员充值失败"),
    FULL_REFUNDING(16, "全额退款中"),
    FULL_REFUND_FAILED(17, "全额退款失败");

    /**
     * 订单状态
     */
    private int code;

    /**
     * 状态描述
     */
    private String desc;

    OrderStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 门店商品属性（门店商品特征）枚举类
 * Author: 向超
 * Date: 2024/12/02 15:33
 */
@Getter
@Slf4j
public enum StoreGoodsFeatures {
    SALEABLE(1, "可销售"),
    PRODUCIBLE(2, "可生产"),
    PURCHASABLE(3, "可采购");

    private final int id;
    private final String name;

    StoreGoodsFeatures(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public static StoreGoodsFeatures fromCode(Integer id) {
        if (id == null) {
            return null;
        }
        for (StoreGoodsFeatures attribute : values()) {
            if (attribute.getId() == id) {
                return attribute;
            }
        }
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "无效的门店商品特征码: " + id);
    }

    public static String getNameFromCode(Integer id) {
        if (id == null) {
            return null;
        }
        return fromCode(id).getName();
    }
}

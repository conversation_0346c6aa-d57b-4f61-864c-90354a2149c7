package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 门店商品库存属性枚举类
 * Author: 向超
 * Date: 2024/12/02 15:33
 */
@Getter
@Slf4j
public enum GoodsInventoryProperty {
    STOCK_ABLE_PRODUCT(1, "可库存产品"),
    VIRTUAL_PRODUCT(2, "虚拟产品"),
    SERVICE_PRODUCT(3, "服务类产品");

    private final int id;
    private final String name;

    GoodsInventoryProperty(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public static GoodsInventoryProperty fromCode(Integer id) {
        if (id == null) {
            return null;
        }
        for (GoodsInventoryProperty property : values()) {
            if (property.getId() == id) {
                return property;
            }
        }
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "无效的库存属性码: " + id);
    }

    public static String getNameFromCode(Integer id) {
        if (id == null) {
            return null;
        }
        return fromCode(id).getName();
    }
}
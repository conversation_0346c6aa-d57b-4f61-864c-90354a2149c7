package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 响应码枚举
 * Author: 向超
 * Date: 2024/11/13 15:33
 */
@Slf4j
@Getter
public enum ResponseCode {
    SUCCESSFUL(0),
    // ~~~ COMMON - 通用响应返回码 ~~~
    COMMON_BAD_REQUEST(1),
    COMMON_INVALID_PARAMETER(2),
    COMMON_NOT_FOUND(3),
    COMMON_INTERNAL_SERVER_ERROR(4),

    // ~~~ 零售 - 商品 - 响应码 ~~~

    /**
     * 当前商品已被删除
     */
    GOODS_IS_DELETED(10001),
    /**
     * 当前商品已被禁用
     */
    GOODS_IS_DISABLE(10002),
    /**
     * 当前商品所属策略单已被删除
     */
    SALES_PROGRAM_IS_DELETED(10003),

    /**
     * 当前商品所属策略单已被禁用
     */
    SALES_PROGRAM_IS_DISABLE(10004),

    /**
     * 当前商品不可售
     */
    GOODS_NOT_IN_SALES_PROGRAM(10005);


    private final int value;

    ResponseCode(int value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举对象
     *
     * @param value 值
     * @return 枚举对象
     */
    public static SalesProgramPeriodType getEnum(int value) {
        for (SalesProgramPeriodType item : SalesProgramPeriodType.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        log.error("销售方案时段类型枚举，未找到枚举值：{}", value);
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR);
    }

    // ~~~ XXX - 其他响应码 ~~~
}

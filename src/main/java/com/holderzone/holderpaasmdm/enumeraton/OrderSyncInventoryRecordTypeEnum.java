package com.holderzone.holderpaasmdm.enumeraton;

/**
 * desc 订单同步库存记录出入库类型枚举
 *
 * <AUTHOR>
 * @date 2024/12/20
 * @since 1.8
 */
public enum OrderSyncInventoryRecordTypeEnum {

    /**
     * 出入库类型：OUTBOUND:出库，PARTIAL_INBOUND:部分入库，ALL_INBOUND:全部入库
     */
    OUTBOUND("OUTBOUND", "出库"),
    PARTIAL_INBOUND("PARTIAL_INBOUND", "部分入库"),
    ALL_INBOUND("ALL_INBOUND", "全部入库");

    private String code;

    private String desc;

    OrderSyncInventoryRecordTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 传秤 - 商品状态秤内码、秤内自编码、计价方式变更类型
 * Author: 向超
 * Date: 2024/12/13 15:33
 */
@Getter
@Slf4j
public enum ScaleCodeChangeReminderType {
    /**
     * 商品计价方式变更
     */
    VALUATION_ERROR("valuation_error"),

    /**
     * 秤内自编码变更
     */
    CUSTOM_CODE_ERROR("custom_code_error"),

    /**
     * 秤内码变更
     */
    PLU_CODE_ERROR("plu_code_error");

    private final String value;

    ScaleCodeChangeReminderType(String value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举对象
     *
     * @param value 值
     * @return 枚举对象
     */
    public static ScaleCodeChangeReminderType getEnum(String value) {
        for (ScaleCodeChangeReminderType item : ScaleCodeChangeReminderType.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        log.error("传秤码变更类型枚举，未找到枚举值：{}", value);
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR);
    }
}

package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 销售方案状态
 * Author: 向超
 * Date: 2024/12/20 15:33
 */
@Getter
@Slf4j
public enum StoreSalesProgramStatus {
    /**
     * 启用
     */
    ENABLE(true),
    /**
     * 禁用
     */
    DISABLE(false);

    private final boolean value;

    StoreSalesProgramStatus(boolean value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举对象
     *
     * @param value 值
     * @return 枚举对象
     */
    public static StoreSalesProgramStatus getEnum(boolean value) {
        for (StoreSalesProgramStatus item : StoreSalesProgramStatus.values()) {
            if (item.isValue() == value) {
                return item;
            }
        }
        log.error("销售方案状态枚举，未找到枚举值：{}", value);
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR);
    }
}

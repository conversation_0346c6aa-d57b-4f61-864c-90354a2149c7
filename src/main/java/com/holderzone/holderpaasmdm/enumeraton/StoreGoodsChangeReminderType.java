package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 快速结账 - 商品状态变更类型
 * Author: 向超
 * Date: 2024/11/29 15:33
 */
@Getter
@Slf4j
public enum StoreGoodsChangeReminderType {
    /**
     * 商品的售卖价格
     */
    SELLING_PRICE("selling_price"),

    /**
     * 商品上下架状态
     */
    LISTING_STATUS("listing_status");

    private final String value;

    StoreGoodsChangeReminderType(String value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举对象
     *
     * @param value 值
     * @return 枚举对象
     */
    public static StoreGoodsChangeReminderType getEnum(String value) {
        for (StoreGoodsChangeReminderType item : StoreGoodsChangeReminderType.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        log.error("销售方案时段类型枚举，未找到枚举值：{}", value);
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR);
    }
}

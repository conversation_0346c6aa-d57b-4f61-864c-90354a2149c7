package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 门店商品计价方式枚举类
 * Author: 向超
 * Date: 2024/12/02 15:33
 */
@Getter
@Slf4j
public enum GoodsValuationMethod {
    NORMAL(1, "普通"),
    WEIGHING(2, "计重"),
    COUNTING(3, "计数");

    private final int id;
    private final String name;

    GoodsValuationMethod(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public static GoodsValuationMethod fromCode(Integer id) {
        if (id == null) {
            return null;
        }
        for (GoodsValuationMethod method : values()) {
            if (method.getId() == id) {
                return method;
            }
        }
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "无效的计价方式码: " + id);
    }

    public static String getNameFromCode(Integer id) {
        if (id == null) {
            return null;
        }
        return fromCode(id).getName();
    }
}

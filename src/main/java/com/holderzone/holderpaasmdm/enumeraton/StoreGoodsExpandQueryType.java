package com.holderzone.holderpaasmdm.enumeraton;


/**
 * Description: 门店商品的扩展查询条件类型
 * Author: 向超
 * Date: 2024/12/20 15:33
 */
public enum StoreGoodsExpandQueryType {
    /**
     * 基础类型的查询，不包含销售方案相关的查询
     */
    BASE,
    /**
     * 销售方案相关，以店铺商品为主，单时段的场景,
     */
    STORE_GOODS_SINGLE_TIMESLOT_SALES_SCHEME,

    /**
     * 销售方案相关，需要以店铺商品为维度分组的（多时段场景）
     */
    GROUP_BY_STORE_GOODS_MULTI_PERIOD
}

package com.holderzone.holderpaasmdm.enumeraton;

import com.holderzone.holderpaasmdm.common.exception.BaseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

/**
 * Description: 销售方案时段类型枚举
 * Author: 向超
 * Date: 2024/11/15 15:33
 */
@Getter
@Slf4j
public enum SalesProgramPeriodType {
    /**
     * 按天
     */
    DAILY(1),
    /**
     * 按周
     */
    WEEKLY(2),
    /**
     * 按月
     */
    MONTHLY(3);

    private final Integer value;

    SalesProgramPeriodType(Integer value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举对象
     *
     * @param value 值
     * @return 枚举对象
     */
    public static SalesProgramPeriodType getEnum(Integer value) {
        for (SalesProgramPeriodType item : SalesProgramPeriodType.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        log.error("销售方案时段类型枚举，未找到枚举值：{}", value);
        throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR);
    }
}

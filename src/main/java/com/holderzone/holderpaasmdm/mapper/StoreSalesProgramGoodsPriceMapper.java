package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.bo.BatchUpdateSellingPriceBO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPricePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Description: 门店销售方案中的商品时段价格表Mapper
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Mapper
public interface StoreSalesProgramGoodsPriceMapper extends BaseMapper<StoreSalesProgramGoodsPricePO> {

    /**
     * 批量更新销售价格
     *
     * @param paramList 批量更新参数
     * @param timestamp 时间戳
     */
    void batchUpdateSellingPrice(@Param("paramList") List<BatchUpdateSellingPriceBO> paramList,
                                 @Param("timestamp") Timestamp timestamp);
}

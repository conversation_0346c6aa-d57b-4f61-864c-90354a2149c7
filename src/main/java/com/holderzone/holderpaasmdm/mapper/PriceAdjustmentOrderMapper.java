package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description: 门店商品的调价单 Mapper
 * Author: 向超
 * Date: 2024/12/17 15:35
 */
@Mapper
public interface PriceAdjustmentOrderMapper extends BaseMapper<PriceAdjustmentOrderPO> {
    /**
     * 根据商品名称或者商品销售名称查询调价单列表
     *
     * @param goodsName 商品名称
     * @return 调价单列表
     */
    List<PriceAdjustmentOrderPO> queryPriceAdjustmentOrderListByGoodsName(@Param("goodsName") String goodsName);
}

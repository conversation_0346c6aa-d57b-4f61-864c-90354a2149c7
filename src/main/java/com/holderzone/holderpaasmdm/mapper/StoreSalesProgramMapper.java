package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Description: 门店销售方案 Mapper
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Mapper
public interface StoreSalesProgramMapper extends BaseMapper<StoreSalesProgramPO> {

    /**
     * 根据店铺销售方案商品ID列表查询店铺销售方案列表
     *
     * @param storeSalesProgramGoodsIdList 店铺销售方案商品ID列表
     * @return 店铺销售方案列表
     */
    List<StoreSalesProgramPO> queryStoreSalesProgramByStoreSalesProgramGoodsIdList(
            @Param("storeSalesProgramGoodsIdList") Collection<Integer> storeSalesProgramGoodsIdList);
}

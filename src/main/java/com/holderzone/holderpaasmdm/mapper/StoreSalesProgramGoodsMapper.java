package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.bo.BatchUpdateSellingPriceBO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;


/**
 * Description: 门店销售方案商品表Mapper
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Mapper
public interface StoreSalesProgramGoodsMapper extends BaseMapper<StoreSalesProgramGoodsPO> {
    /**
     * 批量更新默认销售价格
     *
     * @param paramList 批量更新销售价格BO
     * @param timestamp 时间戳
     * @param allSalesProgramIdSet 所有销售方案ID集合
     */
    void batchUpdateSellingPrice(@Param("paramList") List<BatchUpdateSellingPriceBO> paramList,
                                 @Param("timestamp") Timestamp timestamp,
                                 @Param("allSalesProgramIdSet") Set<Integer> allSalesProgramIdSet);
}

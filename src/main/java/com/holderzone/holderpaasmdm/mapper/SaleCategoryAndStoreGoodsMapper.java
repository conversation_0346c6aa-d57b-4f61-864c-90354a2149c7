package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryAndStoreGoodsPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Description: 销售分组和门店商品关系Mapper
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Mapper
public interface SaleCategoryAndStoreGoodsMapper extends BaseMapper<SaleCategoryAndStoreGoodsPO> {
    /**
     * 根据销售分组ID和渠道ID查询销售分组和门店商品关系
     *
     * @param storeGoodsIdList 销售分组ID列表
     * @param channelId 渠道ID
     * @return 销售分组集合
     */
    List<SaleCategoryAndStoreGoodsPO> querySaleCategoryByStoreGoodsIdListAndChannelId(
            @Param("storeGoodsIdList") Collection<Integer> storeGoodsIdList, @Param("channelId") Integer channelId);
}

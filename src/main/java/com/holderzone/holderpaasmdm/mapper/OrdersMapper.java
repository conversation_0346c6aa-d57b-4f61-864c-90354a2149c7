package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.dto.StoreDailySettleDTO;
import com.holderzone.holderpaasmdm.model.po.OrdersPO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024年12月18日
 */
@Mapper
public interface OrdersMapper extends BaseMapper<OrdersPO> {

    /**
     * 根据条件查询订单
     */
    List<OrdersPO> queryOrdersByConditions(@Param("param") StoreDailySettleDTO storeDailySettleDTO, @Param("companyId") Long companyId);

}

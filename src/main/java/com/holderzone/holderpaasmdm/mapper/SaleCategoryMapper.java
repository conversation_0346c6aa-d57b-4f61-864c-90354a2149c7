package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Description: 销售分组Mapper
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Mapper
public interface SaleCategoryMapper extends BaseMapper<SaleCategoryPO> {
    /**
     * 根据销售分组ID查询所有父级（世代）
     *
     * @param idList 销售分组ID列表
     * @param channelId 渠道ID
     * @return 父级（世代）列表
     */
    List<SaleCategoryPO> queryGenerationsVOListByIdListAndChannelId(@Param("idList") Collection<Integer> idList,
                                                                    @Param("channelId") Integer channelId);

    /**
     * 根据销售方案ID、渠道ID、店铺ID查询销售分组ID集合
     *
     * @param storeSalesProgramId 销售方案ID
     * @param channelId 渠道ID
     * @param storeId 店铺ID
     * @return 销售分组ID集合
     */
    List<Integer> querySaleCategoryIdBySaleProgramIdAndChannelIdAndStoreId(
            @Param("storeSalesProgramId") Integer storeSalesProgramId,
            @Param("channelId") Integer channelId,
            @Param("storeId") Integer storeId);
}

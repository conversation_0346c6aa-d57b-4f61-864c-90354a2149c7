package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.dto.SqlQueryGoodsByTagListDTO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Description: 门店商品表（SPU）Mapper
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Mapper
public interface StoreGoodsMapper extends BaseMapper<StoreGoodsPO> {

    /**
     * 查询门店商品基础信息列表（可售商品）
     *
     * @param storeId                    门店ID
     * @param channelId                  渠道ID
     * @param barcode                    商品条码
     * @param storeGoodsIdList           店铺商品ID集合
     * @param goodsValuationMethodIdList 商品计价方式ID集合
     * @return 门店商品基础信息列表
     */
    List<StoreGoodsPO> queryStoreGoodsForAvailableForSaleWeighList(@Param("storeId") Integer storeId,
                                                                   @Param("channelId") Integer channelId,
                                                                   @Param("barcode") String barcode,
                                                                   @Param("storeGoodsIdList") List<Integer> storeGoodsIdList,
                                                                   @Param("goodsValuationMethodIdList") List<Integer> goodsValuationMethodIdList);

    /**
     * 根据秤内自编码查询店铺商品信息
     *
     * @param scaleCustomCode 秤内自编码
     * @param scaleType       秤类型
     * @return 店铺商品信息
     */
    List<StoreGoodsPO> queryStoreGoodsByScaleCustomCode(@Param("storeId") Integer storeId,
                                                        @Param("scaleCustomCode") String scaleCustomCode,
                                                        @Param("scaleType") Integer scaleType);

    /**
     * 根据店铺商品ID集合和关键字查询门店商品列表
     *
     * @param idList              店铺商品ID集合
     * @param keywords            关键字
     * @param storeSalesProgramId 销售方案ID
     * @return 门店商品列表
     */
    List<StoreGoodsPO> queryStoreGoodsByIdListAndKeywords(@Param("idList") Collection<Integer> idList,
                                                          @Param("keywords") String keywords,
                                                          @Param("storeSalesProgramId") Integer storeSalesProgramId);

    /**
     * 根据编码查询门店商品信息(商品自编码、商品条码、秤内自编码)
     *
     * @param code      商品编码
     * @param storeId   门店ID
     * @param scaleType 秤类型
     * @return 门店商品信息
     */
    List<StoreGoodsPO> queryStoreGoodsByCode(@Param("code") String code, @Param("storeId") Integer storeId,
                                             @Param("scaleType") Integer scaleType);

    /**
     * 查询所有商品，按照规格、时段分层查询
     *
     * @param allStoreSalesProgramGoodsIds 商品id集合
     * @param storeSalesProgramIdList      策略id集合
     * @return 查询结果
     */
    List<SqlQueryGoodsByTagListDTO> queryAllSkuGoodsByTag(@Param("allStoreSalesProgramGoodsIds") Collection<Integer> allStoreSalesProgramGoodsIds,
                                                          @Param("storeSalesProgramIdList") Collection<Integer> storeSalesProgramIdList);
}

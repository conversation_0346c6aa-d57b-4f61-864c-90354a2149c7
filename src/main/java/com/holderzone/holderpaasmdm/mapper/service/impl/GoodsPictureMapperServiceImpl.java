package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsPictureMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsPictureMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsPicturePO;
import org.springframework.stereotype.Service;

/**
 * Description: 商品图片Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class GoodsPictureMapperServiceImpl extends ServiceImpl<GoodsPictureMapper, GoodsPicturePO>
        implements GoodsPictureMapperService {
}

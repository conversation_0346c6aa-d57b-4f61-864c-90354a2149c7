package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.StoreChannelBindMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreChannelBindMapperService;
import com.holderzone.holderpaasmdm.model.po.StoreChannelBindPO;
import org.springframework.stereotype.Service;

/**
 * 店铺渠道绑定扩展表mapperService
 *
 * <AUTHOR>
 * @date 2025/6/6 17:55
 **/
@Service
public class StoreChannelBindMapperServiceImpl extends ServiceImpl<StoreChannelBindMapper, StoreChannelBindPO>
        implements StoreChannelBindMapperService {
}

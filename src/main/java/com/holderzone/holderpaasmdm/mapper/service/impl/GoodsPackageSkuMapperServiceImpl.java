package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsPackageSkuMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsPackageSkuMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsPackageSkuPO;
import com.holderzone.holderpaasmdm.model.po.SkuSpecDetailsPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: SKU商品表 Mapper 接口实现类
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
@Service
public class GoodsPackageSkuMapperServiceImpl extends ServiceImpl<GoodsPackageSkuMapper, GoodsPackageSkuPO>
        implements GoodsPackageSkuMapperService {

    @Override
    public Map<Integer, GoodsPackageSkuPO> findMapByIdIn(Collection<Integer> ids) {
        return this.listByIds(ids).stream().collect(Collectors.toMap(GoodsPackageSkuPO::getId, Function.identity()));
    }

    @Override
    public List<GoodsPackageSkuPO> findBarcodeByStoreGoodsIdIn(Collection<Integer> storeGoodsIds) {
        return this.lambdaQuery()
                .select(GoodsPackageSkuPO::getStoreGoodsId, GoodsPackageSkuPO::getBarcode)
                .in(GoodsPackageSkuPO::getStoreGoodsId, storeGoodsIds)
                .isNotNull(GoodsPackageSkuPO::getBarcode)
                .list();
    }

    @Override
    public List<GoodsPackageSkuPO> findByStoreGoodsIdInAndStoreId(Collection<Integer> storeGoodsIds, Integer storeId) {
        return this.lambdaQuery()
                .in(GoodsPackageSkuPO::getStoreGoodsId, storeGoodsIds)
                .eq(GoodsPackageSkuPO::getStoreId, storeId)
                .list();
    }
}

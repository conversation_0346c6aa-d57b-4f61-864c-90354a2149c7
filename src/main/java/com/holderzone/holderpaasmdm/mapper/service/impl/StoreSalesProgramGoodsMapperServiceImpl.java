package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.StoreSalesProgramGoodsMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsMapperService;
import com.holderzone.holderpaasmdm.model.bo.BatchUpdateSellingPriceBO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

import static java.util.stream.Collectors.toMap;


/**
 * Description: 门店销售方案商品表Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Service
public class StoreSalesProgramGoodsMapperServiceImpl extends ServiceImpl<StoreSalesProgramGoodsMapper, StoreSalesProgramGoodsPO>
        implements StoreSalesProgramGoodsMapperService {
    @Override
    public void batchUpdateSellingPrice(List<BatchUpdateSellingPriceBO> batchUpdateSellingPriceBOForDefaultList, Timestamp timestamp, Set<Integer> allSalesProgramIdSet) {
        this.baseMapper.batchUpdateSellingPrice(batchUpdateSellingPriceBOForDefaultList, timestamp, allSalesProgramIdSet);
    }

    @Override
    public Map<Integer, StoreSalesProgramGoodsPO> findMapByIdIn(Collection<Integer> ids) {
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOS = this.listByIds(ids);
        return storeSalesProgramGoodsPOS.stream().collect(toMap(StoreSalesProgramGoodsPO::getId, Function.identity()));
    }

    @Override
    public List<Integer> queryGoodsIdsByProgramIdIn(List<Integer> programIdList) {
        return this.lambdaQuery()
                .in(StoreSalesProgramGoodsPO::getStoreSalesProgramId, programIdList)
                .list().stream()
                .map(StoreSalesProgramGoodsPO::getGoodsId)
                .distinct()
                .toList();
    }
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsSpecMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsSpecMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecPO;
import org.springframework.stereotype.Service;

/**
 * Description: 商品规格表 Mapper 接口实现类
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
@Service
public class GoodsSpecMapperServiceImpl extends ServiceImpl<GoodsSpecMapper, GoodsSpecPO>
        implements GoodsSpecMapperService {
}

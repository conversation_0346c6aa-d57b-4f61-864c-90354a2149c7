package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.StoreSalesProgramGoodsPriceMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsPriceMapperService;
import com.holderzone.holderpaasmdm.model.bo.BatchUpdateSellingPriceBO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPricePO;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 门店销售方案中的商品时段价格表Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class StoreSalesProgramGoodsPriceMapperServiceImpl extends ServiceImpl<StoreSalesProgramGoodsPriceMapper,
        StoreSalesProgramGoodsPricePO> implements StoreSalesProgramGoodsPriceMapperService {
    @Override
    public void batchUpdateSellingPrice(List<BatchUpdateSellingPriceBO> paramList, Timestamp timestamp) {
        this.baseMapper.batchUpdateSellingPrice(paramList, timestamp);
    }

    @Override
    public Map<Integer, StoreSalesProgramGoodsPricePO> findMapByIdIn(Collection<Integer> ids) {
        return this.listByIds(ids).stream().collect(
                Collectors.toMap(StoreSalesProgramGoodsPricePO::getId, Function.identity()));
    }
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.PriceAdjustmentOrderGoodsChangeInfoMapper;
import com.holderzone.holderpaasmdm.mapper.service.PriceAdjustmentOrderGoodsChangeInfoMapperService;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderGoodsChangeInfoPO;
import org.springframework.stereotype.Service;

/**
 * Description: 调价单商品调价信息 Mapper 接口实现类
 * Author: 向超
 * Date: 2024/12/17 15:35
 */
@Service
public class PriceAdjustmentOrderGoodsChangeInfoMapperServiceImpl extends ServiceImpl<PriceAdjustmentOrderGoodsChangeInfoMapper,
        PriceAdjustmentOrderGoodsChangeInfoPO> implements PriceAdjustmentOrderGoodsChangeInfoMapperService {
}

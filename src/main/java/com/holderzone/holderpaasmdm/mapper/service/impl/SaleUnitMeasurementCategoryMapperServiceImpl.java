package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SaleUnitMeasurementCategoryMapper;
import com.holderzone.holderpaasmdm.mapper.service.SaleUnitMeasurementCategoryMapperService;
import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementCategoryPO;
import org.springframework.stereotype.Service;

/**
 * Description: 商品计量单位分类Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class SaleUnitMeasurementCategoryMapperServiceImpl extends ServiceImpl<SaleUnitMeasurementCategoryMapper,
        SaleUnitMeasurementCategoryPO> implements SaleUnitMeasurementCategoryMapperService {
}

package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderPO;

import java.util.List;

/**
 * Description: 调价单 Mapper 接口
 * Author: 向超
 * Date: 2024/12/17 15:35
 */
public interface PriceAdjustmentOrderMapperService extends IService<PriceAdjustmentOrderPO> {
    /**
     * 根据商品名称查询调价单列表
     *
     * @param goodsName 商品名称
     * @return 调价单列表
     */
    List<PriceAdjustmentOrderPO> queryPriceAdjustmentOrderListByGoodsName(String goodsName);
}

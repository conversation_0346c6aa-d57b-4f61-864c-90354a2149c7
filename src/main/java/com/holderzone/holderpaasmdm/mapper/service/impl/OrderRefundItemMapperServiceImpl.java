package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.OrderRefundItemMapper;
import com.holderzone.holderpaasmdm.mapper.service.OrderRefundItemMapperService;
import com.holderzone.holderpaasmdm.model.po.OrderRefundItemPO;
import org.springframework.stereotype.Service;


/**
 * Description: 订单退款商品Mapper接口实现类
 * Author: 向超
 * Date: 2025/01/14 15:35
 */
@Service
public class OrderRefundItemMapperServiceImpl extends ServiceImpl<OrderRefundItemMapper, OrderRefundItemPO>
        implements OrderRefundItemMapperService {
}

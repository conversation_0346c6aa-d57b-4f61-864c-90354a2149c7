package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.GoodsScaleCodePO;


/**
 * Description: 店铺商品与秤内码的对应关系Mapper接口
 * Author: 向超
 * Date: 2024/12/12 15:35
 */
public interface GoodsScaleCodeMapperService extends IService<GoodsScaleCodePO> {

    /**
     * 商品id查询数据
     *
     * @param storeGoodsId 商品id
     * @return 查询结果
     */
    GoodsScaleCodePO findByStoreGoodsId(Integer storeGoodsId);
}

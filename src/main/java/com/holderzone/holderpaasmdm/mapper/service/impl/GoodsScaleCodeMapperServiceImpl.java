package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsScaleCodeMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsScaleCodeMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsScaleCodePO;
import org.springframework.stereotype.Service;


/**
 * Description: 店铺商品与秤内码的对应关系Mapper接口实现类
 * Author: 向超
 * Date: 2024/12/12 15:35
 */
@Service
public class GoodsScaleCodeMapperServiceImpl extends ServiceImpl<GoodsScaleCodeMapper, GoodsScaleCodePO>
        implements GoodsScaleCodeMapperService {

    @Override
    public GoodsScaleCodePO findByStoreGoodsId(Integer storeGoodsId) {
        return this.lambdaQuery()
                .eq(GoodsScaleCodePO::getGoodsId, storeGoodsId)
                .isNotNull(GoodsScaleCodePO::getScaleCustomCode)
                .eq(GoodsScaleCodePO::getDisabled, Boolean.FALSE)
                .one();
    }
}

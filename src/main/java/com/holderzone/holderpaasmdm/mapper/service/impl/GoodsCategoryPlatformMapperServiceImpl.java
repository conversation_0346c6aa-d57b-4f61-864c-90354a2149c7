package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsCategoryPlatformMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsCategoryPlatformMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsCategoryPlatformPO;
import org.springframework.stereotype.Service;

/**
 * 平台商品分类mapper接口实现类
 *
 * <AUTHOR>
 * @date 2025/5/30 9:49
 **/
@Service
public class GoodsCategoryPlatformMapperServiceImpl
        extends ServiceImpl<GoodsCategoryPlatformMapper, GoodsCategoryPlatformPO>
        implements GoodsCategoryPlatformMapperService {
}

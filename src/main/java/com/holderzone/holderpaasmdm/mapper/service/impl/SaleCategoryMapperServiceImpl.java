package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SaleCategoryMapper;
import com.holderzone.holderpaasmdm.mapper.service.SaleCategoryMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsCategoryPlatformPO;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Description: 销售分组Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class SaleCategoryMapperServiceImpl extends ServiceImpl<SaleCategoryMapper, SaleCategoryPO>
        implements SaleCategoryMapperService {

    @Override
    public List<SaleCategoryPO> queryGenerationsVOListByIdListAndChannelId(Collection<Integer> idList, Integer channelId) {
        return baseMapper.queryGenerationsVOListByIdListAndChannelId(idList, channelId);
    }

    @Override
    public List<Integer> querySaleCategoryIdBySaleProgramIdAndChannelIdAndStoreId(Integer storeSalesProgramId, Integer channelId, Integer storeId) {
        return baseMapper.querySaleCategoryIdBySaleProgramIdAndChannelIdAndStoreId(storeSalesProgramId, channelId, storeId);
    }

    @Override
    public List<SaleCategoryPO> queryByStoreAndChannel(Integer channel, Integer storeId) {
        return this.lambdaQuery()
                .eq(SaleCategoryPO::getChannelId, channel)
                .eq(SaleCategoryPO::getStoreId, storeId)
                .orderByDesc(SaleCategoryPO::getSort)
                .orderByAsc(SaleCategoryPO::getCreatedAt)
                .list();
    }
}

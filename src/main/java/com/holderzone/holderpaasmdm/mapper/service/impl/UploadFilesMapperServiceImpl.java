package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.UploadFilesMapper;
import com.holderzone.holderpaasmdm.mapper.service.UploadFilesMapperService;
import com.holderzone.holderpaasmdm.model.po.UploadFilesPO;
import org.springframework.stereotype.Service;

/**
 * Description: 上传文件 Mapper 接口实现类
 * Author: 向超
 * Date: 2024/11/27 15:35
 */
@Service
public class UploadFilesMapperServiceImpl extends ServiceImpl<UploadFilesMapper, UploadFilesPO>
        implements UploadFilesMapperService {
}

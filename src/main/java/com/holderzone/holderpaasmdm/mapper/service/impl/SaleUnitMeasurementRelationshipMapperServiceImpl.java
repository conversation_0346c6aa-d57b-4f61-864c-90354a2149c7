package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SaleUnitMeasurementRelationshipMapper;
import com.holderzone.holderpaasmdm.mapper.service.SaleUnitMeasurementRelationshipMapperService;
import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementRelationshipPO;
import org.springframework.stereotype.Service;

/**
 * Description: 销售单位计量关系Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class SaleUnitMeasurementRelationshipMapperServiceImpl extends ServiceImpl<SaleUnitMeasurementRelationshipMapper,
        SaleUnitMeasurementRelationshipPO> implements SaleUnitMeasurementRelationshipMapperService {
}

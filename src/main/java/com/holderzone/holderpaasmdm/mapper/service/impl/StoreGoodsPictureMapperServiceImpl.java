package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.StoreGoodsPictureMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreGoodsPictureMapperService;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPicturePO;
import org.springframework.stereotype.Service;

/**
 * Description: 门店商品图片Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class StoreGoodsPictureMapperServiceImpl extends ServiceImpl<StoreGoodsPictureMapper, StoreGoodsPicturePO>
        implements StoreGoodsPictureMapperService {
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.StoreSalesProgramMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramMapperService;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Description: 门店销售方案 Mapper 接口实现类
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Service
public class StoreSalesProgramMapperServiceImpl extends ServiceImpl<StoreSalesProgramMapper, StoreSalesProgramPO>
        implements StoreSalesProgramMapperService {
    @Override
    public List<StoreSalesProgramPO> queryStoreSalesProgramByStoreSalesProgramGoodsIdList(Collection<Integer> storeSalesProgramGoodsIdList) {
        return baseMapper.queryStoreSalesProgramByStoreSalesProgramGoodsIdList(storeSalesProgramGoodsIdList);
    }
}

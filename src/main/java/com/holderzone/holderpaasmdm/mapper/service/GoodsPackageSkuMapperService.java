package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.GoodsPackageSkuPO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Description: SKU商品表 Mapper 接口
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
public interface GoodsPackageSkuMapperService extends IService<GoodsPackageSkuPO> {

    /**
     * 通过id查询sku数据信息
     *
     * @param ids id集合
     * @return 查询结果
     */
    Map<Integer, GoodsPackageSkuPO> findMapByIdIn(Collection<Integer> ids);

    /**
     * 商品id集合查询sku信息
     *
     * @param storeGoodsIds 商品id集合
     * @return 查询结果
     */
    List<GoodsPackageSkuPO> findBarcodeByStoreGoodsIdIn(Collection<Integer> storeGoodsIds);

    /**
     * 商品id集合查询sku信息
     *
     * @param storeGoodsIds 商品id集合
     * @param storeId       店铺id
     * @return 查询结果
     */
    List<GoodsPackageSkuPO> findByStoreGoodsIdInAndStoreId(Collection<Integer> storeGoodsIds, Integer storeId);
}

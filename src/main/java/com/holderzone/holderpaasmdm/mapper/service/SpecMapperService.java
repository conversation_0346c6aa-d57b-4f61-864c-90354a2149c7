package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.SpecPO;

import java.util.Map;

/**
 * Description: 规格表 Mapper 接口
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
public interface SpecMapperService extends IService<SpecPO> {

    /**
     * 门店查询数据信息
     *
     * @param storeId 门店id
     * @return 查询结果
     */
    Map<Integer, SpecPO> findMapByStoreId(Integer storeId);
}

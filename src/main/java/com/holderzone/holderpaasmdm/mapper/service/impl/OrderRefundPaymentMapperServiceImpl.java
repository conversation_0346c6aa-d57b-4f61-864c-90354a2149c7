package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.OrderRefundPaymentMapper;
import com.holderzone.holderpaasmdm.mapper.service.OrderRefundPaymentMapperService;
import com.holderzone.holderpaasmdm.model.po.OrderRefundPaymentPO;
import org.springframework.stereotype.Service;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
@Service
public class OrderRefundPaymentMapperServiceImpl extends ServiceImpl<OrderRefundPaymentMapper, OrderRefundPaymentPO>
        implements OrderRefundPaymentMapperService {
}

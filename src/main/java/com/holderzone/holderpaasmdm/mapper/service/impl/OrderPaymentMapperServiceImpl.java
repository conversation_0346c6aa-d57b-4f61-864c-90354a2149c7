package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.OrderPaymentMapper;
import com.holderzone.holderpaasmdm.mapper.service.OrderPaymentMapperService;
import com.holderzone.holderpaasmdm.model.po.OrderPaymentPO;
import org.springframework.stereotype.Service;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
@Service
public class OrderPaymentMapperServiceImpl extends ServiceImpl<OrderPaymentMapper, OrderPaymentPO>
        implements OrderPaymentMapperService {
}

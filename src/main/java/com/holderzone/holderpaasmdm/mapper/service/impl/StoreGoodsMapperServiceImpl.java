package com.holderzone.holderpaasmdm.mapper.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsListingStatus;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsStatus;
import com.holderzone.holderpaasmdm.mapper.StoreGoodsMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreGoodsMapperService;
import com.holderzone.holderpaasmdm.model.dto.SqlQueryGoodsByTagListDTO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Description: 门店商品表（SPU）Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Service
public class StoreGoodsMapperServiceImpl extends ServiceImpl<StoreGoodsMapper, StoreGoodsPO>
        implements StoreGoodsMapperService {
    @Override
    public List<StoreGoodsPO> queryStoreGoodsForAvailableForSaleWeighList(Integer storeId, Integer channelId,
                                                                          String barcode, List<Integer> storeGoodsIdList,
                                                                          List<Integer> goodsValuationMethodIdList) {
        return baseMapper.queryStoreGoodsForAvailableForSaleWeighList(storeId, channelId, barcode, storeGoodsIdList, goodsValuationMethodIdList);
    }

    @Override
    public List<StoreGoodsPO> queryStoreGoodsByScaleCustomCode(Integer storeId, String scaleCustomCode, Integer scaleType) {
        return baseMapper.queryStoreGoodsByScaleCustomCode(storeId, scaleCustomCode, scaleType);
    }

    @Override
    public List<StoreGoodsPO> queryStoreGoodsByIdListAndKeywords(Collection<Integer> idList, String keywords, Integer storeSalesProgramId) {
        return baseMapper.queryStoreGoodsByIdListAndKeywords(idList, keywords, storeSalesProgramId);
    }

    @Override
    public List<StoreGoodsPO> queryStoreGoodsByCode(String code, Integer storeId, Integer scaleType) {
        return baseMapper.queryStoreGoodsByCode(code, storeId, scaleType);
    }

    @Override
    public List<SqlQueryGoodsByTagListDTO> queryAllSkuGoodsByTag(Collection<Integer> allStoreSalesProgramGoodsIds,
                                                                 Collection<Integer> StoreSalesProgramIdList) {
        return baseMapper.queryAllSkuGoodsByTag(allStoreSalesProgramGoodsIds, StoreSalesProgramIdList);
    }

    @Override
    public List<Integer> queryOnSaleIdsByIdIn(List<Integer> storeGoodsIdList) {
        if(CollUtil.isEmpty(storeGoodsIdList)) {
            return List.of();
        }
        return this.lambdaQuery()
                .select(StoreGoodsPO::getId)
                .in(StoreGoodsPO::getId, storeGoodsIdList)
                .eq(StoreGoodsPO::getStatus, StoreGoodsStatus.ENABLE.getValue())
                .eq(StoreGoodsPO::getListingStatus, StoreGoodsListingStatus.ON_SALE.getValue())
                .list().stream().map(StoreGoodsPO::getId)
                .toList();
    }
}

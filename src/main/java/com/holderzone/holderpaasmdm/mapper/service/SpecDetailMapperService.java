package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.SpecDetailPO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Description: 规格明细表 Mapper 接口
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
public interface SpecDetailMapperService extends IService<SpecDetailPO> {

    /**
     * 根据规格id查询规格明细
     *
     * @param specIdList 规格id集合
     * @return 查询结果
     */
    Map<Integer, SpecDetailPO> findBySpecIdIn(Collection<Integer> specIdList);
}

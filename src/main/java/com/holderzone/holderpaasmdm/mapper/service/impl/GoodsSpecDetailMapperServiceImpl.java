package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsSpecDetailMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsSpecDetailMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecDetailPO;
import org.springframework.stereotype.Service;

/**
 * Description: 商品规格明细表 Mapper 接口实现类
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
@Service
public class GoodsSpecDetailMapperServiceImpl extends ServiceImpl<GoodsSpecDetailMapper, GoodsSpecDetailPO>
        implements GoodsSpecDetailMapperService {
}

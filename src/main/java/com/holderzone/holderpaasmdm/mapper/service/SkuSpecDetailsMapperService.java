package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.dto.SqlQuerySkuSpecInfoDTO;
import com.holderzone.holderpaasmdm.model.po.SkuSpecDetailsPO;

import java.util.Collection;
import java.util.List;

/**
 * Description: SKU商品规格中间表 Mapper 接口
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
public interface SkuSpecDetailsMapperService extends IService<SkuSpecDetailsPO> {
    /**
     * 根据关键字查询匹配成功的skuId
     *
     * @param keywords 关键字
     * @param storeId  店铺id
     * @return 匹配成功的skuId列表
     */
    List<Integer> queryMatchSuccessfulSkuIdByKeywords(String keywords, Integer storeId);

    /**
     * 根据skuId列表查询sku规格信息，并排序
     *
     * @param skuIds skuId列表
     * @return sku规格信息
     */
    List<SqlQuerySkuSpecInfoDTO> querySpecInfoBySkuIdIn(Collection<Integer> skuIds);
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.PriceAdjustmentOrderMapper;
import com.holderzone.holderpaasmdm.mapper.service.PriceAdjustmentOrderMapperService;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description: 调价单 Mapper 接口实现类
 * Author: 向超
 * Date: 2024/12/17 15:35
 */
@Service
public class PriceAdjustmentOrderMapperServiceImpl extends ServiceImpl<PriceAdjustmentOrderMapper, PriceAdjustmentOrderPO>
        implements PriceAdjustmentOrderMapperService {
    @Override
    public List<PriceAdjustmentOrderPO> queryPriceAdjustmentOrderListByGoodsName(String goodsName) {
        return baseMapper.queryPriceAdjustmentOrderListByGoodsName(goodsName);
    }
}

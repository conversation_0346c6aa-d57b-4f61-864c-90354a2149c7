package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsCategoryMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsCategoryMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsCategoryPO;
import org.springframework.stereotype.Service;

/**
 * Description: 企业商品分类Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/27 15:35
 */
@Service
public class GoodsCategoryMapperServiceImpl extends ServiceImpl<GoodsCategoryMapper, GoodsCategoryPO>
        implements GoodsCategoryMapperService {
}

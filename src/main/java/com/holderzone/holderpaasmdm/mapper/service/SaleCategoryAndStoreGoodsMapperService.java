package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryAndStoreGoodsPO;

import java.util.Collection;
import java.util.List;

/**
 * Description: 销售分组和门店商品关系Mapper接口
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
public interface SaleCategoryAndStoreGoodsMapperService extends IService<SaleCategoryAndStoreGoodsPO> {

    /**
     * 根据销售分组ID和渠道ID查询销售分组和门店商品关系
     *
     * @param storeGoodsIdList 销售分组ID列表
     * @param channelId        渠道ID
     * @return 销售分组集合
     */
    List<SaleCategoryAndStoreGoodsPO> querySaleCategoryByStoreGoodsIdListAndChannelId(Collection<Integer> storeGoodsIdList, Integer channelId);

    /**
     * 分组和商品id查询数据信息
     *
     * @param saleCategoryIds  分组id集合
     * @param storeGoodsIdList 商品id集合
     * @return 查询结果
     */
    List<SaleCategoryAndStoreGoodsPO> querySaleCategoryAndStoreGoodsIn(Collection<Integer> saleCategoryIds,
                                                                       Collection<Integer> storeGoodsIdList);
}

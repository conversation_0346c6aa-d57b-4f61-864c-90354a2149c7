package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.StoreGoodsCategoryMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreGoodsCategoryMapperService;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsCategoryPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description: 门店商品分类Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class StoreGoodsCategoryMapperServiceImpl extends ServiceImpl<StoreGoodsCategoryMapper, StoreGoodsCategoryPO>
        implements StoreGoodsCategoryMapperService {
    @Override
    public List<StoreGoodsCategoryPO> queryGenerationsVOListByIdList(List<Integer> category) {
        return baseMapper.queryGenerationsVOListByIdList(category);
    }
}

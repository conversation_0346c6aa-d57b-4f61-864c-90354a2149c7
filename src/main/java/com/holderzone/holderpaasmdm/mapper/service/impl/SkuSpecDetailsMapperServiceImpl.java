package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SkuSpecDetailsMapper;
import com.holderzone.holderpaasmdm.mapper.service.SkuSpecDetailsMapperService;
import com.holderzone.holderpaasmdm.model.dto.SqlQuerySkuSpecInfoDTO;
import com.holderzone.holderpaasmdm.model.po.SkuSpecDetailsPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Description: SKU商品规格中间表 Mapper 接口实现类
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
@Service
public class SkuSpecDetailsMapperServiceImpl extends ServiceImpl<SkuSpecDetailsMapper, SkuSpecDetailsPO>
        implements SkuSpecDetailsMapperService {
    @Override
    public List<Integer> queryMatchSuccessfulSkuIdByKeywords(String keywords, Integer storeId) {
        return this.baseMapper.queryMatchSuccessfulSkuIdByKeywords(keywords, storeId);
    }

    @Override
    public List<SqlQuerySkuSpecInfoDTO> querySpecInfoBySkuIdIn(Collection<Integer> skuIds) {
        return this.baseMapper.queryBySkuIdIn(skuIds);
    }
}

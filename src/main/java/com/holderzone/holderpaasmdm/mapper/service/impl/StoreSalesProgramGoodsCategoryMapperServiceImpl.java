package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.StoreSalesProgramGoodsCategoryMapper;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsCategoryMapperService;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsCategoryPO;
import org.springframework.stereotype.Service;

/**
 * Description: 门店销售方案商品分类表Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Service
public class StoreSalesProgramGoodsCategoryMapperServiceImpl extends ServiceImpl<StoreSalesProgramGoodsCategoryMapper,
        StoreSalesProgramGoodsCategoryPO>
        implements StoreSalesProgramGoodsCategoryMapperService {
}

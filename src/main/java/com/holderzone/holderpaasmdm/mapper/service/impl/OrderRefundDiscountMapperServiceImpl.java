package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.OrderRefundDiscountMapper;
import com.holderzone.holderpaasmdm.mapper.service.OrderRefundDiscountMapperService;
import com.holderzone.holderpaasmdm.model.po.OrderRefundDiscountPO;
import org.springframework.stereotype.Service;


/**
 * Description: 订单退款折扣Mapper接口实现类
 * Author: 向超
 * Date: 2025/01/14 15:35
 */
@Service
public class OrderRefundDiscountMapperServiceImpl extends ServiceImpl<OrderRefundDiscountMapper, OrderRefundDiscountPO>
        implements OrderRefundDiscountMapperService {
}

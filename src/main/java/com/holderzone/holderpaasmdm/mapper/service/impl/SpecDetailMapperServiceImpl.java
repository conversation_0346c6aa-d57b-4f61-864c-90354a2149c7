package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SpecDetailMapper;
import com.holderzone.holderpaasmdm.mapper.service.SpecDetailMapperService;
import com.holderzone.holderpaasmdm.model.po.SpecDetailPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 规格表 Mapper 接口实现类
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
@Service
public class SpecDetailMapperServiceImpl extends ServiceImpl<SpecDetailMapper, SpecDetailPO>
        implements SpecDetailMapperService {

    @Override
    public Map<Integer, SpecDetailPO> findBySpecIdIn(Collection<Integer> specIdList) {
        return this.lambdaQuery()
                .select(SpecDetailPO::getId, SpecDetailPO::getName, SpecDetailPO::getSort, SpecDetailPO::getSpecId)
                .in(SpecDetailPO::getSpecId, specIdList)
                .list().stream().collect(Collectors.toMap(SpecDetailPO::getId, Function.identity()));
    }
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsBrandMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsBrandMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsBrandExtendPO;
import com.holderzone.holderpaasmdm.model.po.GoodsBrandPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Description: 商品品牌Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class GoodsBrandMapperServiceImpl extends ServiceImpl<GoodsBrandMapper, GoodsBrandPO>
        implements GoodsBrandMapperService {
    @Override
    public List<GoodsBrandExtendPO> queryGoodsBrandExtendListByIdList(Collection<Integer> idList) {
        return baseMapper.queryGoodsBrandExtendListByIdList(idList);
    }
}

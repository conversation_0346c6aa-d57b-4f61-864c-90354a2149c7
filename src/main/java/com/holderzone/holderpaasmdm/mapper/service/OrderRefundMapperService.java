package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.bo.QueryOrderRefundListBO;
import com.holderzone.holderpaasmdm.model.po.OrderRefundPO;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * date 2024/12/18
 * @since 1.8
 */
public interface OrderRefundMapperService extends IService<OrderRefundPO> {
    /**
     * 查询退款订单列表
     *
     * @param params 查询条件
     * @return 退款订单列表
     */
    List<OrderRefundPO> queryOrderRefundList(QueryOrderRefundListBO params);
    /**
     * 查询退款订单数量
     *
     * @param params 查询条件
     * @return 退款订单数量
     */
    Integer queryOrderRefundListCount(QueryOrderRefundListBO params);

    /**
     * 查询退款订单异常数量
     *
     * @param params 查询条件
     * @return 退款订单异常数量
     */
    Integer queryOrderRefundExceptionNumber(QueryOrderRefundListBO params);

    /**
     * 根据关键字匹配订单商品的名称和条码查询订单退款列表
     *
     * @param keywords 关键字
     * @return 订单退款列表
     */
    List<String> queryOrderRefundNumberListByKeywordsForNameOrBarcode(String keywords);
}

package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsCategoryPO;

import java.util.List;

/**
 * Description: 门店商品分类Mapper接口
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
public interface StoreGoodsCategoryMapperService extends IService<StoreGoodsCategoryPO> {
    /**
     * 根据分类ID查询所有父级分类 (包括自身)
     *
     * @param category 分类ID
     * @return 父级分类列表
     */
    List<StoreGoodsCategoryPO> queryGenerationsVOListByIdList(List<Integer> category);
}

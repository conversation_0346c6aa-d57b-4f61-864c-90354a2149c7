package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.bo.BatchUpdateSellingPriceBO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPricePO;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Description: 门店销售方案中的商品时段价格表Mapper接口
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
public interface StoreSalesProgramGoodsPriceMapperService extends IService<StoreSalesProgramGoodsPricePO> {
    /**
     * 批量更新销售价格
     *
     * @param paramList 批量更新参数
     * @param timestamp 时间戳
     */
    void batchUpdateSellingPrice(List<BatchUpdateSellingPriceBO> paramList, Timestamp timestamp);

    /**
     * id集合查询数据信息
     *
     * @param ids id集合
     * @return 查询结果
     */
    Map<Integer, StoreSalesProgramGoodsPricePO> findMapByIdIn(Collection<Integer> ids);
}

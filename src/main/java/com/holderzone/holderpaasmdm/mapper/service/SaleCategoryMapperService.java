package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryPO;

import java.util.Collection;
import java.util.List;

/**
 * Description: 销售分组Mapper接口
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
public interface SaleCategoryMapperService extends IService<SaleCategoryPO> {
    /**
     * 根据销售分组ID查询所有父级（世代）
     *
     * @param idList 销售分组ID列表
     * @param channelId 渠道ID
     * @return 父级（世代）列表
     */
    List<SaleCategoryPO> queryGenerationsVOListByIdListAndChannelId(Collection<Integer> idList, Integer channelId);

    /**
     * 根据销售方案ID、渠道ID、店铺ID查询销售分组ID集合
     *
     * @param storeSalesProgramId 销售方案ID
     * @param channelId 渠道ID
     * @param storeId 店铺ID
     * @return 销售分组ID集合
     */
    List<Integer> querySaleCategoryIdBySaleProgramIdAndChannelIdAndStoreId(Integer storeSalesProgramId, Integer channelId, Integer storeId);

    /**
     * 渠道和门店id查询所有分类
     *
     * @param channel 渠道id
     * @param storeId 门店id
     * @return 查询结果
     */
    List<SaleCategoryPO> queryByStoreAndChannel(Integer channel, Integer storeId);
}

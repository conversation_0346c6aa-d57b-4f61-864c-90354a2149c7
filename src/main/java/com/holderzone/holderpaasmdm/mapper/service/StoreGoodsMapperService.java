package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.dto.SqlQueryGoodsByTagListDTO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Description: 门店商品表（SPU）Mapper接口
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
public interface StoreGoodsMapperService extends IService<StoreGoodsPO> {

    /**
     * 查询门店商品基础信息列表（可售商品）
     *
     * @param storeId                    门店ID
     * @param channelId                  渠道ID
     * @param barcode                    商品条码
     * @param storeGoodsIdList           店铺商品ID集合
     * @param goodsValuationMethodIdList 商品计价方式ID集合
     * @return 门店商品基础信息列表
     */
    List<StoreGoodsPO> queryStoreGoodsForAvailableForSaleWeighList(Integer storeId, Integer channelId,
                                                                   String barcode, List<Integer> storeGoodsIdList,
                                                                   List<Integer> goodsValuationMethodIdList);

    /**
     * 根据秤内自编码查询店铺商品信息
     *
     * @param storeId         店铺ID
     * @param scaleCustomCode 秤内自编码
     * @param scaleType       秤类型
     * @return 店铺商品信息
     */
    List<StoreGoodsPO> queryStoreGoodsByScaleCustomCode(Integer storeId, String scaleCustomCode, Integer scaleType);

    /**
     * 根据店铺商品ID集合和关键字查询门店商品基础信息列表
     *
     * @param idList              店铺商铺ID集合
     * @param keywords            关键字
     * @param storeSalesProgramId 销售方案ID
     * @return 门店商品基础信息列表
     */
    List<StoreGoodsPO> queryStoreGoodsByIdListAndKeywords(Collection<Integer> idList, String keywords, Integer storeSalesProgramId);

    /**
     * 根据编码查询门店商品信息(商品自编码、商品条码、秤内自编码)
     *
     * @param code      商品编码
     * @param storeId   门店ID
     * @param scaleType 秤类型
     * @return 门店商品信息
     */
    List<StoreGoodsPO> queryStoreGoodsByCode(String code, Integer storeId, Integer scaleType);

    /**
     * 查询所有商品，按照规格、时段分层查询
     *
     * @param allStoreSalesProgramGoodsIds             商品id集合
     * @param StoreSalesProgramIdList 策略id集合
     * @return 查询结果
     */
    List<SqlQueryGoodsByTagListDTO> queryAllSkuGoodsByTag(Collection<Integer> allStoreSalesProgramGoodsIds,
                                                          Collection<Integer> StoreSalesProgramIdList);

    /**
     * 商品id列表查询在售商品id
     *
     * @param storeGoodsIdList 商品id列表
     * @return 在售商品id
     */
    List<Integer> queryOnSaleIdsByIdIn(List<Integer> storeGoodsIdList);
}

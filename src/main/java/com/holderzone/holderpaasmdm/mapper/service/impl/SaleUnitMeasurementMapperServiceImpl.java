package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SaleUnitMeasurementMapper;
import com.holderzone.holderpaasmdm.mapper.service.SaleUnitMeasurementMapperService;
import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementPO;
import org.springframework.stereotype.Service;

/**
 * Description: 商品计量单位Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class SaleUnitMeasurementMapperServiceImpl extends ServiceImpl<SaleUnitMeasurementMapper,
        SaleUnitMeasurementPO> implements SaleUnitMeasurementMapperService {
}

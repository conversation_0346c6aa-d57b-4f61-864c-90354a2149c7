package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.OrderRefundMapper;
import com.holderzone.holderpaasmdm.mapper.service.OrderRefundMapperService;
import com.holderzone.holderpaasmdm.model.bo.QueryOrderRefundListBO;
import com.holderzone.holderpaasmdm.model.po.OrderRefundPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * date 2024/12/18
 * @since 1.8
 */
@Service
public class OrderRefundMapperServiceImpl extends ServiceImpl<OrderRefundMapper, OrderRefundPO>
        implements OrderRefundMapperService {
    @Override
    public List<OrderRefundPO> queryOrderRefundList(QueryOrderRefundListBO params) {
        return baseMapper.queryOrderRefundList(params);
    }

    @Override
    public Integer queryOrderRefundListCount(QueryOrderRefundListBO params) {
        return baseMapper.queryOrderRefundListCount(params);
    }

    @Override
    public Integer queryOrderRefundExceptionNumber(QueryOrderRefundListBO params) {
        return baseMapper.queryOrderRefundExceptionNumber(params);
    }

    @Override
    public List<String> queryOrderRefundNumberListByKeywordsForNameOrBarcode(String keywords) {
        return baseMapper.queryOrderRefundNumberListByKeywordsForNameOrBarcode(keywords);
    }
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SaleCategoryAndStoreGoodsMapper;
import com.holderzone.holderpaasmdm.mapper.service.SaleCategoryAndStoreGoodsMapperService;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryAndStoreGoodsPO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * Description: 销售分组和门店商品关系Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class SaleCategoryAndStoreGoodsMapperServiceImpl extends ServiceImpl<SaleCategoryAndStoreGoodsMapper,
        SaleCategoryAndStoreGoodsPO> implements SaleCategoryAndStoreGoodsMapperService {

    @Override
    public List<SaleCategoryAndStoreGoodsPO> querySaleCategoryByStoreGoodsIdListAndChannelId(Collection<Integer> storeGoodsIdList, Integer channelId) {
        return baseMapper.querySaleCategoryByStoreGoodsIdListAndChannelId(storeGoodsIdList, channelId);
    }

    @Override
    public List<SaleCategoryAndStoreGoodsPO> querySaleCategoryAndStoreGoodsIn(Collection<Integer> saleCategoryIds,
                                                                              Collection<Integer> storeGoodsIdList) {
        return this.lambdaQuery().in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, saleCategoryIds)
                .in(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, storeGoodsIdList)
                .list();
    }
}

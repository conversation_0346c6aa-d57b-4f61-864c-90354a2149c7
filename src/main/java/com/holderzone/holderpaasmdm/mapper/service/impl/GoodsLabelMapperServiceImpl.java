package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsLabelMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsLabelMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsLabelPO;
import org.springframework.stereotype.Service;


/**
 * Description: 商品标签Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Service
public class GoodsLabelMapperServiceImpl extends ServiceImpl<GoodsLabelMapper, GoodsLabelPO>
        implements GoodsLabelMapperService {
}

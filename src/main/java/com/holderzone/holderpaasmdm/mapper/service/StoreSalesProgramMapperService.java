package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;

import java.util.Collection;
import java.util.List;

/**
 * Description: 门店销售方案 Mapper 接口
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
public interface StoreSalesProgramMapperService extends IService<StoreSalesProgramPO> {
    /**
     * 根据店铺销售方案商品ID列表查询店铺销售方案列表
     *
     * @param storeSalesProgramGoodsIdList 店铺销售方案商品ID列表
     * @return 店铺销售方案列表
     */
    List<StoreSalesProgramPO> queryStoreSalesProgramByStoreSalesProgramGoodsIdList(Collection<Integer> storeSalesProgramGoodsIdList);
}

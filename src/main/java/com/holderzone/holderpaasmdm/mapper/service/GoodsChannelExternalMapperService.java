package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.GoodsChannelExternalPO;

/**
 * 商品渠道信息扩展表mapper service
 *
 * <AUTHOR>
 * @date 2025/6/9 11:02
 **/
public interface GoodsChannelExternalMapperService extends IService<GoodsChannelExternalPO> {

    /**
     * 门店商品id查询数据信息
     *
     * @param storeGoodsId 门店商品id
     * @param channelId    渠道id
     * @return 查询结果
     */
    GoodsChannelExternalPO findByStoreGoodsId(Integer storeGoodsId, Integer channelId);
}

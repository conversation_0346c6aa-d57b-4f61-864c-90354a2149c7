package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SpecMapper;
import com.holderzone.holderpaasmdm.mapper.service.SpecMapperService;
import com.holderzone.holderpaasmdm.model.po.SpecPO;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 规格表 Mapper 接口实现类
 * Author: 向超
 * Date: 2025/03/21 15:35
 */
@Service
public class SpecMapperServiceImpl extends ServiceImpl<SpecMapper, SpecPO>
        implements SpecMapperService {

    @Override
    public Map<Integer, SpecPO> findMapByStoreId(Integer storeId) {
        return this.lambdaQuery()
                .select(SpecPO::getId, SpecPO::getName, SpecPO::getSort)
                .eq(SpecPO::getStoreId, storeId)
                .list().stream().collect(Collectors.toMap(SpecPO::getId, Function.identity()));
    }
}

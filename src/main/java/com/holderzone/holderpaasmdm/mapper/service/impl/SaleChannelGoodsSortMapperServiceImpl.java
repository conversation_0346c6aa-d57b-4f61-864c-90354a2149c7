package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SaleChannelGoodsSortMapper;
import com.holderzone.holderpaasmdm.mapper.service.SaleChannelGoodsSortMapperService;
import com.holderzone.holderpaasmdm.model.po.SaleChannelGoodsSortPO;
import org.springframework.stereotype.Service;

/**
 * Description: 店铺渠道商品排序 Mapper 接口实现类
 * Author: 向超
 * Date: 2024/12/18 15:35
 */
@Service
public class SaleChannelGoodsSortMapperServiceImpl extends ServiceImpl<SaleChannelGoodsSortMapper, SaleChannelGoodsSortPO>
        implements SaleChannelGoodsSortMapperService {
}

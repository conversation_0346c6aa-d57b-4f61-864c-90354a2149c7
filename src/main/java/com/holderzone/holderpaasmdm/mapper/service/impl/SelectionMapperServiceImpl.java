package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.SelectionMapper;
import com.holderzone.holderpaasmdm.mapper.service.SelectionMapperService;
import com.holderzone.holderpaasmdm.model.po.SelectionPO;
import org.springframework.stereotype.Service;

/**
 * Description: 存放各种用于选择的列表、树结构数据Mapper接口实现类
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
@Service
public class SelectionMapperServiceImpl extends ServiceImpl<SelectionMapper, SelectionPO>
        implements SelectionMapperService {
}

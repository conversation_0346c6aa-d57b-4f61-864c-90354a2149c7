package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.OrdersMapper;
import com.holderzone.holderpaasmdm.mapper.service.OrdersMapperService;
import com.holderzone.holderpaasmdm.model.dto.StoreDailySettleDTO;
import com.holderzone.holderpaasmdm.model.po.OrdersPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
@Service
public class OrdersMapperServiceImpl extends ServiceImpl<OrdersMapper, OrdersPO> implements OrdersMapperService {

    @Override
    public List<OrdersPO> queryOrdersByConditions(StoreDailySettleDTO storeDailySettleDTO, Long companyId) {
        return this.baseMapper.queryOrdersByConditions(storeDailySettleDTO, companyId);
    }
}

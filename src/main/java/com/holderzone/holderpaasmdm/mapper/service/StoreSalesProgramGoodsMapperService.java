package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.bo.BatchUpdateSellingPriceBO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * Description: 门店销售方案商品表Mapper接口
 * Author: 向超
 * Date: 2024/11/14 15:35
 */
public interface StoreSalesProgramGoodsMapperService extends IService<StoreSalesProgramGoodsPO> {
    /**
     * 批量更新默认销售价格
     *
     * @param batchUpdateSellingPriceBOForDefaultList 批量更新销售价格BO
     * @param timestamp                               时间戳
     * @param allSalesProgramIdSet                    所有销售方案ID集合
     */
    void batchUpdateSellingPrice(List<BatchUpdateSellingPriceBO> batchUpdateSellingPriceBOForDefaultList,
                                 Timestamp timestamp, Set<Integer> allSalesProgramIdSet);

    /**
     * id查询数据map
     *
     * @param ids id集合
     * @return 查询结果
     */
    Map<Integer, StoreSalesProgramGoodsPO> findMapByIdIn(Collection<Integer> ids);

    /**
     * 销售id查询商品id
     *
     * @param programIdList 销售id
     * @return 商品id集合
     */
    List<Integer> queryGoodsIdsByProgramIdIn(List<Integer> programIdList);
}

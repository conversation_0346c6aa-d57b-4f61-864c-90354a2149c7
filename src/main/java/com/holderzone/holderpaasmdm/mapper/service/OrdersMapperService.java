package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.dto.StoreDailySettleDTO;
import com.holderzone.holderpaasmdm.model.po.OrdersPO;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
public interface OrdersMapperService extends IService<OrdersPO> {

    /**
     * 查询符合条件的订单记录
     */
    List<OrdersPO> queryOrdersByConditions(StoreDailySettleDTO storeDailySettleDTO, Long companyId);


}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.PriceAdjustmentOrderGoodsMapper;
import com.holderzone.holderpaasmdm.mapper.service.PriceAdjustmentOrderGoodsMapperService;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderGoodsPO;
import org.springframework.stereotype.Service;

/**
 * Description: 调价单商品信息 Mapper 接口实现类
 * Author: 向超
 * Date: 2024/12/17 15:35
 */
@Service
public class PriceAdjustmentOrderGoodsMapperServiceImpl extends ServiceImpl<PriceAdjustmentOrderGoodsMapper,
        PriceAdjustmentOrderGoodsPO> implements PriceAdjustmentOrderGoodsMapperService {
}

package com.holderzone.holderpaasmdm.mapper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holderpaasmdm.model.po.GoodsBrandExtendPO;
import com.holderzone.holderpaasmdm.model.po.GoodsBrandPO;

import java.util.Collection;
import java.util.List;

/**
 * Description: 商品品牌Mapper接口
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
public interface GoodsBrandMapperService extends IService<GoodsBrandPO> {
    /**
     * 根据ID查询商品品牌扩展信息
     *
     * @param idList 商品品牌ID列表
     * @return 商品品牌扩展信息列表
     */
    List<GoodsBrandExtendPO> queryGoodsBrandExtendListByIdList(Collection<Integer> idList);
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.PlatformSalesChannelMapper;
import com.holderzone.holderpaasmdm.mapper.service.PlatformSalesChannelMapperService;
import com.holderzone.holderpaasmdm.model.po.PlatformSalesChannelPO;
import org.springframework.stereotype.Service;

/**
 * Description: 销售渠道表Mapper接口实现类
 * Author: 向超
 * Date: 2024/12/05 15:36
 */
@Service
public class PlatformSalesChannelMapperServiceImpl extends ServiceImpl<PlatformSalesChannelMapper, PlatformSalesChannelPO>
        implements PlatformSalesChannelMapperService {
}

package com.holderzone.holderpaasmdm.mapper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holderpaasmdm.mapper.GoodsChannelExternalMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsChannelExternalMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsChannelExternalPO;
import org.springframework.stereotype.Service;

/**
 * 商品渠道信息扩展表mapper service实现类
 *
 * <AUTHOR>
 * @date 2025/6/9 11:02
 **/
@Service
public class GoodsChannelExternalMapperServiceImpl extends ServiceImpl<GoodsChannelExternalMapper, GoodsChannelExternalPO>
        implements GoodsChannelExternalMapperService {

    @Override
    public GoodsChannelExternalPO findByStoreGoodsId(Integer storeGoodsId, Integer channelId) {
        return this.lambdaQuery()
                .eq(GoodsChannelExternalPO::getStoreGoodsId, storeGoodsId)
                .eq(GoodsChannelExternalPO::getChannelId, channelId)
                .one();
    }
}

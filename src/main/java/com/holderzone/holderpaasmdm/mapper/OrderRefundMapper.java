package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.bo.QueryOrderRefundListBO;
import com.holderzone.holderpaasmdm.model.po.OrderRefundPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024年12月18日
 */
@Mapper
public interface OrderRefundMapper extends BaseMapper<OrderRefundPO> {

    /**
     * 查询订单退款列表
     *
     * @param params 查询条件
     * @return 订单退款列表
     */
    List<OrderRefundPO> queryOrderRefundList(@Param("params") QueryOrderRefundListBO params);

    /**
     * 查询订单退款异常数量
     *
     * @param params 查询条件
     * @return 订单退款异常数量
     */
    Integer queryOrderRefundExceptionNumber(@Param("params") QueryOrderRefundListBO params);

    /**
     * 查询订单退款列表数量
     *
     * @param params 查询条件
     * @return 订单退款列表数量
     */
    Integer queryOrderRefundListCount(@Param("params") QueryOrderRefundListBO params);

    /**
     * 根据关键字匹配订单商品的名称和条码查询订单退款列表
     *
     * @param keywords 关键字
     * @return 订单退款列表
     */
    List<String> queryOrderRefundNumberListByKeywordsForNameOrBarcode(@Param("keywords") String keywords);
}

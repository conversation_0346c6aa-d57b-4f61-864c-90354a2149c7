package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.po.GoodsBrandExtendPO;
import com.holderzone.holderpaasmdm.model.po.GoodsBrandPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * Description: 商品品牌表Mapper
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Mapper
public interface GoodsBrandMapper extends BaseMapper<GoodsBrandPO> {
    /**
     * 根据ID查询商品品牌扩展信息
     *
     * @param idList 商品品牌ID列表
     * @return 商品品牌扩展信息列表
     */
    List<GoodsBrandExtendPO> queryGoodsBrandExtendListByIdList(@Param("idList") Collection<Integer> idList);
}

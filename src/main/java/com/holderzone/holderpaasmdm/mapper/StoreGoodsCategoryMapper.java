package com.holderzone.holderpaasmdm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsCategoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description: 门店商品分类表Mapper
 * Author: 向超
 * Date: 2024/11/22 15:35
 */
@Mapper
public interface StoreGoodsCategoryMapper extends BaseMapper<StoreGoodsCategoryPO> {

    /**
     * 根据分类ID查询所有父级分类 (包括自身)
     *
     * @param category 分类ID
     * @return 父级分类列表
     */
    List<StoreGoodsCategoryPO> queryGenerationsVOListByIdList(@Param("category") List<Integer> category);
}

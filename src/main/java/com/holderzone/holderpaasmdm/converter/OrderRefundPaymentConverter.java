package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.OrderRefundPaymentPO;
import com.holderzone.holderpaasmdm.model.vo.OrderRefundPaymentVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * Description: 订单退款支付记录Converter
 * Author: 向超
 * Date: 2025/01/14 16:20
 */
@Mapper(componentModel = "spring")
public interface OrderRefundPaymentConverter {
    OrderRefundPaymentVO toOrderRefundPaymentVO(OrderRefundPaymentPO orderRefundPaymentPO);
    List<OrderRefundPaymentVO> toOrderRefundPaymentVOList(List<OrderRefundPaymentPO> orderRefundPaymentPOList);
}

package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.dto.StoreGoodsAvailableForSaleAdjustmentPriceDTO;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderGoodsChangeInfoPO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsAvailableForSaleAdjustmentPriceVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;


/**
 * Description: 调价单商品调价信息表（SPU）Converter
 * Author: 向超
 * Date: 2024/11/14 16:20
 */
@Mapper(componentModel = "spring")
public interface PriceAdjustmentOrderGoodsChangeInfoConverter {
    PriceAdjustmentOrderGoodsChangeInfoPO toPriceAdjustmentOrderGoodsChangeInfoPO(
            StoreGoodsAvailableForSaleAdjustmentPriceDTO.TimeSectionAdjustmentDTO timeSectionAdjustmentDTO);
    List<PriceAdjustmentOrderGoodsChangeInfoPO> toPriceAdjustmentOrderGoodsChangeInfoPOList(
            List<StoreGoodsAvailableForSaleAdjustmentPriceDTO.TimeSectionAdjustmentDTO> timeSectionAdjustmentDTOList);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void toStoreGoodsAvailableForSaleAdjustmentPriceVO(PriceAdjustmentOrderGoodsChangeInfoPO priceAdjustmentOrderGoodsChangeInfoPO,
                                                       @MappingTarget StoreGoodsAvailableForSaleAdjustmentPriceVO storeGoodsAvailableForSaleAdjustmentPriceVO);
}

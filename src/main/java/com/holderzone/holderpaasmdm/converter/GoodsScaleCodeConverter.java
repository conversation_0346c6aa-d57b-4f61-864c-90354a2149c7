package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.GoodsScaleCodePO;
import com.holderzone.holderpaasmdm.model.vo.GoodsScaleCodeVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsBaseExtendVO;
import org.mapstruct.Mapper;
import java.util.List;


/**
 * Description: 店铺商品与秤内码的对应关系Converter
 * Author: 向超
 * Date: 2024/12/12 16:20
 */
@Mapper(componentModel = "spring")
public interface GoodsScaleCodeConverter {
    StoreGoodsBaseExtendVO.GoodsScaleCodeVO toStoreGoodsScaleCodeVO(GoodsScaleCodePO goodsScaleCodePO);
    List<StoreGoodsBaseExtendVO.GoodsScaleCodeVO> toStoreGoodsScaleCodeVOList(List<GoodsScaleCodePO> goodsScaleCodePOList);

    GoodsScaleCodeVO toGoodsScaleCodeVO(GoodsScaleCodePO goodsScaleCodePO);
    List<GoodsScaleCodeVO> toGoodsScaleCodeVOList(List<GoodsScaleCodePO> goodsScaleCodePOList);
}

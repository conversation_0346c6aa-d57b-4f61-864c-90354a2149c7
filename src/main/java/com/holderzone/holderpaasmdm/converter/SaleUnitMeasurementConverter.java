package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementPO;
import com.holderzone.holderpaasmdm.model.vo.SaleUnitMeasurementVO;
import org.mapstruct.Mapper;
import java.util.List;


/**
 * Description: 单位 Converter
 * Author: 向超
 * Date: 2025/02/06 16:20
 */
@Mapper(componentModel = "spring")
public interface SaleUnitMeasurementConverter {
    SaleUnitMeasurementVO toSaleUnitMeasurementVO(SaleUnitMeasurementPO saleUnitMeasurementPO);
    List<SaleUnitMeasurementVO> toSaleUnitMeasurementVOList(List<SaleUnitMeasurementPO> saleUnitMeasurementPOList);
}

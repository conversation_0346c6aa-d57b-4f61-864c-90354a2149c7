package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.dto.StoreGoodsAvailableForSaleAdjustmentPriceDTO;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderGoodsPO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsAvailableForSaleAdjustmentPriceVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;


/**
 * Description: 调价单商品信息表（SPU）Converter
 * Author: 向超
 * Date: 2024/11/14 16:20
 */
@Mapper(componentModel = "spring")
public interface PriceAdjustmentOrderGoodsConverter {
    @Mapping(target = "goodsId", source = "storeGoodsId")
    @Mapping(target = "goodsName", source = "storeGoodsName")
    PriceAdjustmentOrderGoodsPO toPriceAdjustmentOrderGoodsPO(
            StoreGoodsAvailableForSaleAdjustmentPriceDTO storeGoodsAvailableForSaleAdjustmentPriceDTO);

    PriceAdjustmentOrderGoodsPO.SaleCategoryNode toSaleCategoryNode(
            StoreGoodsAvailableForSaleAdjustmentPriceDTO.SaleCategoryDTO saleCategoryDTO);
    List<PriceAdjustmentOrderGoodsPO.SaleCategoryNode> toSaleCategoryNodeList(
            List<StoreGoodsAvailableForSaleAdjustmentPriceDTO.SaleCategoryDTO> saleCategoryDTOList);

    @Mapping(target = "saleCategoryInfoList", source = "saleCategoryNode")
    @Mapping(target = "saleCategoryNameList", source = "saleCategoryName")
    @Mapping(target = "specRelationList", source = "goodsSpecification")
    @Mapping(target = "storeGoodsName", source = "goodsName")
    @Mapping(target = "storeGoodsId", source = "goodsId")
    @Mapping(target = "createdAt", source = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", source = "updatedAt", ignore = true)
    void toStoreGoodsAvailableForSaleAdjustmentPriceVO(PriceAdjustmentOrderGoodsPO priceAdjustmentOrderGoodsPO,
                                @MappingTarget StoreGoodsAvailableForSaleAdjustmentPriceVO storeGoodsAvailableForSaleAdjustmentPriceVO);

    List<PriceAdjustmentOrderGoodsPO.GoodsSpecification> toGoodsSpecificationList(
            List<StoreGoodsAvailableForSaleAdjustmentPriceDTO.StoreGoodsSpecRelationDTO> storeGoodsSpecRelationList);
}

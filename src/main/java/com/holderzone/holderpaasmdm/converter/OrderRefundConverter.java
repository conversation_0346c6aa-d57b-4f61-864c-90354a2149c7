package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.bo.QueryOrderRefundListBO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundExceptionNumberDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundListDTO;
import com.holderzone.holderpaasmdm.model.po.OrderRefundPO;
import com.holderzone.holderpaasmdm.model.vo.OrderRefundExtendVO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;


/**
 * Description: 订单退款Converter
 * Author: 向超
 * Date: 2025/01/14 16:20
 */
@Mapper(componentModel = "spring")
public interface OrderRefundConverter {
    @Mapping(target = "keywords", source = "keywords", qualifiedByName = {"adapterLikeQuery"})
    @Mapping(target = "offset", expression = "java(getOffset(queryOrderRefundListDTO.getPage(), queryOrderRefundListDTO.getLimit()))")
    QueryOrderRefundListBO toQueryOrderRefundListBO(QueryOrderRefundListDTO queryOrderRefundListDTO);

    @Mapping(target = "keywords", source = "keywords", qualifiedByName = {"adapterLikeQuery"})
    QueryOrderRefundListBO toQueryOrderRefundListBO(QueryOrderRefundExceptionNumberDTO queryOrderRefundExceptionNumberDTO);

    OrderRefundExtendVO toOrderRefundExtendVO(OrderRefundPO orderRefundPO);
    List<OrderRefundExtendVO> toOrderRefundExtendVOList(List<OrderRefundPO> orderRefundPOList);

    /**
     * 组装偏移量
     *
     * @param page 页
     * @param limit 页大小
     * @return 偏移量
     */
    @Named("getOffset")
    default int getOffset(int page, int limit) {
        return (page - 1) * limit;
    }

    /**
     * 转换器方法，适配模糊查询关键字
     *
     * @param keywords 模糊查询关键字
     * @return 转换后的模糊查询关键字
     */
    @Named("adapterLikeQuery")
    default String converterCombinationType(String keywords) {
        if (StringUtils.isBlank(keywords)) {
            return null;
        }
        return "%" + keywords + "%";
    }
}

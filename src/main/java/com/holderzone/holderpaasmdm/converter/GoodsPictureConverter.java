package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.UploadFilesPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsPictureVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * Description: 商品图片Converter
 * Author: 向超
 * Date: 2024/12/12 16:20
 */
@Mapper(componentModel = "spring")
public interface GoodsPictureConverter {
    GoodsPictureVO toGoodsPictureVO(UploadFilesPO uploadFilesPO);
    List<GoodsPictureVO> toGoodsPictureVOList(List<UploadFilesPO> uploadFilesPOList);
}

package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.GoodsChannelExternalPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsChannelExternalVO;
import org.mapstruct.Mapper;

/**
 * 商品渠道信息扩展表对象转换
 *
 * <AUTHOR>
 * @date 2025/6/9 12:05
 **/
@Mapper(componentModel = "spring")
public interface GoodsChannelExternalConverter {

    GoodsChannelExternalVO toGoodsChannelExternalVO(GoodsChannelExternalPO goodsChannelExternalPO);
}

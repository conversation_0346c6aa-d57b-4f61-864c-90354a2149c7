package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.OrderRefundItemPO;
import com.holderzone.holderpaasmdm.model.vo.OrderRefundItemVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * Description: 订单退款商品Converter
 * Author: 向超
 * Date: 2025/01/14 16:20
 */
@Mapper(componentModel = "spring")
public interface OrderRefundItemConverter {
    OrderRefundItemVO toOrderRefundItemVO(OrderRefundItemPO orderRefundItemPO);
    List<OrderRefundItemVO> toOrderRefundItemVOList(List<OrderRefundItemPO> orderRefundItemPOList);
}

package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.enumeraton.GoodsInventoryProperty;
import com.holderzone.holderpaasmdm.enumeraton.GoodsValuationMethod;
import com.holderzone.holderpaasmdm.enumeraton.StoreComboType;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsFeatures;
import com.holderzone.holderpaasmdm.model.dto.SqlQueryGoodsByTagListDTO;
import com.holderzone.holderpaasmdm.model.dto.VerifyStoreGoodsOnSaleDTO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import com.holderzone.holderpaasmdm.model.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collections;
import java.util.List;


/**
 * Description: 门店商品表（SPU）Converter
 * Author: 向超
 * Date: 2024/11/14 16:20
 */
@Mapper(componentModel = "spring")
public interface StoreGoodsConverter {
    @Mapping(source = "comboType", target = "comboTypeName", qualifiedByName = {"converterCombinationType"})
    @Mapping(source = "goodsFeatures", target = "goodsFeaturesName", qualifiedByName = {"converterGoodsFeatures"})
    @Mapping(source = "valuationMethod", target = "valuationMethodName", qualifiedByName = {"converterValuationMethod"})
    @Mapping(source = "inventoryProperty", target = "inventoryPropertyName", qualifiedByName = {"converterInventoryProperty"})
    StoreGoodsOnSaleExtendVO toStoreGoodsOnSaleExtendVO(StoreGoodsPO storeGoodsPO);
    List<StoreGoodsOnSaleExtendVO> toStoreGoodsOnSaleExtendVOList(List<StoreGoodsPO> storeGoodsPOList);

    @Mapping(source = "comboType", target = "comboTypeName", qualifiedByName = {"converterCombinationType"})
    @Mapping(source = "goodsFeatures", target = "goodsFeaturesName", qualifiedByName = {"converterGoodsFeatures"})
    @Mapping(source = "valuationMethod", target = "valuationMethodName", qualifiedByName = {"converterValuationMethod"})
    @Mapping(source = "inventoryProperty", target = "inventoryPropertyName", qualifiedByName = {"converterInventoryProperty"})
    StoreGoodsBaseVO toStoreGoodsBaseVO(StoreGoodsPO storeGoodsPO);

    @Mapping(source = "goodsSaleName", target = "goodsName")
    VerifyStoreGoodsOnSaleVO.StoreGoodsBaseInfo toStoreGoodsBaseInfo(VerifyStoreGoodsOnSaleDTO.GoodsInfo goodsInfo);
    List<VerifyStoreGoodsOnSaleVO.StoreGoodsBaseInfo> toStoreGoodsBaseInfoList(List<VerifyStoreGoodsOnSaleDTO.GoodsInfo> goodsInfoList);

    @Mapping(source = "comboType", target = "comboTypeName", qualifiedByName = {"converterCombinationType"})
    @Mapping(source = "id", target = "goodsId")
    VerifyStoreGoodsOnSaleVO.StoreGoodsExtendInfo toStoreGoodsExtendInfo(StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO);

    GoodsComboTypeVO toGoodsComboTypeVO(StoreComboType storeComboType);
    List<GoodsComboTypeVO> toGoodsComboTypeVOList(List<StoreComboType> storeComboTypeList);

    @Mapping(source = "valuationMethod", target = "valuationMethodName", qualifiedByName = {"converterValuationMethod"})
    StoreGoodsAvailableForSalePOSExtendVO toStoreGoodsAvailableForSalePOSExtendVO(StoreGoodsPO storeGoodsPO);
    List<StoreGoodsAvailableForSalePOSExtendVO> toStoreGoodsAvailableForSalePOSExtendVOList(List<StoreGoodsPO> storeGoodsPOList);

    @Mapping(source = "comboType", target = "comboTypeName", qualifiedByName = {"converterCombinationType"})
    @Mapping(source = "goodsFeatures", target = "goodsFeaturesName", qualifiedByName = {"converterGoodsFeatures"})
    @Mapping(source = "valuationMethod", target = "valuationMethodName", qualifiedByName = {"converterValuationMethod"})
    @Mapping(source = "inventoryProperty", target = "inventoryPropertyName", qualifiedByName = {"converterInventoryProperty"})
    StoreGoodsExtendVO toStoreGoodsExtendVO(StoreGoodsPO storeGoodsPO);
    List<StoreGoodsExtendVO> toStoreGoodsExtendVOList(List<StoreGoodsPO> storeGoodsPOList);

    @Mapping(source = "comboType", target = "comboTypeName", qualifiedByName = {"converterCombinationType"})
    @Mapping(source = "goodsFeatures", target = "goodsFeaturesName", qualifiedByName = {"converterGoodsFeatures"})
    @Mapping(source = "valuationMethod", target = "valuationMethodName", qualifiedByName = {"converterValuationMethod"})
    @Mapping(source = "inventoryProperty", target = "inventoryPropertyName", qualifiedByName = {"converterInventoryProperty"})
    StoreGoodsPrintLabelExtendVO toStoreGoodsPrintLabelExtendVO(StoreGoodsPO storeGoodsPO);

    List<StoreGoodsPrintLabelExtendVO> toStoreGoodsPrintLabelExtendSkuVOList(List<SqlQueryGoodsByTagListDTO> dtoList);

    /**
     * 组合类型转换
     *
     * @param comboTypeCode 组合类型
     * @return 组合类型名称
     */
    @Named("converterCombinationType")
    default String converterCombinationType(Integer comboTypeCode) {
        return StoreComboType.getNameFromCode(comboTypeCode);
    }

    /**
     * 商品特征转换
     *
     * @param goodsFeatures 商品特征
     * @return 商品特征名称
     */
    @Named("converterGoodsFeatures")
    default List<String> converterGoodsFeatures(List<Integer> goodsFeatures) {
        if (goodsFeatures == null) {
            return Collections.emptyList();
        }
        return goodsFeatures.stream().map(StoreGoodsFeatures::getNameFromCode).toList();
    }

    /**
     * 计价方式转换
     *
     * @param valuationMethod 计价方式
     * @return 计价方式名称
     */
    @Named("converterValuationMethod")
    default String converterValuationMethod(Integer valuationMethod) {
        return GoodsValuationMethod.getNameFromCode(valuationMethod);
    }

    /**
     * 库存属性转换
     *
     * @param inventoryProperty 库存属性
     * @return 库存属性名称
     */
    @Named("converterInventoryProperty")
    default String converterInventoryProperty(Integer inventoryProperty) {
        return GoodsInventoryProperty.getNameFromCode(inventoryProperty);
    }
}

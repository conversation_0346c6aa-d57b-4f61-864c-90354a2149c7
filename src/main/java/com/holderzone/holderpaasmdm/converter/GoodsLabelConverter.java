package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.GoodsLabelPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsLabelVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * Description: 商品标签Converter
 * Author: 向超
 * Date: 2024/11/26 16:20
 */
@Mapper(componentModel = "spring")
public interface GoodsLabelConverter {
    GoodsLabelVO toGoodsLabelVO(GoodsLabelPO goodsLabelPO);
    List<GoodsLabelVO> toGoodsLabelVOList(List<GoodsLabelPO> goodsLabelPOList);
}

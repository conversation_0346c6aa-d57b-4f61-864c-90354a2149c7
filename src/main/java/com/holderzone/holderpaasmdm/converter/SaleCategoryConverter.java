package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.SaleCategoryPO;
import com.holderzone.holderpaasmdm.model.vo.SaleCategoryTreeVO;
import com.holderzone.holderpaasmdm.model.vo.SaleCategoryVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsBaseExtendVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsOnSaleExtendVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * Description: 销售分组Converter
 * Author: 向超
 * Date: 2024/11/26 16:20
 */
@Mapper(componentModel = "spring")
public interface SaleCategoryConverter {
    SaleCategoryVO toSaleCategoryTreeVO(SaleCategoryPO saleCategoryPO);
    List<SaleCategoryTreeVO> toSaleCategoryTreeVOList(List<SaleCategoryPO> saleCategoryPOList);
    List<SaleCategoryVO> toSaleCategoryVOList(List<SaleCategoryPO> saleCategoryPOList);


    StoreGoodsOnSaleExtendVO.SaleCategoryVO toStoreGoodsOnSaleExtendVOSaleCategoryVO(SaleCategoryPO saleCategoryPO);
    List<StoreGoodsOnSaleExtendVO.SaleCategoryVO> toStoreGoodsOnSaleExtendVOSaleCategoryVOList(List<SaleCategoryPO> saleCategoryPOList);

    StoreGoodsBaseExtendVO.SaleCategoryVO toStoreGoodsExtendVOSaleCategoryVO(SaleCategoryPO saleCategoryPO);
    List<StoreGoodsBaseExtendVO.SaleCategoryVO> toStoreGoodsExtendVOSaleCategoryVOList(List<SaleCategoryPO> saleCategoryPOList);
}

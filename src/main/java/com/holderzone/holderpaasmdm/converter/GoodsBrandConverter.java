package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.GoodsBrandExtendPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsBrandVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * Description: 商品品牌Converter
 * Author: 向超
 * Date: 2024/11/26 16:20
 */
@Mapper(componentModel = "spring")
public interface GoodsBrandConverter {
    GoodsBrandVO toGoodsBrandVO(GoodsBrandExtendPO goodsBrandExtendPO);
    List<GoodsBrandVO> toGoodsBrandVOList(List<GoodsBrandExtendPO> goodsBrandExtendPOList);
}

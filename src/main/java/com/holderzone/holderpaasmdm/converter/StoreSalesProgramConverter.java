package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendVO;
import com.holderzone.holderpaasmdm.model.vo.StoreSalesProgramExtendVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;


/**
 * Description: 门店销售方案 Converter
 * Author: 向超
 * Date: 2024/11/14 16:20
 */
@Mapper(componentModel = "spring")
public interface StoreSalesProgramConverter {
    @Mapping(source = "timeSection", target = "sectionList")
    @Mapping(source = "storeSalesProgramName", target = "storeSalesProgramName")
    StoreSalesProgramExtendVO toStoreSalesProgramExtendVO(StoreSalesProgramPO storeSalesProgramPO);
    List<StoreSalesProgramExtendVO> toStoreSalesProgramExtendVOList(List<StoreSalesProgramPO> storeSalesProgramPOList);

    StoreGoodsExtendVO.SectionVO toStoreGoodsExtendVOSectionVO(StoreSalesProgramPO.Section section);
    List<StoreGoodsExtendVO.SectionVO> toStoreGoodsExtendVOSectionVOList(List<StoreSalesProgramPO.Section> sectionList);
}

package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.StoreGoodsCategoryPO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsCategoryVO;
import org.mapstruct.Mapper;
import java.util.List;


/**
 * Description: 门店商品分类表 Converter
 * Author: 向超
 * Date: 2025/02/08 16:20
 */
@Mapper(componentModel = "spring")
public interface StoreGoodsCategoryConverter {
    StoreGoodsCategoryVO toStoreGoodsCategoryVO(StoreGoodsCategoryPO storeGoodsCategoryPO);
    List<StoreGoodsCategoryVO> toStoreGoodsCategoryVOList(List<StoreGoodsCategoryPO> storeGoodsCategoryPOList);
}

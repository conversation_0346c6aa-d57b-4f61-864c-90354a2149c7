package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.po.OrderRefundDiscountPO;
import com.holderzone.holderpaasmdm.model.vo.OrderRefundDiscountVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * Description: 订单退款折扣Converter
 * Author: 向超
 * Date: 2025/01/14 16:20
 */
@Mapper(componentModel = "spring")
public interface OrderRefundDiscountConverter {
    OrderRefundDiscountVO toOrderRefundDiscountVO(OrderRefundDiscountPO orderRefundDiscountPO);
    List<OrderRefundDiscountVO> toOrderRefundDiscountVOList(List<OrderRefundDiscountPO> orderRefundDiscountPOList);
}

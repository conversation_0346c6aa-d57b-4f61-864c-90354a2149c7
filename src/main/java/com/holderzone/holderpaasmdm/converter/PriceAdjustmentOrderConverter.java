package com.holderzone.holderpaasmdm.converter;

import com.holderzone.holderpaasmdm.model.dto.StoreGoodsAvailableForSaleAdjustmentPriceDTO;
import com.holderzone.holderpaasmdm.model.po.PriceAdjustmentOrderPO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsAvailableForSaleAdjustmentPriceVO;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;


/**
 * Description: 调价单表（SPU）Converter
 * Author: 向超
 * Date: 2024/11/14 16:20
 */
@Mapper(componentModel = "spring")
public interface PriceAdjustmentOrderConverter {
    PriceAdjustmentOrderPO toPriceAdjustmentOrderPO(StoreGoodsAvailableForSaleAdjustmentPriceDTO storeGoodsAvailableForSaleAdjustmentPriceDTO);

    StoreGoodsAvailableForSaleAdjustmentPriceVO toStoreGoodsAvailableForSaleAdjustmentPriceVO(PriceAdjustmentOrderPO priceAdjustmentOrderPO);
    List<StoreGoodsAvailableForSaleAdjustmentPriceVO> toStoreGoodsAvailableForSaleAdjustmentPriceVOList(Collection<PriceAdjustmentOrderPO> priceAdjustmentOrderPOList);
}

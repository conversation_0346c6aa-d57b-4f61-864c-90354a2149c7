package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.model.dto.QueryStoreSalesProgramListDTO;
import com.holderzone.holderpaasmdm.model.vo.StoreSalesProgramExtendVO;

import java.util.List;

/**
 * Description: 店铺销售方案 Service
 * Author: 向超
 * Date: 2024/11/14 15:33
 */
public interface StoreSalesProgramService {
    /**
     * 查询店铺销售方案列表
     *
     * @param queryStoreSalesProgramListDTO 店铺销售方案查询条件
     * @return 店铺销售方案列表
     */
    List<StoreSalesProgramExtendVO> queryStoreSalesProgramList(QueryStoreSalesProgramListDTO queryStoreSalesProgramListDTO);
}

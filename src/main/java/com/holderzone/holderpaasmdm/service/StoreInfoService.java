package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.dto.QueryStoreChannelBindDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsSkuInventoryDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStorePosGoodsAvailableForSaleDetailDTO;
import com.holderzone.holderpaasmdm.model.vo.StoreChannelBindVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendChannelVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsSkuInventoryVO;

import java.util.List;

/**
 * 门店信息service
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
public interface StoreInfoService {

    /**
     * 第三方店铺查询绑定关系
     *
     * @param queryStoreChannelBindDTO 查询数据
     * @return 查询结果
     */
    StoreChannelBindVO queryBindStore(QueryStoreChannelBindDTO queryStoreChannelBindDTO);

    /**
     * 查询商品详情（对外）
     *
     * @param queryDTO 查询对象
     * @return 查询结果
     */
    StoreGoodsExtendChannelVO queryDetails(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO);

    /**
     * 查询商品详情批量（对外）
     *
     * @param queryDTO 查询对象
     * @return 传结果
     */
    List<StoreGoodsExtendChannelVO> queryDetailsBatch(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO);

    /**
     * 查询sku库存数据
     *
     * @param inventoryDTO 查询对象
     * @return 查询结果
     */
    List<StoreGoodsSkuInventoryVO> querySkuInventory(QueryStoreGoodsSkuInventoryDTO inventoryDTO);

    /**
     * 运费模版id查询绑定的商品数量
     *
     * @param freightTemplateId 运维模版id
     * @return 查询结果
     */
    Integer countByFreightTemplateId(Long freightTemplateId);
}

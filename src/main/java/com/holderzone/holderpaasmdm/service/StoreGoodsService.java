package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.model.dto.GenerateBatchStoreGoodsPluCodeDTO;
import com.holderzone.holderpaasmdm.model.dto.GenerateStoreGoodsPluCodeDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleMemberListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSalePosListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleWeighDetailDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleWeighListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsOnSaleDetailDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsOnSaleFilterListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsOnSaleListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStorePosGoodsAvailableForSaleDetailDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStorePosGoodsAvailableForSaleListDTO;
import com.holderzone.holderpaasmdm.model.dto.StoreGoodsAvailableForSaleAdjustmentPriceDTO;
import com.holderzone.holderpaasmdm.model.dto.VerifyStoreGoodsAvailableForSaleScaleCodeDTO;
import com.holderzone.holderpaasmdm.model.dto.VerifyStoreGoodsOnSaleDTO;
import com.holderzone.holderpaasmdm.model.vo.*;

import java.util.List;

/**
 * Description: 门店营销方案 Service
 * Author: 向超
 * Date: 2024/11/14 15:33
 */
public interface StoreGoodsService {
    /**
     * 根据店铺Id 和 渠道ID 查询该门店在售商品列表
     *
     * @param queryStoreGoodsOnSaleListDTO 查询条件
     * @return 门店在售商品列表
     */
    PageRespVO<StoreGoodsOnSaleExtendVO> queryStoreGoodsOnSaleListPage(QueryStoreGoodsOnSaleListDTO queryStoreGoodsOnSaleListDTO);

    /**
     * 根据店铺ID、渠道ID和(店铺商品ID或者条码或者自编码)查询在售商品详情
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询条件
     * @return 门店在售商品详情
     */
    StoreGoodsOnSaleExtendVO queryStoreGoodsOnSaleDetail(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO);

    /**
     * 根据店铺ID、渠道ID和(店铺商品ID或者条码或者自编码)查询在售商品详情
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询条件
     * @return 门店在售商品详情
     */
    StoreGoodsOnSaleExtendVO queryStoreGoodsOnSaleDetailV2(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO);

    /**
     * 快速结账 - 校验商品信息（包含上|下架状态、价格、库存等信息）
     * 当前版本暂不考虑库存问题
     *
     * @param verifyStoreGoodsOnSaleDTO 商品信息
     * @return 在售商品列表
     */
    VerifyStoreGoodsOnSaleVO verifyStoreGoodsOnSaleInfo(VerifyStoreGoodsOnSaleDTO verifyStoreGoodsOnSaleDTO);

    /**
     * 根据店铺ID、渠道ID、商品类型以及销售分组ID查询可售商品列表
     *
     * @param queryStoreGoodsAvailableForSaleMemberListDTO 查询条件
     * @return 可售商品列表
     */
    PageRespVO<StoreGoodsBaseVO> queryStoreGoodsAvailableForSaleMemberListPage(
            QueryStoreGoodsAvailableForSaleMemberListDTO queryStoreGoodsAvailableForSaleMemberListDTO);

    /**
     * 查询店铺商品组合类型列表
     *
     * @return 店铺商品组合类型列表
     */
    List<GoodsComboTypeVO> queryStoreGoodsComboTypeList();

    /**
     * 根据店铺ID、渠道ID以及销售分组ID查询可售商品列表 (POS)
     *
     * @param queryStoreGoodsAvailableForSalePosListDTO 查询条件
     * @return 可售商品列表
     */
    PageRespVO<StoreGoodsAvailableForSalePOSExtendVO> queryStoreGoodsAvailableForSalePosListPage(
            QueryStoreGoodsAvailableForSalePosListDTO queryStoreGoodsAvailableForSalePosListDTO);

    /**
     * 根据店铺ID、渠道ID查询可售商品列表 (传秤)
     *
     * @param queryStoreGoodsAvailableForSalePosListDTO 查询条件
     * @return 可售商品列表
     */
    PageRespVO<StoreGoodsExtendVO> queryStoreGoodsAvailableForSaleWeighListPage(
            QueryStoreGoodsAvailableForSaleWeighListDTO queryStoreGoodsAvailableForSalePosListDTO);

    /**
     * 生成店铺商品秤内码
     *
     * @param generateStoreGoodsPluCodeDTO 生成店铺商品秤内码参数
     */
    GoodsScaleCodeVO generateStoreGoodsPluCode(GenerateStoreGoodsPluCodeDTO generateStoreGoodsPluCodeDTO);

    /**
     * 批量生成店铺商品秤内码
     *
     * @param generateBatchStoreGoodsPluCodeDTO 批量生成店铺商品秤内码参数
     */
    List<GoodsScaleCodeVO> generateStoreGoodsPluCodeBatch(GenerateBatchStoreGoodsPluCodeDTO generateBatchStoreGoodsPluCodeDTO);

    /**
     * 传秤校验商品状态 - 商品秤内码、秤内自编码、计价方法
     *
     * @param verifyStoreGoodsAvailableForSaleScaleCodeDTO 校验参数
     * @return 校验结果
     */
    VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleScaleCode(
            VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO);

    /**
     * 根据店铺ID、渠道ID查询可售商品详情 (传秤)
     *
     * @param queryStoreGoodsAvailableForSaleWeighDetailDTO 查询条件
     * @return 可售商品详情
     */
    StoreGoodsExtendVO queryStoreGoodsAvailableForSaleWeighDetail(
            QueryStoreGoodsAvailableForSaleWeighDetailDTO queryStoreGoodsAvailableForSaleWeighDetailDTO);

    /**
     * 查询店铺可售商品分页列表
     *
     * @param queryStoreGoodsAvailableForSaleListDTO 查询条件
     * @return 可售商品分页列表
     */
    PageRespVO<StoreGoodsPrintLabelExtendVO> queryStoreGoodsAvailableForSaleTimeSectionListPage(
            QueryStoreGoodsAvailableForSaleListDTO queryStoreGoodsAvailableForSaleListDTO);

    /**
     * 可售商品调价
     *
     * @param storeGoodsAvailableForSaleAdjustmentPriceDTO 调价参数
     */
    void storeGoodsAvailableForSalePriceAdjustment(StoreGoodsAvailableForSaleAdjustmentPriceDTO storeGoodsAvailableForSaleAdjustmentPriceDTO);

    /**
     * 查询店铺可售商品调价记录列表
     *
     * @param queryDTO 查询条件
     * @return 调价记录列表
     */
    PageRespVO<StoreGoodsAvailableForSaleAdjustmentPriceVO> queryStoreGoodsAvailableForSalePriceAdjustmentList(
            QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO queryDTO);

    /**
     * 传秤导出校验商品状态 - 计价方法
     *
     * @param verifyStoreGoodsAvailableForSaleScaleCodeDTO 校验参数
     * @return 校验结果
     */
    VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleExportScaleCode(
            VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO);

    /**
     * 查询POS渠道的可售商品列表
     *
     * @param queryDTO 查询条件
     * @return 可售商品列表
     */
    PageRespVO<StoreGoodsExtendVO> queryStorePosGoodsAvailableForSaleListPage(QueryStorePosGoodsAvailableForSaleListDTO queryDTO);

    /**
     * 查询POS渠道的可售商品详情
     *
     * @param queryDTO 查询条件
     * @return 可售商品详情
     */
    StoreGoodsExtendVO queryStorePosGoodsAvailableForSaleDetail(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO);

    /**
     * 根据spu编码、商品自编码、条码查询在售商品详情列表
     *
     * @param queryStoreGoodsOnSaleFilterListDTO 查询条件
     * @return 在售商品详情列表
     */
    List<StoreGoodsOnSaleExtendVO> queryStoreGoodsOnSaleFilterList(QueryStoreGoodsOnSaleFilterListDTO queryStoreGoodsOnSaleFilterListDTO);
}

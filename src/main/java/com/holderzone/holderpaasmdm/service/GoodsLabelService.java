package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.vo.GoodsLabelVO;

import java.util.List;

/**
 * 商品标签service
 *
 * <AUTHOR>
 * @date 2025/6/17 10:50
 **/
public interface GoodsLabelService {

    /**
     * 查询所有标签数据
     *
     * @return 查询结果
     */
    List<GoodsLabelVO> findAll();

    /**
     * id查询详情数据
     *
     * @param id id
     * @return 查询结果
     */
    GoodsLabelVO findById(Integer id);
}

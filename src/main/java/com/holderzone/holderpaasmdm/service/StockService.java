package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.model.dto.QueryInboundListDTO;
import com.holderzone.holderpaasmdm.model.vo.InboundBaseVO;

import java.util.List;

/**
 * Description: 存库 Service
 * Author: 向超
 * Date: 2025/01/16 15:33
 */
public interface StockService {

    /**
     * 查询入库单列表
     *
     * @param queryInboundListDTO 入库单列表查询条件
     * @return 入库单列表
     */
    List<InboundBaseVO> queryInboundsList(QueryInboundListDTO queryInboundListDTO);
}

package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendVO;

import java.util.List;

/**
 * SKU商品表 service
 *
 * <AUTHOR>
 * @date 2025/4/18 9:37
 **/
public interface GoodsPackageSkuService {

    /**
     * 根据关键字过滤条形码数据
     *
     * @param storeGoodsExtendVOList 商品数据信息
     * @param keywords               关键字
     * @return 包含条形码关键字的商品id
     */
    List<Integer> filterBarcodeByKeywords(List<StoreGoodsExtendVO> storeGoodsExtendVOList, String keywords);
}

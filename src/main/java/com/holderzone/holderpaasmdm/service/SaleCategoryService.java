package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.model.dto.*;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.model.vo.SaleCategoryTreeVO;

import java.util.List;

/**
 * Description: 门店营销方案 Service
 * Author: 向超
 * Date: 2024/11/14 15:33
 */
public interface SaleCategoryService {
    /**
     * 查询在售商品对应的销售分组树
     *
     * @param querySaleCategoryOnSaleTreeDTO 查询条件
     * @return 销售分组树
     */
    List<SaleCategoryTreeVO> querySaleCategoryOnSaleTree(QuerySaleCategoryOnSaleTreeDTO querySaleCategoryOnSaleTreeDTO);

    /**
     * 查询所有的销售分组树
     *
     * @param querySaleCategoryTreeDTO 查询条件
     * @param hasAll                   是否有全部
     * @return 销售分组树
     */
    List<SaleCategoryTreeVO> querySaleCategoryTree(QuerySaleCategoryTreeDTO querySaleCategoryTreeDTO, boolean hasAll);

    /**
     * 查询可售商品对应的销售分组树
     *
     * @param querySaleCategoryAvailableForSaleTreeDTO 查询条件
     * @return 销售分组树
     */
    List<SaleCategoryTreeVO> querySaleCategoryAvailableForSaleTree(QuerySaleCategoryAvailableForSaleTreeDTO querySaleCategoryAvailableForSaleTreeDTO);

    /**
     * 查询可售商品对应的销售分组树 (传秤)
     *
     * @param querySaleCategoryAvailableForSaleWeighTreeDTO 查询条件
     * @return 销售分组树
     */
    List<SaleCategoryTreeVO> querySaleCategoryAvailableForSaleWeighTree(
            QuerySaleCategoryAvailableForSaleWeighTreeDTO querySaleCategoryAvailableForSaleWeighTreeDTO);

    /**
     * 查询POS渠道的可售商品对应的销售分组树
     *
     * @param queryDTO 查询条件
     * @return 销售分组树
     */
    List<SaleCategoryTreeVO> querySaleCategoryPosGoodsAvailableForSaleTree(QuerySaleCategoryPosGoodsAvailableForSaleTreeDTO queryDTO);

    /**
     * 分页查询一级类目数据
     *
     * @param pageDTO 查询参数
     * @return 查询结果
     */
    PageRespVO<SaleCategoryTreeVO> queryCategoryPage(QuerySaleCategoryPageDTO pageDTO);

    /**
     * 查询销售分组详情数据
     *
     * @param querySaleCategoryTreeDTO 查询数据信息
     * @return 查询结果
     */
    SaleCategoryTreeVO queryCategoryDetails(QuerySaleCategoryTreeDTO querySaleCategoryTreeDTO);
}

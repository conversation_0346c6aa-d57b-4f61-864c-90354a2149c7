package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.model.dto.QueryStoreSalesProgramGoodsCategoryTreeDTO;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;

/**
 * Description: 门店营销方案 Service
 * Author: 向超
 * Date: 2024/11/14 15:33
 */
public interface StoreSalesProgramGoodsCategoryService {
    PageRespVO<Void> getTestList(QueryStoreSalesProgramGoodsCategoryTreeDTO queryStoreSalesProgramGoodsCategoryTreeDTO);
}

package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleListDTO;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsPrintLabelExtendVO;

/**
 * 门店营销方案2
 *
 * <AUTHOR>
 * @date 2025/4/15 15:39
 **/
public interface StoreGoods2Service {

    /**
     * 查询店铺可售商品分页列表
     *
     * @param queryStoreGoodsAvailableForSaleListDTO 查询条件
     * @return 可售商品分页列表
     */
    PageRespVO<StoreGoodsPrintLabelExtendVO> queryStoreGoodsAvailableForSaleTimeSectionListPage(
            QueryStoreGoodsAvailableForSaleListDTO queryStoreGoodsAvailableForSaleListDTO);
}

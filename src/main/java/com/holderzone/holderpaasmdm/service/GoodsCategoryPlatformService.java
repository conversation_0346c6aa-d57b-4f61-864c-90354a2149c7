package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.dto.QueryCategoryPlatformPageDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryCategoryPlatformTreeDTO;
import com.holderzone.holderpaasmdm.model.vo.GoodsCategoryPlatformTreeVO;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendChannelVO;

import java.util.List;

/**
 * 平台类目信息
 *
 * <AUTHOR>
 * @date 2025/6/3 9:38
 **/
public interface GoodsCategoryPlatformService {

    /**
     * 查询平台类目树信息
     *
     * @param queryCategoryPlatformTreeDTO 查询参数
     * @return 查询结果
     */
    List<GoodsCategoryPlatformTreeVO> queryGoodsCategoryPlatformTree(QueryCategoryPlatformTreeDTO queryCategoryPlatformTreeDTO);

    /**
     * 分页查询一级类目数据
     *
     * @param pageDTO 查询参数
     * @return 查询结果
     */
    PageRespVO<GoodsCategoryPlatformTreeVO> queryGoodsCategoryPlatformPage(QueryCategoryPlatformPageDTO pageDTO);

    /**
     * 查询层级数据信息
     *
     * @param id 分类id
     * @return 查询结果
     */
    StoreGoodsExtendChannelVO.CategoryLevelVO queryGoodsCategoryPlatform(Integer id);

    /**
     * 查询单个详情数据
     *
     * @param queryCategoryPlatformTreeDTO 查询分组数据
     * @return 查询结果
     */
    GoodsCategoryPlatformTreeVO findTreeVoById(QueryCategoryPlatformTreeDTO queryCategoryPlatformTreeDTO);
}

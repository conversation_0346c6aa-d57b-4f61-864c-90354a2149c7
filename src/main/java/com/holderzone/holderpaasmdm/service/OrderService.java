package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.dto.OrderCreateDTO;
import com.holderzone.holderpaasmdm.model.dto.OrderStatusChangeDTO;
import com.holderzone.holderpaasmdm.model.dto.StoreDailySettleDTO;
import com.holderzone.holderpaasmdm.model.vo.OrderCreateVO;
import com.holderzone.holderpaasmdm.model.vo.StoreDailySettleVO;

import java.util.List;

/**
 * desc 门店信息
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
public interface OrderService {

    /**
     * 门店日结信息
     * @param companyId 企业ID
     * @param storeDailySettleDTO 查询参数
     */
    List<StoreDailySettleVO> storeDailySettle(Long companyId, StoreDailySettleDTO storeDailySettleDTO);

    /**
     * 创建订单
     * @param orderCreateDTO 订单信息
     * @return 订单编号
     */
    OrderCreateVO createOrder(OrderCreateDTO orderCreateDTO);

    /**
     * 订单状态变更
     * @param orderStatusChangeDTO 订单状态变更信息
     */
    Boolean changeOrderStatus(OrderStatusChangeDTO orderStatusChangeDTO, Long companyId);

}

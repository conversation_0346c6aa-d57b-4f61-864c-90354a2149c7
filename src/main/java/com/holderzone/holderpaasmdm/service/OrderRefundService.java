package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundDetailsDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundExceptionNumberDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundListDTO;
import com.holderzone.holderpaasmdm.model.vo.OrderRefundExtendVO;
import com.holderzone.holderpaasmdm.model.vo.PageRespBaseVO;

/**
 * Description: 订单退款 Service
 * Author: 向超
 * Date: 2025/01/14 15:33
 */
public interface OrderRefundService {
    /**
     * 查询订单退款列表
     *
     * @param queryOrderRefundListDTO 订单退款列表查询条件
     * @return 订单退款列表
     */
    PageRespBaseVO<OrderRefundExtendVO> queryOrderRefundList(QueryOrderRefundListDTO queryOrderRefundListDTO);

    /**
     * 查询订单退款异常数量
     *
     * @param queryOrderRefundExceptionNumberDTO 订单退款异常数量查询条件
     * @return 订单退款异常数量
     */
    Integer queryOrderRefundExceptionNumber(QueryOrderRefundExceptionNumberDTO queryOrderRefundExceptionNumberDTO);

    /**
     * 查询订单退款详情
     *
     * @param queryOrderRefundDetailsDTO 订单退款详情查询条件
     * @return 订单退款详情
     */
    OrderRefundExtendVO queryOrderRefundDetails(QueryOrderRefundDetailsDTO queryOrderRefundDetailsDTO);
}

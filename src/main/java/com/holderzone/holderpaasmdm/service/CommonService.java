package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpand;
import com.holderzone.holderpaasmdm.model.bo.QueryStoreGoodsBaseListForOnSaleBO;
import com.holderzone.holderpaasmdm.model.bo.QueryStoreGoodsForAvailableForSaleWeighListBO;
import com.holderzone.holderpaasmdm.model.bo.QueryStorePosGoodsAvailableForSaleBO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecPO;
import com.holderzone.holderpaasmdm.model.po.SpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.SpecPO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import com.holderzone.holderpaasmdm.model.vo.*;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Description: 公共 Service
 * Author: 向超
 * Date: 2024/11/25 15:33
 */
public interface CommonService {
    /**
     * 查询门店商品基础信息列表（在售商品）
     * 注意：
     *  1、销售方案分为了默认和自定义两种，但存在优先级问题： 自定义方案 > 默认方案
     *  2、默认方案是全天24小时的，即默认方案是随时生效的
     *  3、自定义方案是有设置时间段的，即自定义方案是设置时间段内生效的
     *  估：处理逻辑时，需要先判断是否有自定义方案，如果有自定义方案，再判断是否在自定义方案的时间段内生效，如果在，则使用自定义方案，否则使用默认方案
     *  尤其是销售价格、销售名称、销售图片等需要优先考虑自定义方案，如果没有自定义方案，则使用默认方案
     *
     * @param queryStoreGoodsBaseListForOnSaleBO 查询参数
     * @return 门店商品基础信息列表
     */
    PageRespVO<StoreGoodsOnSaleExtendVO> queryStoreGoodsBaseListForOnSale(
            QueryStoreGoodsBaseListForOnSaleBO queryStoreGoodsBaseListForOnSaleBO);

    /**
     * 根据销售分组ID查询所有的关联店铺商品ID（需要查询销售分组及其子级分组关联的商品）
     *
     * @param categoryIdList 销售分组ID列表
     * @return 所有关联店铺商品ID
     */
    List<Integer> queryStoreGoodsIdListBySaleCategoryIdListAndChild(List<Integer> categoryIdList);

    /**
     * 对可售商品进行排序，排序规则如下：
     * 1、如果商品有排序的数据，则按照数据库中的排序数据来进行排序，sort值越小越靠前
     * 2、如果sort值相同，则按照创建时间倒序来进行排序
     * 3、如果商品没有排序的数据，则按照创建时间倒序来进行排序
     *
     * @param storeId 店铺ID
     * @param channelId 渠道ID
     * @param storeGoodsOnSaleExtendVOList 可售商品列表
     * @return 排序后的可售商品列表
     */
    <T extends StoreGoodsBaseVO> List<T> sortStoreGoodsBaseVOList(
            Integer storeId, Integer channelId, List<T> storeGoodsOnSaleExtendVOList);

    /**
     * 组装商品的全量规格信息
     *
     * @param storeGoodsOnSaleExtendVO   在售商品信息
     * @param storeGoodsIdAndGoodsSpecPOListMap 店铺商品ID和商品规格信息的映射
     * @param goodsSpecIdAndGoodsSpecDetailInfoMap 商品规格ID和商品规格详情信息的映射
     * @param specIdAndSpecInfoMap 商品规格ID和商品规格信息的映射
     * @param specDetailIdAndInfoMap 商品规格详情ID和商品规格详情信息的映射
     */
    void assembleStoreGoodsSpecList(StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                    Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap,
                                    Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap,
                                    Map<Integer, SpecPO> specIdAndSpecInfoMap,
                                    Map<Integer, SpecDetailPO> specDetailIdAndInfoMap);

    /**
     * 根据 StoreGoodsSpecRelationVO 排序
     * 排序规则：
     * 1. 比较 goodsSpecSort，升序排序；
     * 2. 如果 goodsSpecSort 相等，则比较 goodsSpecId，降序排序；
     *
     * @param specDetailList 待排序的列表，类型为 StoreGoodsSpecRelationVO 或其子类
     */
    <T extends StoreGoodsSpecRelationVO> void sortStoreGoodsSpecList(List<T> specDetailList);

    /**
     * 根据 StoreGoodsSpecAndDetailsVO 内部的 storeGoodsSpecRelationList 排序
     * 排序规则：
     * 1. 依次比较 storeGoodsSpecRelationList 中每个元素的 goodsSpecDetailSort，升序排序；
     * 2. 如果 goodsSpecDetailSort 相等，则比较 goodsSpecDetailId，降序排序；
     * 3. 如果当前比较的元素都相等，则继续比较下一个元素；
     * 4. 如果其中一个列表比较完毕，则列表长度较短的排前面（可根据业务需求调整）。
     *
     * @param specList 待排序的列表，类型为 StoreGoodsSpecAndDetailsVO 或其子类
     */
    <T extends StoreGoodsSpecAndDetailsVO> void sortStoreGoodsSpecAndDetailsList(List<T> specList);

    /**
     * 组装店铺商品封面ID和商品封面信息的映射
     *
     * @param storeGoodsCoverIdSet 店铺商品封面ID集合
     * @return 商品封面ID和商品封面信息的映射
     */
    Map<Integer, GoodsPictureVO> assembleCoverIdAndPictureVOMap(Set<Integer> storeGoodsCoverIdSet);

    /**
     * 根据店铺ID和渠道ID查询出默认销售方案 以及  自定义销售方案和当前时间段对应的销售方案的时间段序号的映射关系Pair
     * 结构如下：
     * Pair(defaultStoreSalesProgramPO, Pair(customStoreSalesProgramPO, customStoreSalesProgramTimeSerialNumber))
     *
     * @param storeId 门店ID
     * @param channelId 渠道ID
     * @return 默认销售方案 以及  自定义销售方案和当前时间段对应的销售方案的时间段序号的映射关系Pair
     */
    Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> queryDefaultSalesProgramAndCustomSalesProgramAndTimeSerialNumberPair(
            Integer storeId, Integer channelId);

    /**
     * 根据店铺ID和渠道ID查询出店铺的(传秤即：计重、计数)可售商品列表
     *
     * @param queryStoreGoodsForAvailableForSaleWeighListBO 查询参数
     * @return 店铺的(传秤即：计重、计数)可售商品列表
     */
    List<StoreGoodsPO> queryStoreGoodsForAvailableForSaleWeighList(
            QueryStoreGoodsForAvailableForSaleWeighListBO queryStoreGoodsForAvailableForSaleWeighListBO);

    /**
     * 组装商品名称、销售价格、商品图片 (根据销售方案来组装)，规则如下：
     * 根据当前时间取销售策略中最近的售价。
     * 1、若当前时间在某个时段销售策略内的时段定价内，则显示当前时段策略单中的对应时段的售价，售卖名称也是，售卖图片也是；
     * 2、若当前时间没有对应的时段策略单，则向当前时间之后查今日最近的一个时段策略单，读取售价，售卖名称也是，售卖图片也是；
     * 3、若在今日当前时间之后都没有时段策略单了，则取全时段策略单中的售价，售卖名称也是，售卖图片也是；
     * 4、如果此商品不在全时段策略单中，那就取离当前时间往前最近的一个策略单，取对应的售价，售卖名称也是，售卖图片也是；
     *
     * @param storeGoodsExtendVOList 商品列表
     * @param storeId 门店ID
     * @param channelId 渠道ID
     */
    void assembleGoodsNameAndSalePriceAndPictureForWeighList(List<StoreGoodsExtendVO> storeGoodsExtendVOList,
                                                             Integer storeId, Integer channelId);

    /**
     * 组装商品名称、销售价格、商品图片 (根据销售方案来组装)，规则如下：
     * 根据当前时间取销售策略中最近的售价。
     * 1、若当前时间在某个时段销售策略内的时段定价内，则显示当前时段策略单中的对应时段的售价，售卖名称也是，售卖图片也是；
     * 2、若当前时间没有对应的时段策略单，则向当前时间之后查今日最近的一个时段策略单，读取售价，售卖名称也是，售卖图片也是；
     * 3、若在今日当前时间之后都没有时段策略单了，则取全时段策略单中的售价，售卖名称也是，售卖图片也是；
     * 4、如果此商品不在全时段策略单中，那就取离当前时间往前最近的一个策略单，取对应的售价，售卖名称也是，售卖图片也是；
     *
     * @param storeGoodsExtendVO 商品详情
     * @param storeId 门店ID
     * @param channelId 渠道ID
     */
    void assembleGoodsNameAndSalePriceAndPictureForWeigh(StoreGoodsExtendVO storeGoodsExtendVO,
                                                             Integer storeId, Integer channelId);

    /**
     * 组装商品扩展信息
     *
     * @param storeGoodsExtendVOList 商品列表
     * @param storeGoodsExpandList   商品扩展信息列表
     * @param storeId                店铺ID
     * @param channelId              渠道ID
     * @param scaleType              秤的品牌
     */
    void assembleGoodsExpandInfo(List<StoreGoodsExtendVO> storeGoodsExtendVOList, List<StoreGoodsExpand> storeGoodsExpandList,
                                 Integer storeId, Integer channelId, Integer scaleType);

    /**
     * 组装商品扩展信息(打印标签)
     *
     * @param storeGoodsPrintLabelExtendVOList 商品列表
     * @param storeGoodsExpandList   商品扩展信息列表
     * @param storeId                店铺ID
     * @param channelId              渠道ID
     * @param scaleType              秤的品牌
     */
    void assembleGoodsExpandInfoForPrintLabel(List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList,
                                              List<StoreGoodsExpand> storeGoodsExpandList,
                                              Integer storeId, Integer channelId, Integer scaleType);

    /**
     * 根据关键字过滤商品列表, 关键字匹配商品售卖名称、拼音、商品自编码
     *
     * @param storeGoodsExtendVOList 商品列表
     * @param keywords              关键字
     */
    void filterStoreGoodsExtendVOListByKeywordsForSaleNameAndPinyinAndCustomCode(
            List<StoreGoodsExtendVO> storeGoodsExtendVOList, String keywords);

    /**
     * 根据销售分组ID过滤商品列表 (不包含其子分组)
     *
     * @param storeGoodsPOList 商品列表
     * @param saleCategoryIdList 销售分组ID列表
     */
    void filterStoreGoodsListBySaleCategoryIdListOneself(List<StoreGoodsPO> storeGoodsPOList, List<Integer> saleCategoryIdList);

    /**
     * 根据订单号查询入库单号信息
     *
     * @param orderRefundNumberList 订单退货单号列表
     * @return 入库单号信息
     */
    String queryOrderRefundInboundNumberInfo(List<String> orderRefundNumberList);

    /**
     * 查询销售渠道 POS ID
     *
     * @return POS ID
     */
    Integer querySaleChannelPOSId();

    /**
     * 查询POS渠道的可售商品列表
     *
     * @param queryBO 查询参数
     * @param defaultStoreSalesProgramPO 默认销售方案
     * @param storeGoodsIdAndSalesProgramGoodsListMap 商品ID和信息映射
     *
     * @return POS ID
     */
    List<StoreGoodsExtendVO> queryStorePosGoodsAvailableForSaleList(QueryStorePosGoodsAvailableForSaleBO queryBO,
                                                                    StoreSalesProgramPO defaultStoreSalesProgramPO,
                                                                    Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsListMap);
}

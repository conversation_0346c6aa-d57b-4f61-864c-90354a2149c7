package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.vo.GoodsPictureVO;

import java.util.List;
import java.util.Map;

/**
 * 资源文件service
 *
 * <AUTHOR>
 * @date 2025/6/6 16:18
 **/
public interface UploadFilesService {

    /**
     * id查询文件信息
     *
     * @param coverIds id集合
     * @return 查询结果
     */
    Map<Integer, GoodsPictureVO> findFileByIdIn(List<Integer> coverIds);

    /**
     * id查询资源信息
     *
     * @param id id
     * @return 查询结果
     */
    GoodsPictureVO findById(Integer id);

    /**
     * id查询资源信息
     *
     * @param coverIds id
     * @return 查询结果
     */
    List<GoodsPictureVO> findByIdIn(List<Integer> coverIds);
}

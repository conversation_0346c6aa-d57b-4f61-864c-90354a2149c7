package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.converter.GoodsPictureConverter;
import com.holderzone.holderpaasmdm.mapper.service.UploadFilesMapperService;
import com.holderzone.holderpaasmdm.model.po.UploadFilesPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsPictureVO;
import com.holderzone.holderpaasmdm.service.UploadFilesService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资源文件service实现类
 *
 * <AUTHOR>
 * @date 2025/6/6 16:18
 **/
@Service
@RequiredArgsConstructor
public class UploadFilesServiceImpl implements UploadFilesService {

    private final UploadFilesMapperService uploadFilesMapperService;
    private final GoodsPictureConverter goodsPictureConverter;

    @Override
    public Map<Integer, GoodsPictureVO> findFileByIdIn(List<Integer> coverIds) {
        Map<Integer, GoodsPictureVO> returnMap = new HashMap<>();
        if (coverIds.isEmpty()) {
            return returnMap;
        }
        // 查询资源数据
        List<UploadFilesPO> filesPOList = uploadFilesMapperService.listByIds(coverIds);
        if (filesPOList.isEmpty()) {
            return returnMap;
        }
        List<GoodsPictureVO> pictureVOS = goodsPictureConverter.toGoodsPictureVOList(filesPOList);
        pictureVOS.forEach(pictureVO -> {returnMap.put(pictureVO.getId(), pictureVO);});
        return returnMap;
    }

    @Override
    public GoodsPictureVO findById(Integer id) {
        UploadFilesPO uploadFilesPO = uploadFilesMapperService.getById(id);
        if (Objects.nonNull(uploadFilesPO)) {
            return goodsPictureConverter.toGoodsPictureVO(uploadFilesPO);
        }
        return null;
    }

    @Override
    public List<GoodsPictureVO> findByIdIn(List<Integer> coverIds) {
        List<UploadFilesPO> uploadFilesPOList = uploadFilesMapperService.listByIds(coverIds);
        return goodsPictureConverter.toGoodsPictureVOList(uploadFilesPOList);
    }
}

package com.holderzone.holderpaasmdm.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.holderzone.holderpaasmdm.common.constant.UrlPathConstants;
import com.holderzone.holderpaasmdm.common.utils.DistributedIdGenerator;
import com.holderzone.holderpaasmdm.enumeraton.OrderStatusEnum;
import com.holderzone.holderpaasmdm.enumeraton.OrderSyncInventoryRecordFlagEnum;
import com.holderzone.holderpaasmdm.enumeraton.OrderSyncInventoryRecordTypeEnum;
import com.holderzone.holderpaasmdm.mapper.service.*;
import com.holderzone.holderpaasmdm.model.dto.OrderCreateDTO;
import com.holderzone.holderpaasmdm.model.dto.OrderOutboundCreateDTO;
import com.holderzone.holderpaasmdm.model.dto.OrderStatusChangeDTO;
import com.holderzone.holderpaasmdm.model.dto.StoreDailySettleDTO;
import com.holderzone.holderpaasmdm.model.po.*;
import com.holderzone.holderpaasmdm.model.vo.OrderCreateVO;
import com.holderzone.holderpaasmdm.model.vo.StoreDailySettleVO;
import com.holderzone.holderpaasmdm.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desc 订单信息
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderServiceImpl implements OrderService {

    private final OrdersMapperService ordersMapperService;

    private final OrderPaymentMapperService orderPaymentMapperService;

    private final OrderRefundPaymentMapperService orderRefundPaymentMapperService;

    private final OrderItemMapperService orderItemMapperService;

    private final RedissonClient redissonClient;

    private final DistributedIdGenerator distributedIdGenerator;

    private  final OrderSyncInventoryRecordMapperService orderSyncInventoryRecordMapperService;

    private final RetryTemplate retryTemplate;

    private final RestTemplate restTemplate;

    // 出库 扣减库存接口域名
    @Value("${outbound.create-order.host}")
    private String outBoundCreateOrderHost;

    private final StoreGoodsMapperService storeGoodsMapperService;


    /**
     * 门店日结信息
     * @param companyId 企业ID
     * @param storeDailySettleDTO 查询参数
     * @return 门店日结信息集合
     */
    @Override
    public List<StoreDailySettleVO> storeDailySettle(Long companyId, StoreDailySettleDTO storeDailySettleDTO) {
        List<StoreDailySettleVO> storeDailySettleVOList = new ArrayList<>();
        for (Long storeId : storeDailySettleDTO.getStoreIdList()) {
            StoreDailySettleVO storeDailySettleVO = new StoreDailySettleVO();
            // 门店的总订单数 不区分状态，查出全部状态的订单
            List<OrdersPO> orderList = ordersMapperService.queryOrdersByConditions(storeDailySettleDTO, companyId);
            // 门店id
            storeDailySettleVO.setStoreId(storeId);
            // 汇总门店的订单号
            Set<String> orderNumberSet = orderList.stream().map(OrdersPO::getOrderNumber)
                    .collect(Collectors.toSet());
            // 门店订单号集合为空，跳过本次循环
            if (CollectionUtils.isEmpty(orderNumberSet)) {
                continue;
            }
            // 门店订单总数
            storeDailySettleVO.setSalesOrderCount(orderList.size());
            // 查询门店订单实付总金额
            BigDecimal salesTotalActualReceipt = orderPaymentMapperService.lambdaQuery()
                     // 门店订单号集合
                    .in(OrderPaymentPO::getOrderNumber, orderNumberSet)
                     // 支付成功的
                    .eq(OrderPaymentPO::getIsPaySuccess, Boolean.TRUE)
                    // 汇总订单支付表记录的支付金额
                    .list().stream().map(OrderPaymentPO::getPaymentAmount)
                    // 过滤掉实付金额为空的数据
                    .filter(StringUtils::isNotEmpty)
                    // 元素类型转换：String转为BigDecimal
                    .map(BigDecimal::new)
                     // 计算总和
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    // 保留两位小数，四舍五入
                    .setScale(2, RoundingMode.HALF_UP);
            // 订单实付金额
            storeDailySettleVO.setSalesTotalActualReceipt(salesTotalActualReceipt);
            // 查询门店订单实际退款金额
            BigDecimal salesTotalActualRefund = orderRefundPaymentMapperService.lambdaQuery()
                    // 门店订单号集合
                    .in(OrderRefundPaymentPO::getOrderNumber, orderNumberSet)
                    // 退款成功的
                    .eq(OrderRefundPaymentPO::getIsRefundSuccess, Boolean.TRUE)
                    // 汇总退款金额 退款支付金额
                    .list().stream().map(OrderRefundPaymentPO::getRefundPaymentAmount)
                    // 过滤掉实退金额为空的数据
                    .filter(StringUtils::isNotEmpty)
                    // 元素类型转换：String转为BigDecimal
                    .map(BigDecimal::new)
                    // 计算总和
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    // 保留两位小数，四舍五入
                    .setScale(2, RoundingMode.HALF_UP);
            // 订单实际退款金额
            storeDailySettleVO.setSalesTotalActualRefund(salesTotalActualRefund);
            // 营业实收：实付金额 - 实退金额
            BigDecimal businessActualReceipt = salesTotalActualReceipt.subtract(salesTotalActualRefund);
            storeDailySettleVO.setBusinessActualReceipt(businessActualReceipt);
            // 门店日结信息汇总
            storeDailySettleVOList.add(storeDailySettleVO);
        }

        return storeDailySettleVOList;
    }

    /**
     * 创建订单
     * @param orderCreateDTO 订单信息
     * @return 订单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCreateVO createOrder(OrderCreateDTO orderCreateDTO) {

        OrdersPO ordersPO = assemblyOrder(orderCreateDTO);
        // 生成订单号  // 前缀固定为ZT
        String orderNumber = generateIdOrderNumber("ZT");
        ordersPO.setOrderNumber(orderNumber);
        // 1.创建订单信息
        ordersMapperService.save(ordersPO);
        // 订单id
        Integer orderId = ordersPO.getId();
        // 重新设置record_id
        ordersPO.setRecordId(orderId);
        ordersMapperService.updateById(ordersPO);

        // 2. 订单支付信息
        List<OrderPaymentPO> orderPaymentPOList = assemblyOrderPaymentList(orderCreateDTO,
                orderCreateDTO.getPayments(), orderNumber, orderId);
        orderPaymentMapperService.saveBatch(orderPaymentPOList);

        // 3. 订单明细
        // 传入的订单明细信息
        List<OrderItemPO> orderItemList = assemblyOrderItemList(orderCreateDTO, orderId, orderNumber);
        orderItemMapperService.saveBatch(orderItemList);

        // 返回信息封装
        OrderCreateVO orderCreateVO = new OrderCreateVO();
        orderCreateVO.setOrderNumber(orderNumber);
        return orderCreateVO;
    }

    @Override
    public Boolean changeOrderStatus(OrderStatusChangeDTO orderStatusChangeDTO, Long companyId) {
        // 查询订单信息
        OrdersPO ordersPO = ordersMapperService.getOne(Wrappers.<OrdersPO>lambdaQuery()
                .eq(OrdersPO::getOrderNumber, orderStatusChangeDTO.getOrderCode()));
        if (Objects.isNull(ordersPO)) {
            log.error("订单不存在，订单编号：{}", orderStatusChangeDTO.getOrderCode());
            return false;
        }

        // 支付成功(成功，扣减库存)
        if (Boolean.TRUE.equals(orderStatusChangeDTO.getPayStatus())) {
            // 1. 扣减库存
            Boolean executeResult = reduceInventory(orderStatusChangeDTO, ordersPO, companyId);
            // 库存扣减失败，不继续后面的订单状态修改操作了
            if (Boolean.FALSE.equals(executeResult)) {
                log.error("库存扣减失败，订单号：{}", orderStatusChangeDTO.getOrderCode());
                return Boolean.FALSE;
            }

            // 更新订单和支付明细状态
            updateOrderAndOrderPaymentStatus(orderStatusChangeDTO.getOrderCode(),
                    OrderStatusEnum.PAID.getCode(), Boolean.TRUE);
        } else {
            // 支付失败(失败)
            // 更新订单和支付明细状态
            updateOrderAndOrderPaymentStatus(orderStatusChangeDTO.getOrderCode(),
                    OrderStatusEnum.PAY_FAILED.getCode(), Boolean.FALSE);
        }
        return Boolean.TRUE;
    }

    /**
     * 生成订单号
     * @return 订单号
     */
    private String generateIdOrderNumber(String prefix) {
        // 规则：ZT年月日时分秒毫秒0001自增  例如：ZT2412191550221110001
        // 定义日期时间格式
        String pattern = "yyMMddHHmmssSSS";
        // 创建 SimpleDateFormat 对象
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        // 获取当前日期时间并格式化为字符串
        String formattedDate = sdf.format(new Date());
        String orderNumber = prefix + formattedDate;
        // 以每个订单号的 前缀+时间戳 生成所需的序列号
        orderNumber += distributedIdGenerator.generateId(orderNumber);
        return orderNumber;
    }

    /**
     * 组装订单信息对象
     */
    private OrdersPO assemblyOrder(OrderCreateDTO orderCreateDTO) {
        OrdersPO ordersPO = new OrdersPO();
        BeanUtils.copyProperties(orderCreateDTO, ordersPO);
        ordersPO.setVersion(1);
        ordersPO.setBeforeState(OrderStatusEnum.UN_CREATED.getCode());
        // 所属营业日
        ordersPO.setBusinessDay(Calendar.getInstance().getTime());
        // 订单原始总金额
        ordersPO.setTotalOriginPrice(orderCreateDTO.getOriginPrice());
        // 企业ID
        ordersPO.setCompanyId(orderCreateDTO.getEnterpriseId());
        // 订单状态：已创建  CREATED
        ordersPO.setState(OrderStatusEnum.CREATED.getCode());
        ordersPO.setDisabled(Boolean.FALSE);
        ordersPO.setMemberName("散客");
        ordersPO.setChangeMoney("0");
        // 订单原始总金额
        BigDecimal totalOriginPrice = new BigDecimal(orderCreateDTO.getOriginPrice()).add(new BigDecimal(orderCreateDTO.getDiscountPrice()));
        ordersPO.setTotalOriginPrice(String.valueOf(totalOriginPrice));
        ordersPO.setExternalDiscounts(new ArrayList<>());

        if (CollectionUtils.isNotEmpty(orderCreateDTO.getItems())) {
            BigDecimal actualReceivePriceTotal = orderCreateDTO.getItems().stream().map(item -> new BigDecimal(item.getActualReceivePrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            ordersPO.setOriginPrice(String.valueOf(actualReceivePriceTotal));
        }
        return ordersPO;
    }

    /**
     * 组装订单支付信息
     * @param orderCreateDTO 订单信息
     * @param payments 支付信息
     * @return 订单支付信息集合
     */
    private List<OrderPaymentPO> assemblyOrderPaymentList(OrderCreateDTO orderCreateDTO, List<OrderCreateDTO.OrderPaymentVO> payments,
                                                          String orderNumber, Integer orderId) {
        List<OrderPaymentPO> orderPaymentPOList = new ArrayList<>();
        OrderPaymentPO orderPaymentPO = null;
        for (OrderCreateDTO.OrderPaymentVO payment : payments) {
            orderPaymentPO = new OrderPaymentPO();
            BeanUtils.copyProperties(payment, orderPaymentPO);

            orderPaymentPO.setRecordId(0);
            orderPaymentPO.setOrderId(orderId);
            orderPaymentPO.setOrderNumber(orderNumber);
            orderPaymentPO.setCompanyId(orderCreateDTO.getEnterpriseId());
            orderPaymentPO.setIsPaySuccess(Boolean.FALSE);
            orderPaymentPO.setIsPrePaySuccess(Boolean.FALSE);
            orderPaymentPO.setChangeAmount("0");

            orderPaymentPOList.add(orderPaymentPO);
        }
        return orderPaymentPOList;
    }

    /**
     * 组装订单明细集合
     */
    private List<OrderItemPO> assemblyOrderItemList(OrderCreateDTO orderCreateDTO,
                                                    Integer orderId, String orderNumber) {
        // 传入的订单明细信息
        List<OrderCreateDTO.OrderItemVO> orderItems = orderCreateDTO.getItems();
        // 要保存的订单明细信息
        List<OrderItemPO> orderItemList = new ArrayList<>();
        OrderItemPO orderItemPO = null;
        for (OrderCreateDTO.OrderItemVO orderItem : orderItems) {
            orderItemPO = new OrderItemPO();
            BeanUtils.copyProperties(orderItem, orderItemPO);
            // 重置实收金额
            BigDecimal actualReceivePriceDecimal = new BigDecimal(orderItem.getActualReceivePrice());
            BigDecimal purchaseQuantity = new BigDecimal(orderItem.getPurchaseQuantity());
            orderItemPO.setActualReceivePrice(String.valueOf(actualReceivePriceDecimal.multiply(purchaseQuantity)));
            orderItemPO.setOrderId(orderId);
            orderItemPO.setOrderNumber(orderNumber);
            orderItemPO.setCompanyId(orderCreateDTO.getEnterpriseId());
            orderItemPO.setOriginTotalPriceInShopcaritem(orderItem.getSellingPrice());
            // 商品原价
            String sellingPrice = orderItem.getSellingPrice();
            // 优惠金额
            String discountTotalPrice = orderItem.getDiscountTotalPrice();

            BigDecimal sellingPriceDecimal = new BigDecimal(sellingPrice);
            BigDecimal discountTotalPriceDecimal = new BigDecimal(discountTotalPrice);
            orderItemPO.setShopPrice(sellingPriceDecimal.subtract(discountTotalPriceDecimal).toString());
            orderItemList.add(orderItemPO);
        }
        return orderItemList;
    }

    /**
     * 扣减库存操作
     * @return 库存扣减结果
     */
    private Boolean reduceInventory(OrderStatusChangeDTO orderStatusChangeDTO, OrdersPO ordersPO, Long companyId) {
        // 1.扣减库存
        // 查询该订单的出库记录，是否已经进行了出库，如果出库了就不进行后续动作
        long count = orderSyncInventoryRecordMapperService.lambdaQuery()
                .eq(OrderSyncInventoryRecordPO::getFlag, OrderSyncInventoryRecordFlagEnum.SUCCESSFUL.getCode())
                .eq(OrderSyncInventoryRecordPO::getType, OrderSyncInventoryRecordTypeEnum.OUTBOUND.getCode())
                .eq(OrderSyncInventoryRecordPO::getOrderNumber, orderStatusChangeDTO.getOrderCode())
                .count();
        if (count > 0) {
            log.info("订单：{}已经出库，无需重复出库，不再进行库存扣减操作", orderStatusChangeDTO.getOrderCode());
            return Boolean.TRUE;
        }
        // 扣减库存
        // 组装请求参数
        OrderOutboundCreateDTO orderOutboundCreateDTO = new OrderOutboundCreateDTO();
        orderOutboundCreateDTO.setStoreId(ordersPO.getStoreTeamInfoId());
        orderOutboundCreateDTO.setChannelId(ordersPO.getChannelId());
        orderOutboundCreateDTO.setRelatedCode(orderStatusChangeDTO.getOrderCode());
        orderOutboundCreateDTO.setRelatedId(ordersPO.getId());
        orderOutboundCreateDTO.setCustomerName(ordersPO.getMemberName());
        orderOutboundCreateDTO.setCreatorName(ordersPO.getOperaterName());
        orderOutboundCreateDTO.setCreatorId(ordersPO.getOperaterId());
        orderOutboundCreateDTO.setDocumentCreationTime(DateUtil.format(ordersPO.getCreateAt(), "yyyy-MM-dd HH:mm:ss"));
        // 查询订单明细列表
        List<OrderItemPO> orderItemList = orderItemMapperService.list(Wrappers.<OrderItemPO>lambdaQuery()
                .eq(OrderItemPO::getOrderNumber, orderStatusChangeDTO.getOrderCode()));
        List<Integer> goodsIdList = orderItemList.stream().map(OrderItemPO::getGoodsId).toList();
        orderOutboundCreateDTO.setGoodsList(goodsIdList);

        List<OrderOutboundCreateDTO.GoodsInfoDTO> goodsDetails = new ArrayList<>();
        OrderOutboundCreateDTO.GoodsInfoDTO goodsInfoDTO = null;

        // 查询商品信息
        List<StoreGoodsPO> storeGoodsList = storeGoodsMapperService.lambdaQuery()
                .eq(StoreGoodsPO::getStoreId, ordersPO.getStoreTeamInfoId())
                .in(StoreGoodsPO::getId, goodsIdList)
                .list();
        // key = goods_id  id value: StoreGoodsPO
        Map<Integer, StoreGoodsPO> storeGoodsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(storeGoodsList)) {
            storeGoodsMap = storeGoodsList.stream().collect(Collectors.toMap(StoreGoodsPO::getId, Function.identity()));
        }

        for (OrderItemPO orderItem : orderItemList) {
            goodsInfoDTO = new OrderOutboundCreateDTO.GoodsInfoDTO();
            BeanUtils.copyProperties(orderItem, goodsInfoDTO);
            goodsInfoDTO.setId(orderItem.getGoodsId());
            goodsInfoDTO.setQuantity(Integer.valueOf(orderItem.getPurchaseQuantity()));
            goodsInfoDTO.setAmount(new BigDecimal(orderItem.getActualReceivePrice()));
            goodsInfoDTO.setGoodsName(orderItem.getGoodsSaleName());
            goodsInfoDTO.setGoodsUnit(orderItem.getGoodsUnitId());
            StoreGoodsPO storeGoodsPO = storeGoodsMap.get(orderItem.getGoodsId());
            if (Objects.nonNull(storeGoodsPO)) {
                goodsInfoDTO.setCosts(storeGoodsPO.getCosts());
            }

            goodsDetails.add(goodsInfoDTO);
        }
        orderOutboundCreateDTO.setGoodsDetail(goodsDetails);

        return retryTemplate.execute(context -> {
            // "https://bms-inventory-sit.holderzone.cn/api/sale_outbound/create_order"
            String url = outBoundCreateOrderHost + UrlPathConstants.OUT_BOUND_CREATE_ORDER_PATH;
            log.info("库存扣减请求地址：{}", url);

            HttpHeaders headers = new HttpHeaders();
            headers.set("company_id", String.valueOf(companyId));

            HttpEntity<OrderOutboundCreateDTO> requestEntity = new HttpEntity<>(orderOutboundCreateDTO, headers);
            log.info("库存扣减请求参数：{}", requestEntity);
            ResponseEntity<String> postForEntity = restTemplate.postForEntity(url, requestEntity, String.class);
            log.info("库存扣减返回结果：{}", postForEntity);
            if (postForEntity.getStatusCode().is2xxSuccessful()) {
                String entityBody = postForEntity.getBody();
                JSONObject jsonObject = JSON.parseObject(entityBody);
                Assert.notNull(jsonObject, "库存扣减返回结果转JSON后为空");
                Integer code = jsonObject.getInteger("code");
                String msg = jsonObject.getString("msg");
                if (Objects.equals(code, 0)) {
                    log.info("库存扣减成功，订单号：{}", orderStatusChangeDTO.getOrderCode());
                    return true;
                } else {
                    log.error("库存扣减失败，订单号：{}，原因：{}", orderStatusChangeDTO.getOrderCode(), msg);
                    return false;
                }
            }
            // 返回结果不为空，且状态码不是200，认为失败
            log.error("库存扣减失败(接口返回状态码不是200)，订单号：{}", orderStatusChangeDTO.getOrderCode());
            return false;
        }, context -> {
            // 兜底逻辑
            log.error("库存扣减接口请求或处理请求返回结果过程中有报错：", context.getLastThrowable());
            return false;
        });
    }

    private void updateOrderAndOrderPaymentStatus(String orderNumber, Integer orderState, Boolean isPaySuccess) {
        // 系统当前时间
        Date now = Calendar.getInstance().getTime();
        // 1.更新订单状态
        ordersMapperService.lambdaUpdate()
                .eq(OrdersPO::getOrderNumber, orderNumber)
                .set(OrdersPO::getState, orderState)
                .set(isPaySuccess, OrdersPO::getPaidAt, now)
                .update();

        // 4.更新订单支付信息
        // 更新支付明细
        orderPaymentMapperService.lambdaUpdate()
                .eq(OrderPaymentPO::getOrderNumber, orderNumber)
                .set(OrderPaymentPO::getIsPaySuccess, isPaySuccess)
                // 支付成功时才更新支付成功时间
                .set(isPaySuccess, OrderPaymentPO::getPaySuccessTime, now)
                .update();
    }

}


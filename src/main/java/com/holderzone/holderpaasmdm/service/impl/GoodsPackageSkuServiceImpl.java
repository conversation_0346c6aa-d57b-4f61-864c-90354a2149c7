package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.common.utils.CommonUtils;
import com.holderzone.holderpaasmdm.mapper.service.GoodsPackageSkuMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsPackageSkuPO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendVO;
import com.holderzone.holderpaasmdm.service.GoodsPackageSkuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * SKU商品表 serviceImpl
 *
 * <AUTHOR>
 * @date 2025/4/18 9:37
 **/
@Service
@RequiredArgsConstructor
public class GoodsPackageSkuServiceImpl implements GoodsPackageSkuService {

    private final GoodsPackageSkuMapperService goodsPackageSkuMapperService;


    @Override
    public List<Integer> filterBarcodeByKeywords(List<StoreGoodsExtendVO> storeGoodsExtendVOList, String keywords) {
        // 查询商品sku信息
        List<Integer> storeGoodsIds = storeGoodsExtendVOList.stream().map(StoreGoodsExtendVO::getId).collect(Collectors.toList());
        List<GoodsPackageSkuPO> goodsPackageSkuPOS = goodsPackageSkuMapperService.findBarcodeByStoreGoodsIdIn(storeGoodsIds);
        // 如果都没有商品条码，跳过
        if (goodsPackageSkuPOS.isEmpty()) {
            return List.of();
        }
        storeGoodsIds.clear();
        // 根据关键字过滤
        goodsPackageSkuPOS.forEach(goodsPackageSkuPO -> {
            // 条形码包含
            if (CommonUtils.filterLikeBarcodeListByKeywords(goodsPackageSkuPO.getBarcode(), keywords)) {
                storeGoodsIds.add(goodsPackageSkuPO.getStoreGoodsId());
            }
        });
        return storeGoodsIds;
    }
}

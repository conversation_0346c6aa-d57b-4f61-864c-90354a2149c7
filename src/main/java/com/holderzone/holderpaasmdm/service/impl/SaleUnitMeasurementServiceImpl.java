package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.converter.SaleUnitMeasurementConverter;
import com.holderzone.holderpaasmdm.mapper.service.SaleUnitMeasurementCategoryMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleUnitMeasurementMapperService;
import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementCategoryPO;
import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementPO;
import com.holderzone.holderpaasmdm.model.vo.SaleUnitMeasurementVO;
import com.holderzone.holderpaasmdm.service.SaleUnitMeasurementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Description: 单位 ServiceImpl
 * Author: 向超
 * Date: 2025/02/06 15:31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SaleUnitMeasurementServiceImpl implements SaleUnitMeasurementService {

    private final SaleUnitMeasurementMapperService saleUnitMeasurementMapperService;
    private final SaleUnitMeasurementCategoryMapperService saleUnitMeasurementCategoryMapperService;
    private final SaleUnitMeasurementConverter saleUnitMeasurementConverter;


    @Override
    public List<SaleUnitMeasurementVO> querySaleUnitMeasurementList() {
        List<SaleUnitMeasurementVO> saleUnitMeasurementVOList =
                saleUnitMeasurementConverter.toSaleUnitMeasurementVOList(
                        saleUnitMeasurementMapperService.lambdaQuery().eq(SaleUnitMeasurementPO::getStatus, true).list());
        if (CollectionUtils.isEmpty(saleUnitMeasurementVOList)) {
            return Collections.emptyList();
        }
        Map<Integer, String> categoryIdAndNameMap = saleUnitMeasurementCategoryMapperService.lambdaQuery()
                .eq(SaleUnitMeasurementCategoryPO::getStatus, true)
                .list().stream()
                .collect(Collectors.toMap(SaleUnitMeasurementCategoryPO::getId, SaleUnitMeasurementCategoryPO::getCategoryName));
        saleUnitMeasurementVOList.forEach(saleUnitMeasurementVO ->
                saleUnitMeasurementVO.setCategoryName(categoryIdAndNameMap.get(saleUnitMeasurementVO.getCategoryId())));
        return saleUnitMeasurementVOList;
    }
}

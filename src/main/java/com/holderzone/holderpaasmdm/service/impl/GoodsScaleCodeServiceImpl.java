package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.converter.GoodsScaleCodeConverter;
import com.holderzone.holderpaasmdm.mapper.service.GoodsScaleCodeMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsScaleCodePO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendVO;
import com.holderzone.holderpaasmdm.service.GoodsScaleCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 门店商品称码 serviceImpl
 *
 * <AUTHOR>
 * @date 2025/4/16 15:05
 **/
@Service
@RequiredArgsConstructor
public class GoodsScaleCodeServiceImpl implements GoodsScaleCodeService {

    private final GoodsScaleCodeMapperService goodsScaleCodeMapperService;
    private final GoodsScaleCodeConverter goodsScaleCodeConverter;

    @Override
    public void assembleGoodsScaleCode(List<StoreGoodsExtendVO> storeGoodsExtendVOList, Integer storeId) {
        // 获取商品id数据
        List<Integer> storeGoodsIds = storeGoodsExtendVOList.stream().map(StoreGoodsExtendVO::getId).toList();
        // 查询秤内数据
        List<GoodsScaleCodePO> goodsScaleCodePOS = goodsScaleCodeMapperService.lambdaQuery()
                .in(GoodsScaleCodePO::getGoodsId, storeGoodsIds)
                .eq(GoodsScaleCodePO::getStoreId, storeId)
                .list();
        if (goodsScaleCodePOS.isEmpty()) {
            return;
        }
        for (StoreGoodsExtendVO storeGoodsExtendVO : storeGoodsExtendVOList) {
            // 查询商品秤内数据信息
            List<GoodsScaleCodePO> goodsScaleCodePOList = goodsScaleCodePOS.stream()
                    .filter(goodsScaleCodePO -> goodsScaleCodePO.getGoodsId().equals(storeGoodsExtendVO.getId()))
                    .toList();
            if (!goodsScaleCodePOList.isEmpty()) {
                storeGoodsExtendVO.setGoodsScaleCodeList(goodsScaleCodeConverter.toStoreGoodsScaleCodeVOList(goodsScaleCodePOList));
            }
        }
    }
}

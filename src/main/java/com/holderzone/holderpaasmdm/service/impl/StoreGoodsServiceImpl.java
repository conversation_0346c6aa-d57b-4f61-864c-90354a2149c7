package com.holderzone.holderpaasmdm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.holderpaasmdm.common.exception.BaseException;
import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.common.utils.CommonUtils;
import com.holderzone.holderpaasmdm.common.utils.DateUtils;
import com.holderzone.holderpaasmdm.converter.*;
import com.holderzone.holderpaasmdm.enumeraton.*;
import com.holderzone.holderpaasmdm.mapper.service.*;
import com.holderzone.holderpaasmdm.model.bo.*;
import com.holderzone.holderpaasmdm.model.dto.*;
import com.holderzone.holderpaasmdm.model.po.*;
import com.holderzone.holderpaasmdm.model.vo.*;
import com.holderzone.holderpaasmdm.service.CommonService;
import com.holderzone.holderpaasmdm.service.GoodsScaleCodeService;
import com.holderzone.holderpaasmdm.service.StoreGoods2Service;
import com.holderzone.holderpaasmdm.service.StoreGoodsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.stream.CollectorUtil.groupingBy;
import static com.holderzone.holderpaasmdm.common.constant.ErrorMessageKeys.*;
import static com.holderzone.holderpaasmdm.common.constant.PatternConstants.POSITION_FILLING_FIVE;
import static com.holderzone.holderpaasmdm.common.constant.PatternConstants.POSITION_FILLING_FOUR;
import static com.holderzone.holderpaasmdm.common.constant.RedissonLockConstants.RETAIL_GOODS_PLU_CODE_LOCK_PREFIX;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;


/**
 * Description: 门店营销方案 ServiceImpl
 * Author: 向超
 * Date: 2024/11/14 15:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StoreGoodsServiceImpl implements StoreGoodsService {
    private final CommonService commonService;
    private final GoodsBrandMapperService goodsBrandMapperService;
    private final GoodsLabelMapperService goodsLabelMapperService;
    private final UploadFilesMapperService uploadFilesMapperService;
    private final SaleUnitMeasurementMapperService saleUnitMeasurementMapperService;
    private final StoreGoodsMapperService storeGoodsMapperService;
    private final SaleCategoryMapperService saleCategoryMapperService;
    private final SaleCategoryAndStoreGoodsMapperService saleCategoryAndStoreGoodsMapperService;
    private final StoreSalesProgramMapperService storeSalesProgramMapperService;
    private final StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService;
    private final StoreSalesProgramGoodsPriceMapperService storeSalesProgramGoodsPriceMapperService;
    private final PriceAdjustmentOrderMapperService priceAdjustmentOrderMapperService;
    private final PriceAdjustmentOrderGoodsMapperService priceAdjustmentOrderGoodsMapperService;
    private final PriceAdjustmentOrderGoodsChangeInfoMapperService priceAdjustmentOrderGoodsChangeInfoMapperService;
    private final SaleChannelGoodsSortMapperService saleChannelGoodsSortMapperService;
    private final GoodsScaleCodeMapperService goodsScaleCodeMapperService;
    private final StoreGoodsCategoryMapperService storeGoodsCategoryMapperService;
    private final GoodsPackageSkuMapperService goodsPackageSkuMapperService;
    private final SpecMapperService specMapperService;
    private final SpecDetailMapperService specDetailMapperService;
    private final SkuSpecDetailsMapperService skuSpecDetailsMapperService;
    private final GoodsSpecMapperService goodsSpecMapperService;
    private final GoodsSpecDetailMapperService goodsSpecDetailMapperService;
    private final StoreGoodsConverter storeGoodsConverter;
    private final GoodsPictureConverter goodsPictureConverter;
    private final GoodsLabelConverter goodsLabelConverter;
    private final GoodsBrandConverter goodsBrandConverter;
    private final SaleCategoryConverter saleCategoryConverter;
    private final GoodsScaleCodeConverter goodsScaleCodeConverter;
    private final PriceAdjustmentOrderConverter priceAdjustmentOrderConverter;
    private final PriceAdjustmentOrderGoodsConverter priceAdjustmentOrderGoodsConverter;
    private final PriceAdjustmentOrderGoodsChangeInfoConverter priceAdjustmentOrderGoodsChangeInfoConverter;
    private final StoreSalesProgramConverter storeSalesProgramConverter;
    private final StoreGoodsCategoryConverter storeGoodsCategoryConverter;
    private final RedissonClient redissonClient;
    private final StoreGoods2Service storeGoods2Service;
    private final GoodsScaleCodeService goodsScaleCodeService;

    @Override
    public PageRespVO<StoreGoodsOnSaleExtendVO> queryStoreGoodsOnSaleListPage(QueryStoreGoodsOnSaleListDTO queryStoreGoodsOnSaleListDTO) {
        return commonService.queryStoreGoodsBaseListForOnSale(
                QueryStoreGoodsBaseListForOnSaleBO.builder()
                        .storeId(queryStoreGoodsOnSaleListDTO.getStoreId())
                        .channelId(queryStoreGoodsOnSaleListDTO.getChannelId())
                        .keyword(StringUtils.isEmpty(queryStoreGoodsOnSaleListDTO.getKeywords()) ? null : queryStoreGoodsOnSaleListDTO.getKeywords().toLowerCase())
                        .selaCategoryIdList(queryStoreGoodsOnSaleListDTO.getCategoryIdList())
                        .storeGoodsExpandList(Arrays.asList(StoreGoodsExpand.values()))
                        .skuCodeList(queryStoreGoodsOnSaleListDTO.getSpuCodeList())
                        .limit(queryStoreGoodsOnSaleListDTO.getLimit())
                        .page(queryStoreGoodsOnSaleListDTO.getPage())
                        .build());
    }

    /**
     * 对可售商品进行排序，排序规则如下：
     * 1、如果商品有排序的数据，则按照数据库中的排序数据来进行排序，sort值越小越靠前
     * 2、如果sort值相同，则按照创建时间倒序来进行排序
     * 3、如果商品没有排序的数据，则按照创建时间倒序来进行排序
     *
     * @param queryDTO         查询在售商品列表DTO
     * @param storeGoodsPOList 可售商品列表
     * @return 排序后的可售商品列表
     */
    private List<StoreGoodsPO> sortStoreGoodsPOList(
            QueryBasePageDTO queryDTO, List<StoreGoodsPO> storeGoodsPOList) {
        List<Integer> allStoreGoodsIdList = new ArrayList<>();
        Map<Integer, StoreGoodsPO> storeGoodsOnSaleExtendVOMap = new HashMap<>();
        for (StoreGoodsPO storeGoodsPO : storeGoodsPOList) {
            allStoreGoodsIdList.add(storeGoodsPO.getId());
            storeGoodsOnSaleExtendVOMap.put(storeGoodsPO.getId(), storeGoodsPO);
        }
        // 查询商品排序数据
        List<SaleChannelGoodsSortPO> saleChannelGoodsSortPOList = saleChannelGoodsSortMapperService.lambdaQuery()
                .eq(SaleChannelGoodsSortPO::getStoreId, queryDTO.getStoreId())
                .eq(SaleChannelGoodsSortPO::getChannelId, queryDTO.getChannelId())
                .in(SaleChannelGoodsSortPO::getStoreGoodsId, allStoreGoodsIdList)
                .list();
        if (CollectionUtils.isEmpty(saleChannelGoodsSortPOList)) {
            // 如果都没有排序数据则按照创建时间倒序来进行排序
            storeGoodsPOList.sort(Comparator.comparing(StoreGoodsPO::getCreatedAt).reversed());
        } else {
            // 先将商品排序数据分组，然后按照分组的key进行排序，分组的Key则是sort值，越小越靠前
            TreeMap<Integer, List<Integer>> sortedGroupTreeMap = saleChannelGoodsSortPOList.stream()
                    .collect(Collectors.groupingBy(SaleChannelGoodsSortPO::getSort,
                            () -> new TreeMap<>(Comparator.naturalOrder()), // TreeMap 会根据 key (即 sort) 自动排序
                            Collectors.mapping(SaleChannelGoodsSortPO::getStoreGoodsId, toList())
                    ));
            List<StoreGoodsPO> storeGoodsPOResultList = new ArrayList<>();
            List<Integer> storeGoodsIdListForHaveSort = new ArrayList<>();
            for (Map.Entry<Integer, List<Integer>> item : sortedGroupTreeMap.entrySet()) {
                storeGoodsIdListForHaveSort.addAll(item.getValue());
                storeGoodsPOResultList.addAll(item.getValue().stream()
                        .map(storeGoodsOnSaleExtendVOMap::get)
                        .sorted(Comparator.comparing(StoreGoodsPO::getCreatedAt).reversed())
                        .toList());
            }
            // 处理没有排序数据的商品
            allStoreGoodsIdList.removeAll(storeGoodsIdListForHaveSort);
            if (CollectionUtils.isNotEmpty(allStoreGoodsIdList)) {
                storeGoodsPOResultList.addAll(allStoreGoodsIdList.stream()
                        .map(storeGoodsOnSaleExtendVOMap::get)
                        .sorted(Comparator.comparing(StoreGoodsPO::getCreatedAt).reversed())
                        .toList());
            }
            storeGoodsPOList = storeGoodsPOResultList;
        }
        return storeGoodsPOList;
    }


    @Override
    public StoreGoodsOnSaleExtendVO queryStoreGoodsOnSaleDetail(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO) {
        // 校验查询在售商品详情DTO
        checkQueryStoreGoodsOnSaleDetailDTO(queryStoreGoodsOnSaleDetailDTO);
        // 查询在售商品信息
        StoreGoodsPO storeGoodsPO = queryStoreGoodsOnSalePO(queryStoreGoodsOnSaleDetailDTO);
        // 根据店铺商品ID查询所有销售方案商品列表，如果不存在则代表该商品未上架，返回null
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList =
                storeSalesProgramGoodsMapperService.lambdaQuery()
                        .eq(StoreSalesProgramGoodsPO::getGoodsId, storeGoodsPO.getId())
                        .list();
        if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, UNAVAILABLE_FOR_SALE);
        }
        // 查询所有销售方案 用于和此时此刻生效的方案去匹配
        Map<Integer, List<StoreSalesProgramGoodsPO>> storeSalesProgramIdAndGoodsInfoMap = storeSalesProgramGoodsPOList.stream()
                .collect(groupingBy(StoreSalesProgramGoodsPO::getStoreSalesProgramId));
        // 查询默认销售方案 以及 自定义销售方案和当前时间段对应的销售方案的时间段序号的映射
        Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair =
                commonService.queryDefaultSalesProgramAndCustomSalesProgramAndTimeSerialNumberPair(
                        queryStoreGoodsOnSaleDetailDTO.getStoreId(), queryStoreGoodsOnSaleDetailDTO.getChannelId());
        Integer storeSalesProgramId;
        Integer serialNumber;
        // 判断当前是自定义销售方案生效，还是默认销售方案生效
        if (resultPair.getValue() != null) {
            // 有生效的自定义销售方案，优先使用自定义方案
            if (storeSalesProgramIdAndGoodsInfoMap.containsKey(resultPair.getValue().getKey().getId())) {
                storeSalesProgramId = resultPair.getValue().getKey().getId();
                serialNumber = resultPair.getValue().getValue();
            } else if (storeSalesProgramIdAndGoodsInfoMap.containsKey(resultPair.getKey().getId())) {
                storeSalesProgramId = resultPair.getKey().getId();
                serialNumber = resultPair.getKey().getTimeSection().get(0).getSerialNumber();
            } else {
                // 商品未上架，返回 null
                throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, UNAVAILABLE_FOR_SALE);
            }
        } else {
            // 只有默认销售方案生效
            if (!storeSalesProgramIdAndGoodsInfoMap.containsKey(resultPair.getKey().getId())) {
                // 商品未上架，返回 null
                throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, UNAVAILABLE_FOR_SALE);
            }
            storeSalesProgramId = resultPair.getKey().getId();
            serialNumber = resultPair.getKey().getTimeSection().get(0).getSerialNumber();
        }
        // 获取商品信息
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOOnsaleList = storeSalesProgramIdAndGoodsInfoMap.get(storeSalesProgramId);
        // 查询销售价格信息
        List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList = storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                .in(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId,
                        storeSalesProgramGoodsPOOnsaleList.stream().map(StoreSalesProgramGoodsPO::getId).toList())
                .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, storeSalesProgramId)
                .eq(StoreSalesProgramGoodsPricePO::getSerialNumber, serialNumber)
                .list();
        return assembleStoreGoodsOnSaleExtendVO(queryStoreGoodsOnSaleDetailDTO, storeGoodsPO,
                storeSalesProgramGoodsPOOnsaleList, storeSalesProgramGoodsPricePOList);
    }

    @Override
    public StoreGoodsOnSaleExtendVO queryStoreGoodsOnSaleDetailV2(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO) {
        // 查询原有的数据
        StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO = this.queryStoreGoodsOnSaleDetail(queryStoreGoodsOnSaleDetailDTO);
        // sku商品为空，错误数据
        if (CollectionUtils.isEmpty(storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList())) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, GOODS_SKU_NOT_EXIST);
        }
        if (storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList().size() > 1) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, GOODS_SKU_OVER);
        }
        StoreGoodsSpecAndDetailsVO storeGoodsSpecAndDetailsVO = storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList().get(0);
        storeGoodsOnSaleExtendVO.setSellingPrice(storeGoodsSpecAndDetailsVO.getGoodsSpecSellingPrice());
        storeGoodsOnSaleExtendVO.setGoodsPackageSkuId(storeGoodsSpecAndDetailsVO.getSkuId());
        // 上浮sku商品数据
        return storeGoodsOnSaleExtendVO;
    }

    /**
     * 根据条码精确匹配在售商品
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询在售商品详情DTO
     * @return 在售商品信息
     */
    private StoreGoodsPO queryStoreGoodsOnSalePO(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO) {
        List<StoreGoodsPO> storeGoodsPOList = checkStoreGoodsIsExistsAndReturnInfo(queryStoreGoodsOnSaleDetailDTO);
        if (CollectionUtils.isEmpty(storeGoodsPOList)) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, GOODS_NOT_EXIST);
        }
        return storeGoodsPOList.get(0);
    }

    @Override
    public VerifyStoreGoodsOnSaleVO verifyStoreGoodsOnSaleInfo(VerifyStoreGoodsOnSaleDTO verifyStoreGoodsOnSaleDTO) {
        // 查询在售商品信息
        List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList = getStoreGoodsOnSaleList(verifyStoreGoodsOnSaleDTO);
        // 返回结果对象
        VerifyStoreGoodsOnSaleVO verifyStoreGoodsOnSaleVO = new VerifyStoreGoodsOnSaleVO();
        // 如果查询结果为空，则说明所有商品已下架，返回下架提示信息
        if (CollectionUtils.isEmpty(storeGoodsOnSaleExtendVOList)) {
            handleGoodsChangeReminderForListingStatus(verifyStoreGoodsOnSaleDTO, verifyStoreGoodsOnSaleVO);
        } else {
            // 处理在售商品信息
            handleGoodsOnSaleInfo(verifyStoreGoodsOnSaleDTO, storeGoodsOnSaleExtendVOList, verifyStoreGoodsOnSaleVO);
        }
        return verifyStoreGoodsOnSaleVO;
    }

    @Override
    public PageRespVO<StoreGoodsBaseVO> queryStoreGoodsAvailableForSaleMemberListPage(
            QueryStoreGoodsAvailableForSaleMemberListDTO queryStoreGoodsAvailableForSaleMemberListDTO) {
        // 校验参数
        checkQueryStoreGoodsAvailableForSaleListDTO(queryStoreGoodsAvailableForSaleMemberListDTO);
        // 获取可售商品ID列表
        Set<Integer> allAvailableGoodsIdSet = getAvailableGoodsIdSet(queryStoreGoodsAvailableForSaleMemberListDTO);
        if (CollectionUtils.isEmpty(allAvailableGoodsIdSet)) {
            return new PageRespVO<>(Collections.emptyList(), queryStoreGoodsAvailableForSaleMemberListDTO.getLimit(),
                    queryStoreGoodsAvailableForSaleMemberListDTO.getPage());
        }
        // 根据查询条件查询商品列表
        List<StoreGoodsPO> storeGoodsList = queryStoreGoodsList(queryStoreGoodsAvailableForSaleMemberListDTO, allAvailableGoodsIdSet);
        // 分页处理
        return buildPagedResponse(queryStoreGoodsAvailableForSaleMemberListDTO, storeGoodsList);
    }

    @Override
    public List<GoodsComboTypeVO> queryStoreGoodsComboTypeList() {
        StoreComboType[] values = StoreComboType.values();
        return storeGoodsConverter.toGoodsComboTypeVOList(new ArrayList<>(Arrays.asList(values)));
    }

    @Override
    public PageRespVO<StoreGoodsAvailableForSalePOSExtendVO> queryStoreGoodsAvailableForSalePosListPage(
            QueryStoreGoodsAvailableForSalePosListDTO queryStoreGoodsAvailableForSalePosListDTO) {
        // 通过销售方案ID查询销售方案信息
        StoreSalesProgramPO storeSalesProgramPO = getStoreSalesProgramPOById(queryStoreGoodsAvailableForSalePosListDTO);
        if (storeSalesProgramPO == null) {
            return new PageRespVO<>(queryStoreGoodsAvailableForSalePosListDTO);
        }
        // 通过销售方案ID查询销售方案商品信息集合
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeSalesProgramGoodsMapperService.lambdaQuery()
                .eq(StoreSalesProgramGoodsPO::getStoreSalesProgramId, storeSalesProgramPO.getId())
                .list();
        if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
            return new PageRespVO<>(queryStoreGoodsAvailableForSalePosListDTO);
        }
        // 组装店铺商品ID和销售方案商品信息的映射，用于后续组装商品售卖信息（封面、售卖名称等）
        Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndStoreSalesProgramGoodsPOListMap = new HashMap<>();
        Set<Integer> skuIdSet = new HashSet<>();
        Set<Integer> coverIdSet = new HashSet<>();
        storeSalesProgramGoodsPOList.forEach(storeSalesProgramGoodsPO -> {
            storeGoodsIdAndStoreSalesProgramGoodsPOListMap.computeIfAbsent(storeSalesProgramGoodsPO.getGoodsId(), k -> new ArrayList<>()).add(storeSalesProgramGoodsPO);
            skuIdSet.add(storeSalesProgramGoodsPO.getGoodsPackageSkuId());
            if (storeSalesProgramGoodsPO.getCoverPicture() != null) {
                coverIdSet.add(storeSalesProgramGoodsPO.getCoverPicture());
            }
        });
        List<Integer> storeGoodsIdList = new ArrayList<>(storeGoodsIdAndStoreSalesProgramGoodsPOListMap.keySet());
        // 如果传入了销售分组ID，则需要通过销售分组ID过滤商品ID
        storeGoodsIdList = filterStoreGoodsBySaleCategoryId(queryStoreGoodsAvailableForSalePosListDTO, storeGoodsIdList);
        if (CollectionUtils.isEmpty(storeGoodsIdList)) {
            return new PageRespVO<>(queryStoreGoodsAvailableForSalePosListDTO);
        }
        List<StoreGoodsPO> storeGoodsPOList = getStoreGoodsPOListByIdListAndKeywords(queryStoreGoodsAvailableForSalePosListDTO.getKeywords(),
                storeGoodsIdList, storeSalesProgramPO.getId());
        if (CollectionUtils.isEmpty(storeGoodsPOList)) {
            return new PageRespVO<>(queryStoreGoodsAvailableForSalePosListDTO);
        }
        // 对商品进行排序
        storeGoodsPOList = sortStoreGoodsPOList(queryStoreGoodsAvailableForSalePosListDTO, storeGoodsPOList);
        // 先进行分页，避免查询商品信息时，查询大量的无用数据
        PageRespVO<Integer> storeGoodsIdPageList = new PageRespVO<>(storeGoodsIdList, queryStoreGoodsAvailableForSalePosListDTO.getLimit(),
                queryStoreGoodsAvailableForSalePosListDTO.getPage());
        storeGoodsIdList = storeGoodsIdPageList.getList();
        if (CollectionUtils.isEmpty(storeGoodsIdList)) {
            return new PageRespVO<>(queryStoreGoodsAvailableForSalePosListDTO);
        }
        List<StoreGoodsPO> storeGoodsPOListResult = new ArrayList<>();
        // 根据分页后的商品ID列表查询商品信息
        for (StoreGoodsPO storeGoodsPO : storeGoodsPOList) {
            if (storeGoodsIdList.contains(storeGoodsPO.getId())) {
                storeGoodsPOListResult.add(storeGoodsPO);
            }
        }
        List<StoreGoodsAvailableForSalePOSExtendVO> storeGoodsAvailableForSalePOSExtendVOList = storeGoodsConverter.toStoreGoodsAvailableForSalePOSExtendVOList(
                storeGoodsPOListResult);
        // 查询并组装 销售方案商品ID和对应时段价格信息列表的映射
        Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndPricePOListMap =
                getStoreSalesProgramGoodsIdAndPricePOListMap(storeSalesProgramPO, storeGoodsIdList);
        // 获取商品某个时段的序号
        // 用于获取商品的时段价格，因为在列表出需要展示一个价格，估只能获取某一个时段的价格来展示
        int serialNumberForUnitPriceDisplay = getSerialNumberForUnitPriceDisplay(storeSalesProgramPO);
        // 获取单位ID和单位名称的映射
        Map<Integer, String> unitIdAndNameMap = saleUnitMeasurementMapperService.list().stream()
                .collect(toMap(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName));

        // 组装商品规格详情列表的业务对象
        AssembleStoreGoodsSpecDetailListForAvailableForSaleBO storeGoodsSpecDetailListForAvailableForSaleBO =
                getAssembleStoreGoodsSpecDetailListForAvailableForSaleBO(queryStoreGoodsAvailableForSalePosListDTO,
                        storeGoodsAvailableForSalePOSExtendVOList, skuIdSet, unitIdAndNameMap, coverIdSet);

        // 开始组装数据 并 返回封面图片ID集合，用于后续获取封面图片URL并组装商品图片信息
        assembleStoreGoodsAvailableForSaleVOListAndReturnCoverIdSet(
                storeGoodsAvailableForSalePOSExtendVOList, storeGoodsIdAndStoreSalesProgramGoodsPOListMap, storeGoodsSpecDetailListForAvailableForSaleBO,
                storeSalesProgramGoodsIdAndPricePOListMap, serialNumberForUnitPriceDisplay, storeSalesProgramPO);
        assembleStoreGoodsAvailableForSaleSaleCategoryList(storeGoodsAvailableForSalePOSExtendVOList, storeGoodsIdList,
                queryStoreGoodsAvailableForSalePosListDTO.getChannelId());
        return new PageRespVO<>(storeGoodsAvailableForSalePOSExtendVOList, storeGoodsIdPageList.getTotal(),
                storeGoodsIdPageList.getPageSize(), storeGoodsIdPageList.getPageNum(), storeGoodsIdPageList.getTotalPage());
    }

    /**
     * 根据关键字和商品ID集合查询商品信息
     *
     * @param keywords            关键字
     * @param storeGoodsIdList    商品ID集合
     * @param storeSalesProgramId 销售方案ID
     * @return 商品信息列表
     */
    private List<StoreGoodsPO> getStoreGoodsPOListByIdListAndKeywords(String keywords,
                                                                      List<Integer> storeGoodsIdList,
                                                                      Integer storeSalesProgramId) {
        if (StringUtils.isEmpty(keywords)) {
            return storeGoodsMapperService.listByIds(storeGoodsIdList);
        } else {
            List<StoreGoodsPO> goodsPOS = storeGoodsMapperService.queryStoreGoodsByIdListAndKeywords(storeGoodsIdList,
                    CommonUtils.assembleSqlLikeString(keywords), storeSalesProgramId);
            // id去除过滤后不存在的参数
            if(!goodsPOS.isEmpty()){
                List<Integer> filterIds = goodsPOS.stream().map(StoreGoodsPO::getId).toList();
                storeGoodsIdList.removeIf(item -> !filterIds.contains(item));
            }
            return goodsPOS;
        }
    }

    /**
     * 组装商品规格详情列表的业务对象(针对商品调价列表接口)
     *
     * @param queryStoreGoodsAvailableForSalePosListDTO 查询商品调价列表DTO
     * @param storeGoodsAvailableForSalePOSExtendVOList 商品调价列表
     * @param skuIdSet                                  商品SKU ID集合
     * @param unitIdAndNameMap                          单位ID和单位名称的映射
     * @return 组装商品规格详情列表的业务对象
     */
    private AssembleStoreGoodsSpecDetailListForAvailableForSaleBO getAssembleStoreGoodsSpecDetailListForAvailableForSaleBO(
            QueryStoreGoodsAvailableForSalePosListDTO queryStoreGoodsAvailableForSalePosListDTO,
            List<StoreGoodsAvailableForSalePOSExtendVO> storeGoodsAvailableForSalePOSExtendVOList,
            Set<Integer> skuIdSet,
            Map<Integer, String> unitIdAndNameMap,
            Set<Integer> coverIdSet) {
        boolean hasMultipleSpec = storeGoodsAvailableForSalePOSExtendVOList.stream().anyMatch(storeGoodsOnSaleExtendVO -> storeGoodsOnSaleExtendVO.getGoodsSpec() == 2);
        Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap = new HashMap<>();
        Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap = new HashMap<>();
        Map<Integer, SpecPO> specIdAndSpecInfoMap = null;
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap = null;
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = new HashMap<>();
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = new HashMap<>();
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = new HashMap<>();
        Map<Integer, GoodsPictureVO> coverIdAndPictureVOMap = new HashMap<>();
        List<GoodsPackageSkuPO> goodsPackageSkuPOList = goodsPackageSkuMapperService.listByIds(skuIdSet);
        Map<Integer, GoodsPackageSkuPO> goodsPackageSkuIdAndInfoMap = new HashMap<>();
        goodsPackageSkuPOList.forEach(goodsPackageSkuPO -> {
            goodsPackageSkuIdAndInfoMap.put(goodsPackageSkuPO.getId(), goodsPackageSkuPO);
            if (Objects.nonNull(goodsPackageSkuPO.getCover())) {
                coverIdSet.add(goodsPackageSkuPO.getCover());
            }
        });
        if (hasMultipleSpec) {
            specIdAndSpecInfoMap = specMapperService.lambdaQuery()
                    .select(SpecPO::getId, SpecPO::getName, SpecPO::getSort)
                    .eq(SpecPO::getStoreId, queryStoreGoodsAvailableForSalePosListDTO.getStoreId())
                    .list().stream().collect(toMap(SpecPO::getId, Function.identity()));
            specDetailIdAndInfoMap = specDetailMapperService.lambdaQuery()
                    .select(SpecDetailPO::getId, SpecDetailPO::getName, SpecDetailPO::getSort, SpecDetailPO::getSpecId)
                    .in(SpecDetailPO::getSpecId, specIdAndSpecInfoMap.keySet())
                    .list().stream().collect(toMap(SpecDetailPO::getId, Function.identity()));
            List<SkuSpecDetailsPO> skuSpecDetailsPOList = skuSpecDetailsMapperService.lambdaQuery()
                    .select(SkuSpecDetailsPO::getGoodsSpecDetailId, SkuSpecDetailsPO::getSkuId)
                    .in(SkuSpecDetailsPO::getSkuId, skuIdSet).list();

            List<Integer> goodsSpecDetailIdList = new ArrayList<>();
            skuSpecDetailsPOList.forEach(skuSpecDetailsPO -> {
                goodsSpecDetailIdList.add(skuSpecDetailsPO.getGoodsSpecDetailId());
                skuIdAndGoodsSpecDetailIdListMap.computeIfAbsent(skuSpecDetailsPO.getSkuId(), k -> new ArrayList<>()).add(skuSpecDetailsPO.getGoodsSpecDetailId());
            });
            List<Integer> goodsSpecIdList = new ArrayList<>();

            List<GoodsSpecDetailPO> goodsSpecDetailPOList = goodsSpecDetailMapperService.lambdaQuery().in(GoodsSpecDetailPO::getId, goodsSpecDetailIdList).list();
            goodsSpecDetailPOList.forEach(goodsSpecDetailPO -> {
                goodsSpecDetailIdAndInfoMap.put(goodsSpecDetailPO.getId(), goodsSpecDetailPO);
                goodsSpecIdList.add(goodsSpecDetailPO.getGoodsSpecId());
                goodsSpecIdAndGoodsSpecDetailInfoMap.computeIfAbsent(goodsSpecDetailPO.getGoodsSpecId(), k -> new ArrayList<>()).add(goodsSpecDetailPO);
            });
            List<GoodsSpecPO> goodsSpecPOList = goodsSpecMapperService.lambdaQuery()
                    .in(GoodsSpecPO::getId, goodsSpecIdList)
                    .list();

            goodsSpecPOList.forEach(goodsSpecPO -> {
                storeGoodsIdAndGoodsSpecPOListMap.computeIfAbsent(goodsSpecPO.getStoreGoodsId(), k -> new ArrayList<>()).add(goodsSpecPO);
                goodsSpecIdAndInfoMap.put(goodsSpecPO.getId(), goodsSpecPO);
            });
        }
        if (CollectionUtils.isNotEmpty(coverIdSet)) {
            coverIdAndPictureVOMap = commonService.assembleCoverIdAndPictureVOMap(coverIdSet);
        }
        return AssembleStoreGoodsSpecDetailListForAvailableForSaleBO.builder()
                .goodsSpecDetailIdAndInfoMap(goodsSpecDetailIdAndInfoMap)
                .skuIdAndGoodsSpecDetailIdListMap(skuIdAndGoodsSpecDetailIdListMap)
                .goodsSpecIdAndInfoMap(goodsSpecIdAndInfoMap)
                .storeGoodsUnitIdAndNameMap(unitIdAndNameMap)
                .specDetailIdAndInfoMap(specDetailIdAndInfoMap)
                .specIdAndSpecInfoMap(specIdAndSpecInfoMap)
                .coverIdAndPictureVOMap(coverIdAndPictureVOMap)
                .goodsPackageSkuIdAndInfoMap(goodsPackageSkuIdAndInfoMap)
                .build();
    }

    /**
     * 根据销售方案ID查询销售方案信息
     *
     * @param queryStoreGoodsAvailableForSalePosListDTO 查询在售商品列表DTO
     * @return 销售方案信息
     */
    private StoreSalesProgramPO getStoreSalesProgramPOById(QueryStoreGoodsAvailableForSalePosListDTO queryStoreGoodsAvailableForSalePosListDTO) {
        StoreSalesProgramPO storeSalesProgramPO;
        if (Objects.isNull(queryStoreGoodsAvailableForSalePosListDTO.getStoreSalesProgramId())) {
            // 如果没有传入销售方案ID，则查询默认的销售方案
            storeSalesProgramPO = storeSalesProgramMapperService.lambdaQuery()
                    .eq(StoreSalesProgramPO::getStoreId, queryStoreGoodsAvailableForSalePosListDTO.getStoreId())
                    .eq(StoreSalesProgramPO::getChannelId, queryStoreGoodsAvailableForSalePosListDTO.getChannelId())
                    .eq(StoreSalesProgramPO::getIsDefault, true)
                    .one();
        } else {
            // 如果传入了销售方案ID，则查询该销售方案信息
            storeSalesProgramPO = storeSalesProgramMapperService.getById(
                    queryStoreGoodsAvailableForSalePosListDTO.getStoreSalesProgramId());

        }
        return storeSalesProgramPO;
    }

    /**
     * 根据封面ID集合获取商品封面信息集合
     *
     * @param coverPictureIdSet 封面ID集合
     * @return 商品封面信息集合
     */
    private Map<Integer, GoodsPictureVO> getIntegerGoodsPictureVOMap(Set<Integer> coverPictureIdSet) {
        Map<Integer, GoodsPictureVO> coverPictureIdAndUrlMap;
        if (CollectionUtils.isEmpty(coverPictureIdSet)) {
            coverPictureIdAndUrlMap = Collections.emptyMap();
        } else {
            coverPictureIdAndUrlMap = goodsPictureConverter.toGoodsPictureVOList(
                            uploadFilesMapperService.listByIds(coverPictureIdSet)).stream()
                    .collect(toMap(GoodsPictureVO::getId, Function.identity()));
        }
        return coverPictureIdAndUrlMap;
    }

    /**
     * 组装商品销售分组信息
     *
     * @param storeGoodsAvailableForSalePOSExtendVOList 可售商品列表
     * @param storeGoodsIdList                          商品ID列表
     * @param channelId                                 渠道ID
     */
    private void assembleStoreGoodsAvailableForSaleSaleCategoryList(
            List<StoreGoodsAvailableForSalePOSExtendVO> storeGoodsAvailableForSalePOSExtendVOList,
            Collection<Integer> storeGoodsIdList, Integer channelId) {
        List<SaleCategoryAndStoreGoodsPO> saleCategoryAndStoreGoodsPOList =
                saleCategoryAndStoreGoodsMapperService.querySaleCategoryByStoreGoodsIdListAndChannelId(storeGoodsIdList, channelId);
        Map<Integer, List<Integer>> storeGoodsIdAndSaleCategoryIdListMap = new HashMap<>();
        List<Integer> saleCategoryIdList = new ArrayList<>();
        // 根据商品ID分组，并将销售分组ID放入集合中
        for (SaleCategoryAndStoreGoodsPO saleCategoryAndStoreGoodsPO : saleCategoryAndStoreGoodsPOList) {
            saleCategoryIdList.add(saleCategoryAndStoreGoodsPO.getSaleCategoryId());
            storeGoodsIdAndSaleCategoryIdListMap.computeIfAbsent(saleCategoryAndStoreGoodsPO.getStoreGoodsId(), k -> new ArrayList<>())
                    .add(saleCategoryAndStoreGoodsPO.getSaleCategoryId());
        }
        List<SaleCategoryVO> saleCategoryVOList = saleCategoryConverter.toSaleCategoryVOList(saleCategoryMapperService.queryGenerationsVOListByIdListAndChannelId(saleCategoryIdList, channelId));
        Map<Integer, SaleCategoryVO> saleCategoryIdAndInfoMap = saleCategoryVOList.stream().collect(toMap(SaleCategoryVO::getId, Function.identity()));
        for (StoreGoodsAvailableForSalePOSExtendVO storeGoodsAvailableForSalePOSExtendVO : storeGoodsAvailableForSalePOSExtendVOList) {
            storeGoodsAvailableForSalePOSExtendVO.setSaleCategoryIdList(storeGoodsIdAndSaleCategoryIdListMap.get(storeGoodsAvailableForSalePOSExtendVO.getId()));
            Set<SaleCategoryVO> saleCategoryChildAndParentVOSet = new HashSet<>();
            for (Integer saleCategoryChildId : storeGoodsAvailableForSalePOSExtendVO.getSaleCategoryIdList()) {
                SaleCategoryVO saleCategoryVO = saleCategoryIdAndInfoMap.get(saleCategoryChildId);
                saleCategoryChildAndParentVOSet.add(saleCategoryVO);
                if (saleCategoryVO.getParentId() != null && saleCategoryVO.getParentId() != 0) {
                    saleCategoryChildAndParentVOSet.add(saleCategoryIdAndInfoMap.get(saleCategoryVO.getParentId()));
                }
            }
            storeGoodsAvailableForSalePOSExtendVO.setSaleCategoryList(saleCategoryChildAndParentVOSet);
        }
    }

    @Override
    public StoreGoodsExtendVO queryStoreGoodsAvailableForSaleWeighDetail(
            QueryStoreGoodsAvailableForSaleWeighDetailDTO queryStoreGoodsAvailableForSaleWeighDetailDTO) {
        if (!storeGoodsMapperService.lambdaQuery()
                .like(StoreGoodsPO::getBarcode, queryStoreGoodsAvailableForSaleWeighDetailDTO.getBarcode())
                .eq(StoreGoodsPO::getStatus, StoreGoodsStatus.ENABLE.getValue())
                .exists()) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "商品不存在!");
        }
        List<StoreGoodsPO> storeGoodsPOList = commonService.queryStoreGoodsForAvailableForSaleWeighList(
                QueryStoreGoodsForAvailableForSaleWeighListBO.builder()
                        .storeId(queryStoreGoodsAvailableForSaleWeighDetailDTO.getStoreId())
                        .channelId(queryStoreGoodsAvailableForSaleWeighDetailDTO.getChannelId())
                        .barcode(CommonUtils.assembleSqlLikeString(queryStoreGoodsAvailableForSaleWeighDetailDTO.getBarcode())).build());
        StoreGoodsPO storeGoodsPO = filterStoreGoodsForAvailableForSaleWeigh(storeGoodsPOList, queryStoreGoodsAvailableForSaleWeighDetailDTO.getBarcode());
        if (storeGoodsPO == null) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "该商品暂不支持传秤!");
        }
        StoreGoodsExtendVO storeGoodsExtendVO = storeGoodsConverter.toStoreGoodsExtendVO(storeGoodsPO);
        // 给可售商品组装对应方案的售卖信息
        // 组装商品名称、销售价格、商品图片
        commonService.assembleGoodsNameAndSalePriceAndPictureForWeigh(storeGoodsExtendVO,
                queryStoreGoodsAvailableForSaleWeighDetailDTO.getStoreId(),
                queryStoreGoodsAvailableForSaleWeighDetailDTO.getChannelId());
        // 查询商品扩展信息
        commonService.assembleGoodsExpandInfo(List.of(storeGoodsExtendVO), queryStoreGoodsAvailableForSaleWeighDetailDTO.getStoreGoodsExpandList(),
                queryStoreGoodsAvailableForSaleWeighDetailDTO.getStoreId(), queryStoreGoodsAvailableForSaleWeighDetailDTO.getChannelId(),
                queryStoreGoodsAvailableForSaleWeighDetailDTO.getScaleType());
        return storeGoodsExtendVO;
    }

    /**
     * 根据条码过滤出对应的商品
     *
     * @param storeGoodsPOList 商品列表
     * @param barcode          条码
     * @return 商品
     */
    private StoreGoodsPO filterStoreGoodsForAvailableForSaleWeigh(List<StoreGoodsPO> storeGoodsPOList, String barcode) {
        if (CollectionUtils.isEmpty(storeGoodsPOList)) {
            return null;
        }
        for (StoreGoodsPO storeGoodsPO : storeGoodsPOList) {
            if (CommonUtils.filterPreciseBarcodeListByKeywords(storeGoodsPO.getBarcode(), barcode)) {
                return storeGoodsPO;
            }
        }
        return null;
    }

    @Override
    public PageRespVO<StoreGoodsPrintLabelExtendVO> queryStoreGoodsAvailableForSaleTimeSectionListPage(
            QueryStoreGoodsAvailableForSaleListDTO queryDTO) {
        return storeGoods2Service.queryStoreGoodsAvailableForSaleTimeSectionListPage(queryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void storeGoodsAvailableForSalePriceAdjustment(
            StoreGoodsAvailableForSaleAdjustmentPriceDTO storeGoodsAvailableForSaleAdjustmentPriceDTO) {
        // 校验商品是否可以调价
        validatePriceAdjustment(storeGoodsAvailableForSaleAdjustmentPriceDTO.getStoreGoodsId(),
                storeGoodsAvailableForSaleAdjustmentPriceDTO.getTimeSectionAdjustmentList().get(0).getSectionList().get(0).getStoreSalesProgramId(),
                storeGoodsAvailableForSaleAdjustmentPriceDTO.getTimeSectionAdjustmentList().get(0).getSectionList().get(0).getStoreSalesProgramGoodsId());
        // 目前只做了POS端的调价，POS端目前只支持的单个商品、默认策略的多规格调价，默认策略只有一个全时段
        StoreGoodsAvailableForSaleAdjustmentPriceDTO.TimeSectionAdjustmentDTO timeSectionAdjustmentDTO =
                storeGoodsAvailableForSaleAdjustmentPriceDTO.getTimeSectionAdjustmentList().get(0);
        // 是否是修改的默认方案的商品的价格，默认方案的价格需要修改所有自定义方案中的建议售价
        Set<Integer> allSalesProgramIdSet = storeSalesProgramGoodsMapperService.lambdaQuery()
                .select(StoreSalesProgramGoodsPO::getStoreSalesProgramId)
                .eq(StoreSalesProgramGoodsPO::getGoodsId, storeGoodsAvailableForSaleAdjustmentPriceDTO.getStoreGoodsId())
                .list().stream().map(StoreSalesProgramGoodsPO::getStoreSalesProgramId).collect(Collectors.toSet());
        allSalesProgramIdSet.remove(timeSectionAdjustmentDTO.getSectionList().get(0).getStoreSalesProgramId());
        boolean needUpdateOtherNonDefaultSalesProgram = CollectionUtils.isNotEmpty(allSalesProgramIdSet);
        List<BatchUpdateSellingPriceBO> batchUpdateSellingPriceBOForNonDefaultList = new ArrayList<>();
        List<BatchUpdateSellingPriceBO> batchUpdateSellingPriceBOForSalesProgramList = new ArrayList<>();
        List<PriceAdjustmentOrderGoodsPO> priceAdjustmentOrderGoodsPOList = new ArrayList<>();
        List<PriceAdjustmentOrderGoodsChangeInfoPO> priceAdjustmentOrderGoodsChangeInfoPOList = new ArrayList<>();
        List<Integer> saleCategoryIdList = storeGoodsAvailableForSaleAdjustmentPriceDTO.getSaleCategoryIdList();
        List<PriceAdjustmentOrderGoodsPO.SaleCategoryNode> saleCategoryNodeList = priceAdjustmentOrderGoodsConverter.toSaleCategoryNodeList(storeGoodsAvailableForSaleAdjustmentPriceDTO.getSaleCategoryList());
        saleCategoryNodeList.forEach(node -> {
            if (saleCategoryIdList.contains(node.getId())) {
                node.setIsBind(true);
            }
        });
        Map<Integer, PriceAdjustmentOrderGoodsPO.SaleCategoryNode> saleCategoryNodeIdAndInfoMap =
                saleCategoryNodeList.stream().collect(toMap(PriceAdjustmentOrderGoodsPO.SaleCategoryNode::getId, Function.identity()));
        List<String> saleCategoryNameList = new ArrayList<>();
        for (Integer saleCategoryId : saleCategoryIdList) {
            PriceAdjustmentOrderGoodsPO.SaleCategoryNode childSaleCategoryNode = saleCategoryNodeIdAndInfoMap.get(saleCategoryId);
            if (childSaleCategoryNode == null) {
                continue;
            }
            if (childSaleCategoryNode.getParentId() == 0) {
                saleCategoryNameList.add(childSaleCategoryNode.getName());
            } else {
                PriceAdjustmentOrderGoodsPO.SaleCategoryNode parentSaleCategoryNode =
                        saleCategoryNodeIdAndInfoMap.get(childSaleCategoryNode.getParentId());
                saleCategoryNameList.add(parentSaleCategoryNode.getName() + "/" + childSaleCategoryNode.getName());
            }
        }
        PriceAdjustmentOrderPO priceAdjustmentOrderPO = priceAdjustmentOrderConverter.toPriceAdjustmentOrderPO(storeGoodsAvailableForSaleAdjustmentPriceDTO);
        priceAdjustmentOrderPO.setSourceType(2);
        priceAdjustmentOrderMapperService.save(priceAdjustmentOrderPO);
        for (StoreGoodsAvailableForSaleAdjustmentPriceDTO.TimeSectionAdjustmentDTO sectionAdjustmentDTO :
                storeGoodsAvailableForSaleAdjustmentPriceDTO.getTimeSectionAdjustmentList()) {
            // 目前只能修改默认方案的时段价格，默认方案的时段就一个全时段
            StoreGoodsAvailableForSaleAdjustmentPriceDTO.SectionDTO sectionDTO = sectionAdjustmentDTO.getSectionList().get(0);
            if (needUpdateOtherNonDefaultSalesProgram) {
                batchUpdateSellingPriceBOForNonDefaultList.add(BatchUpdateSellingPriceBO.builder()
                        .adjustmentPrice(sectionDTO.getAdjustmentPrice())
                        .conditionId(sectionAdjustmentDTO.getGoodsPackageSkuId())
                        .build());
            }
            batchUpdateSellingPriceBOForSalesProgramList.add(BatchUpdateSellingPriceBO.builder()
                    .adjustmentPrice(sectionDTO.getAdjustmentPrice())
                    .conditionId(sectionDTO.getStoreSalesProgramGoodsPriceId())
                    .build());

            PriceAdjustmentOrderGoodsPO priceAdjustmentOrderGoodsPO = assemblePriceAdjustmentOrderGoodsPO(storeGoodsAvailableForSaleAdjustmentPriceDTO,
                    sectionAdjustmentDTO, saleCategoryNameList, saleCategoryNodeList);
            priceAdjustmentOrderGoodsPOList.add(priceAdjustmentOrderGoodsPO);
            PriceAdjustmentOrderGoodsChangeInfoPO priceAdjustmentOrderGoodsChangeInfoPO =
                    assemblePriceAdjustmentOrderGoodsChangeInfoPO(storeGoodsAvailableForSaleAdjustmentPriceDTO, priceAdjustmentOrderPO, sectionDTO);
            priceAdjustmentOrderGoodsChangeInfoPOList.add(priceAdjustmentOrderGoodsChangeInfoPO);
        }
        if (CollectionUtils.isNotEmpty(batchUpdateSellingPriceBOForNonDefaultList)) {
            // 是否是修改的默认方案的商品的价格，默认方案的价格需要修改所有自定义方案中的建议售价
            storeSalesProgramGoodsMapperService.batchUpdateSellingPrice(batchUpdateSellingPriceBOForNonDefaultList, Timestamp.valueOf(LocalDateTime.now()), allSalesProgramIdSet);
        }
        // 修改销售方案商品价格表
        storeSalesProgramGoodsPriceMapperService.batchUpdateSellingPrice(batchUpdateSellingPriceBOForSalesProgramList, Timestamp.valueOf(LocalDateTime.now()));
        for (PriceAdjustmentOrderGoodsPO priceAdjustmentOrderGoodsPO : priceAdjustmentOrderGoodsPOList) {
            priceAdjustmentOrderGoodsPO.setPriceAdjustmentOrderId(priceAdjustmentOrderPO.getId());
        }
        priceAdjustmentOrderGoodsMapperService.saveBatch(priceAdjustmentOrderGoodsPOList);
        for (int i = 0; i < priceAdjustmentOrderGoodsPOList.size(); i++) {
            priceAdjustmentOrderGoodsChangeInfoPOList.get(i).setPriceAdjustmentOrderGoodsId(priceAdjustmentOrderGoodsPOList.get(i).getId());
        }
        priceAdjustmentOrderGoodsChangeInfoMapperService.saveBatch(priceAdjustmentOrderGoodsChangeInfoPOList);
    }

    /**
     * 组装价格调整单商品价格调整信息
     *
     * @param storeGoodsAvailableForSaleAdjustmentPriceDTO 请求参数
     * @param priceAdjustmentOrderPO                       价格调整单信息
     * @param sectionDTO                                   时段调整DTO
     * @return 价格调整单商品价格调整信息
     */
    private static PriceAdjustmentOrderGoodsChangeInfoPO assemblePriceAdjustmentOrderGoodsChangeInfoPO(
            StoreGoodsAvailableForSaleAdjustmentPriceDTO storeGoodsAvailableForSaleAdjustmentPriceDTO,
            PriceAdjustmentOrderPO priceAdjustmentOrderPO,
            StoreGoodsAvailableForSaleAdjustmentPriceDTO.SectionDTO sectionDTO) {
        PriceAdjustmentOrderGoodsChangeInfoPO priceAdjustmentOrderGoodsChangeInfoPO = new PriceAdjustmentOrderGoodsChangeInfoPO();
        priceAdjustmentOrderGoodsChangeInfoPO.setGoodsId(storeGoodsAvailableForSaleAdjustmentPriceDTO.getStoreGoodsId());
        priceAdjustmentOrderGoodsChangeInfoPO.setPriceAdjustmentOrderId(priceAdjustmentOrderPO.getId());
        priceAdjustmentOrderGoodsChangeInfoPO.setGoodsId(storeGoodsAvailableForSaleAdjustmentPriceDTO.getStoreGoodsId());
        // 目前只能修改默认方案的时段价格，所以只会有全天
        priceAdjustmentOrderGoodsChangeInfoPO.setAdjustmentTime("全天");
        priceAdjustmentOrderGoodsChangeInfoPO.setSalesProgramPrice(sectionDTO.getSalesProgramPrice());
        priceAdjustmentOrderGoodsChangeInfoPO.setAdjustmentPrice(sectionDTO.getAdjustmentPrice());
        priceAdjustmentOrderGoodsChangeInfoPO.setGoodsSaleName(sectionDTO.getGoodsSaleName());
        priceAdjustmentOrderGoodsChangeInfoPO.setStoreSalesProgramName(sectionDTO.getStoreSalesProgramName());
        priceAdjustmentOrderGoodsChangeInfoPO.setStoreSalesProgramId(sectionDTO.getStoreSalesProgramId());
        priceAdjustmentOrderGoodsChangeInfoPO.setStoreSalesProgramGoodsId(sectionDTO.getStoreSalesProgramGoodsId());
        priceAdjustmentOrderGoodsChangeInfoPO.setStoreSalesProgramGoodsPriceId(sectionDTO.getStoreSalesProgramGoodsPriceId());
        priceAdjustmentOrderGoodsChangeInfoPO.setSalesProgramIsDefault(sectionDTO.getSalesProgramIsDefault());
        priceAdjustmentOrderGoodsChangeInfoPO.setSalesProgramStartTime(sectionDTO.getSalesProgramStartTime());
        priceAdjustmentOrderGoodsChangeInfoPO.setSalesProgramEndTime(sectionDTO.getSalesProgramEndTime());
        return priceAdjustmentOrderGoodsChangeInfoPO;
    }

    /**
     * 组装调价单商品信息
     *
     * @param storeGoodsAvailableForSaleAdjustmentPriceDTO 请求参数
     * @param sectionAdjustmentDTO                         时段调整DTO
     * @param saleCategoryNameList                         销售分类名称列表
     * @param saleCategoryNodeList                         销售分类节点列表
     * @return 调价单商品信息
     */
    private PriceAdjustmentOrderGoodsPO assemblePriceAdjustmentOrderGoodsPO(
            StoreGoodsAvailableForSaleAdjustmentPriceDTO storeGoodsAvailableForSaleAdjustmentPriceDTO,
            StoreGoodsAvailableForSaleAdjustmentPriceDTO.TimeSectionAdjustmentDTO sectionAdjustmentDTO,
            List<String> saleCategoryNameList,
            List<PriceAdjustmentOrderGoodsPO.SaleCategoryNode> saleCategoryNodeList) {
        PriceAdjustmentOrderGoodsPO priceAdjustmentOrderGoodsPO = new PriceAdjustmentOrderGoodsPO();
        priceAdjustmentOrderGoodsPO.setGoodsId(storeGoodsAvailableForSaleAdjustmentPriceDTO.getStoreGoodsId());
        priceAdjustmentOrderGoodsPO.setGoodsName(storeGoodsAvailableForSaleAdjustmentPriceDTO.getStoreGoodsName());
        priceAdjustmentOrderGoodsPO.setGoodsUuid(storeGoodsAvailableForSaleAdjustmentPriceDTO.getGoodsUuid());
        priceAdjustmentOrderGoodsPO.setBarcode(sectionAdjustmentDTO.getBarcode());
        priceAdjustmentOrderGoodsPO.setGoodsUnit(sectionAdjustmentDTO.getGoodsUnit());
        priceAdjustmentOrderGoodsPO.setSellingPrice(storeGoodsAvailableForSaleAdjustmentPriceDTO.getSellingPrice());
        priceAdjustmentOrderGoodsPO.setGoodsSpec(storeGoodsAvailableForSaleAdjustmentPriceDTO.getGoodsSpec());
        priceAdjustmentOrderGoodsPO.setSaleCategoryName(saleCategoryNameList);
        priceAdjustmentOrderGoodsPO.setSaleCategoryNode(saleCategoryNodeList);
        priceAdjustmentOrderGoodsPO.setCosts(sectionAdjustmentDTO.getCosts());
        priceAdjustmentOrderGoodsPO.setCover(sectionAdjustmentDTO.getCover());
        priceAdjustmentOrderGoodsPO.setGoodsPackageSkuId(sectionAdjustmentDTO.getGoodsPackageSkuId());
        priceAdjustmentOrderGoodsPO.setGoodsUnitName(sectionAdjustmentDTO.getGoodsUnitName());
        priceAdjustmentOrderGoodsPO.setSpuCode(storeGoodsAvailableForSaleAdjustmentPriceDTO.getSpuCode());
        priceAdjustmentOrderGoodsPO.setSkuCode(sectionAdjustmentDTO.getSkuCode());
        priceAdjustmentOrderGoodsPO.setGoodsSpecification(
                priceAdjustmentOrderGoodsConverter.toGoodsSpecificationList(sectionAdjustmentDTO.getStoreGoodsSpecRelationList()));
        return priceAdjustmentOrderGoodsPO;
    }

    /**
     * 校验价格调整参数
     *
     * @param storeGoodsId             商品ID
     * @param storeSalesProgramId      销售方案ID
     * @param storeSalesProgramGoodsId 销售方案商品ID
     */
    private void validatePriceAdjustment(Integer storeGoodsId, Integer storeSalesProgramId, Integer storeSalesProgramGoodsId) {
        StoreGoodsPO storeGoodsPO = storeGoodsMapperService.getById(storeGoodsId);
        if (storeGoodsPO == null) {
            throw new StoreBaseException(ResponseCode.GOODS_IS_DELETED, GOODS_IS_DELETED);
        }
        if (Objects.equals(storeGoodsPO.getStatus(), StoreGoodsStatus.DISABLE.getValue())) {
            throw new StoreBaseException(ResponseCode.GOODS_IS_DISABLE, GOODS_IS_DISABLE);
        }
        StoreSalesProgramPO storeSalesProgramPO = storeSalesProgramMapperService.getById(storeSalesProgramId);
        if (storeSalesProgramPO == null) {
            throw new StoreBaseException(ResponseCode.SALES_PROGRAM_IS_DELETED, SALES_PROGRAM_IS_DELETED);
        }
        if (Objects.equals(storeSalesProgramPO.getIsEnable(), StoreSalesProgramStatus.DISABLE.isValue())) {
            throw new StoreBaseException(ResponseCode.SALES_PROGRAM_IS_DISABLE, SALES_PROGRAM_IS_DISABLE);
        }
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeSalesProgramGoodsMapperService.lambdaQuery()
                .select(StoreSalesProgramGoodsPO::getId)
                .eq(StoreSalesProgramGoodsPO::getGoodsId, storeGoodsId)
                .list();
        if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
            throw new StoreBaseException(ResponseCode.GOODS_NOT_IN_SALES_PROGRAM, GOODS_NOT_IN_SALES_PROGRAM);
        }
        for (StoreSalesProgramGoodsPO storeSalesProgramGoodsPO : storeSalesProgramGoodsPOList) {
            if (Objects.equals(storeSalesProgramGoodsPO.getId(), storeSalesProgramGoodsId)) {
                return;
            }
        }
        throw new StoreBaseException(ResponseCode.GOODS_NOT_IN_SALES_PROGRAM, GOODS_NOT_IN_SALES_PROGRAM);
    }

    @Override
    public PageRespVO<StoreGoodsAvailableForSaleAdjustmentPriceVO> queryStoreGoodsAvailableForSalePriceAdjustmentList(
            QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO queryDTO) {
        // 校验查询在售商品详情DTO
        checkQueryStoreGoodsAvailableForSalePriceAdjustmentListDTO(queryDTO);
        if (queryDTO.getTimeSearch() != null) {
            Map<String, Date> startAndEndDateTimeByTimeShortcut = DateUtils.getStartAndEndDateTimeByTimeShortcut(queryDTO.getTimeSearch());
            queryDTO.setStartTime(startAndEndDateTimeByTimeShortcut.get("startTime"));
            queryDTO.setEndTime(startAndEndDateTimeByTimeShortcut.get("endTime"));
        }
        // 查询出POS相关的调价单
        List<PriceAdjustmentOrderPO> priceAdjustmentOrderPOList = priceAdjustmentOrderMapperService.lambdaQuery()
                .eq(PriceAdjustmentOrderPO::getStoreId, queryDTO.getStoreId())
                .eq(PriceAdjustmentOrderPO::getChannelId, queryDTO.getChannelId())
                .in(PriceAdjustmentOrderPO::getSourceType, List.of(2, 3))
                .between(!Objects.isNull(queryDTO.getStartTime()), PriceAdjustmentOrderPO::getCreatedAt, queryDTO.getStartTime(), queryDTO.getEndTime())
                .list();
        if (CollectionUtils.isEmpty(priceAdjustmentOrderPOList)) {
            return new PageRespVO<>(queryDTO);
        }

        if (CollectionUtils.isEmpty(priceAdjustmentOrderPOList)) {
            return new PageRespVO<>(queryDTO);
        }
        // 组装调价单商品ID列表,用于后续信息的组装
        List<Integer> priceAdjustmentOrderGoodsIdList = new ArrayList<>();
        // 组装返回结果信息，需要以调价单商品为维度进行组装，每条信息就是一个商品的调价信息，如果一个调价单内有多个商品，则返回多条调价单商品信息
        List<StoreGoodsAvailableForSaleAdjustmentPriceVO> storeGoodsAvailableForSaleAdjustmentPriceVOList = new ArrayList<>();
        // 组装调价单商品信息
        Set<Integer> coverIdSet = assembleResultListAndGoodsIdList(priceAdjustmentOrderPOList, priceAdjustmentOrderGoodsIdList, storeGoodsAvailableForSaleAdjustmentPriceVOList);
        // 过滤查询在售商品详情列表
        filterStoreGoodsAvailableForSalePriceAdjustmentList(queryDTO, storeGoodsAvailableForSaleAdjustmentPriceVOList);
        if (CollectionUtils.isEmpty(priceAdjustmentOrderPOList)) {
            return new PageRespVO<>(queryDTO);
        }
        // 排序
        storeGoodsAvailableForSaleAdjustmentPriceVOList.sort(Comparator.comparing(StoreGoodsAvailableForSaleAdjustmentPriceVO::getCreatedAt).reversed());
        // 组装商品封面
        assembleStoreGoodsAvailableForSaleAdjustmentPricePicture(coverIdSet, storeGoodsAvailableForSaleAdjustmentPriceVOList);
        return new PageRespVO<>(storeGoodsAvailableForSaleAdjustmentPriceVOList, queryDTO.getLimit(), queryDTO.getPage());
    }

    /**
     * 组装返回结果信息，需要以调价单商品为维度进行组装，每条信息就是一个商品的调价信息，如果一个调价单内有多个商品，则返回多条调价单商品信息
     *
     * @param priceAdjustmentOrderPOList                      调价单列表
     * @param priceAdjustmentOrderGoodsIdList                 调价单商品ID列表
     * @param storeGoodsAvailableForSaleAdjustmentPriceVOList 商品列表
     * @return 商品封面ID集合
     */
    private Set<Integer> assembleResultListAndGoodsIdList(List<PriceAdjustmentOrderPO> priceAdjustmentOrderPOList,
                                                          List<Integer> priceAdjustmentOrderGoodsIdList,
                                                          List<StoreGoodsAvailableForSaleAdjustmentPriceVO> storeGoodsAvailableForSaleAdjustmentPriceVOList) {
        // 组装调价单ID列表和调价单信息的映射
        List<Integer> priceAdjustmentOrderIdList = new ArrayList<>();
        Map<Integer, PriceAdjustmentOrderPO> priceAdjustmentOrderIdAndInfoMap = new HashMap<>();
        for (PriceAdjustmentOrderPO priceAdjustmentOrderPO : priceAdjustmentOrderPOList) {
            priceAdjustmentOrderIdList.add(priceAdjustmentOrderPO.getId());
            priceAdjustmentOrderIdAndInfoMap.put(priceAdjustmentOrderPO.getId(), priceAdjustmentOrderPO);
        }
        // 查询每个调价单对应的商品信息
        List<PriceAdjustmentOrderGoodsPO> priceAdjustmentOrderGoodsPOList = priceAdjustmentOrderGoodsMapperService.lambdaQuery()
                .in(PriceAdjustmentOrderGoodsPO::getPriceAdjustmentOrderId, priceAdjustmentOrderIdList)
                .list();
        // 查询每个调价单对应的商品信息
        List<PriceAdjustmentOrderGoodsChangeInfoPO> priceAdjustmentOrderGoodschangeInfoPOList = priceAdjustmentOrderGoodsChangeInfoMapperService.lambdaQuery()
                .in(PriceAdjustmentOrderGoodsChangeInfoPO::getPriceAdjustmentOrderId, priceAdjustmentOrderIdList)
                .list();
        Map<Integer, PriceAdjustmentOrderGoodsChangeInfoPO> priceAdjustmentOrderGoodsIdAndChangeInfoMap =
                priceAdjustmentOrderGoodschangeInfoPOList.stream().collect(toMap(PriceAdjustmentOrderGoodsChangeInfoPO::getPriceAdjustmentOrderGoodsId, Function.identity()));
        Set<Integer> coverIdSet = new HashSet<>();
        // 以商品信息为维度去循环组装数据
        for (PriceAdjustmentOrderGoodsPO priceAdjustmentOrderGoodsPO : priceAdjustmentOrderGoodsPOList) {
            priceAdjustmentOrderGoodsIdList.add(priceAdjustmentOrderGoodsPO.getId());
            // 先给结果组装 调价单相关的信息
            StoreGoodsAvailableForSaleAdjustmentPriceVO storeGoodsAvailableForSaleAdjustmentPriceVO =
                    priceAdjustmentOrderConverter.toStoreGoodsAvailableForSaleAdjustmentPriceVO(priceAdjustmentOrderIdAndInfoMap.get(priceAdjustmentOrderGoodsPO.getPriceAdjustmentOrderId()));
            // 再给结果组装 商品相关的信息
            priceAdjustmentOrderGoodsConverter.toStoreGoodsAvailableForSaleAdjustmentPriceVO(priceAdjustmentOrderGoodsPO,
                    storeGoodsAvailableForSaleAdjustmentPriceVO);
            PriceAdjustmentOrderGoodsChangeInfoPO priceAdjustmentOrderGoodsChangeInfoPO =
                    priceAdjustmentOrderGoodsIdAndChangeInfoMap.get(priceAdjustmentOrderGoodsPO.getId());
            priceAdjustmentOrderGoodsChangeInfoConverter.toStoreGoodsAvailableForSaleAdjustmentPriceVO(priceAdjustmentOrderGoodsChangeInfoPO, storeGoodsAvailableForSaleAdjustmentPriceVO);
            storeGoodsAvailableForSaleAdjustmentPriceVOList.add(storeGoodsAvailableForSaleAdjustmentPriceVO);
            if (priceAdjustmentOrderGoodsPO.getCover() != null) {
                coverIdSet.add(priceAdjustmentOrderGoodsPO.getCover());
            }
        }
        return coverIdSet;
    }

    /**
     * 过滤查询在售商品详情列表
     *
     * @param queryDTO                                        查询条件
     * @param storeGoodsAvailableForSaleAdjustmentPriceVOList 商品列表
     */
    private static void filterStoreGoodsAvailableForSalePriceAdjustmentList(QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO queryDTO,
                                                                            List<StoreGoodsAvailableForSaleAdjustmentPriceVO> storeGoodsAvailableForSaleAdjustmentPriceVOList) {
        if (StringUtils.isEmpty(queryDTO.getKeywords())) {
            return;
        }
        storeGoodsAvailableForSaleAdjustmentPriceVOList.removeIf(
                storeGoodsAvailableForSaleAdjustmentPriceVO ->
                        // 店铺商品名称
                        !storeGoodsAvailableForSaleAdjustmentPriceVO.getStoreGoodsName().toLowerCase().contains(queryDTO.getKeywords().toLowerCase()) &&
                                // 商品商品名称
                                !storeGoodsAvailableForSaleAdjustmentPriceVO.getGoodsSaleName().toLowerCase().contains(queryDTO.getKeywords().toLowerCase()) &&
                                // 调价人名称
                                !storeGoodsAvailableForSaleAdjustmentPriceVO.getUsername().toLowerCase().contains(queryDTO.getKeywords().toLowerCase()) &&
                                // 调价人账号
                                !storeGoodsAvailableForSaleAdjustmentPriceVO.getAccount().toLowerCase().contains(queryDTO.getKeywords().toLowerCase()));
    }

    /**
     * 组装商品封面
     *
     * @param coverIdSet                                      商品图片ID集合
     * @param storeGoodsAvailableForSaleAdjustmentPriceVOList 商品列表
     */
    private void assembleStoreGoodsAvailableForSaleAdjustmentPricePicture(
            Set<Integer> coverIdSet, List<StoreGoodsAvailableForSaleAdjustmentPriceVO> storeGoodsAvailableForSaleAdjustmentPriceVOList) {
        if (CollectionUtils.isNotEmpty(coverIdSet)) {
            Map<Integer, GoodsPictureVO> coverIdAndUrlMap = goodsPictureConverter.toGoodsPictureVOList(
                    uploadFilesMapperService.listByIds(coverIdSet)).stream().collect(toMap(GoodsPictureVO::getId, Function.identity()));
            storeGoodsAvailableForSaleAdjustmentPriceVOList.forEach(storeGoodsAvailableForSaleAdjustmentPriceVO -> {
                if (storeGoodsAvailableForSaleAdjustmentPriceVO.getCover() != null) {
                    storeGoodsAvailableForSaleAdjustmentPriceVO.setPicture(coverIdAndUrlMap.get(storeGoodsAvailableForSaleAdjustmentPriceVO.getCover()));
                }
            });
        }
    }

    /**
     * 校验查询在售商品详情DTO
     *
     * @param queryDTO 查询在售商品详情DTO
     */
    private void checkQueryStoreGoodsAvailableForSalePriceAdjustmentListDTO(QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO queryDTO) {
        if ((queryDTO.getStartTime() != null && queryDTO.getEndTime() == null) ||
                (queryDTO.getStartTime() == null && queryDTO.getEndTime() != null)) {
            throw new StoreBaseException(ResponseCode.COMMON_BAD_REQUEST, "开始时间和结束时间必须同时存在或同时不存在");
        }
    }

    @Override
    public PageRespVO<StoreGoodsExtendVO> queryStoreGoodsAvailableForSaleWeighListPage(
            QueryStoreGoodsAvailableForSaleWeighListDTO queryStoreGoodsAvailableForSalePosListDTO) {
        List<StoreGoodsPO> storeGoodsPOList = commonService.queryStoreGoodsForAvailableForSaleWeighList(
                QueryStoreGoodsForAvailableForSaleWeighListBO.builder()
                        .storeId(queryStoreGoodsAvailableForSalePosListDTO.getStoreId())
                        .storeGoodsIdList(queryStoreGoodsAvailableForSalePosListDTO.getStoreGoodsIdList())
                        .channelId(queryStoreGoodsAvailableForSalePosListDTO.getChannelId()).build());
        if (CollectionUtils.isEmpty(storeGoodsPOList)) {
            return new PageRespVO<>(queryStoreGoodsAvailableForSalePosListDTO);
        }
        // 根据销售分组ID过滤商品列表(不包含其子分组)
        commonService.filterStoreGoodsListBySaleCategoryIdListOneself(storeGoodsPOList,
                queryStoreGoodsAvailableForSalePosListDTO.getSaleCategoryIdList());
        List<StoreGoodsExtendVO> storeGoodsExtendVOList = storeGoodsConverter.toStoreGoodsExtendVOList(storeGoodsPOList);
        // 给可售商品组装对应方案的售卖信息
        // 组装商品名称、销售价格、商品图片
        commonService.assembleGoodsNameAndSalePriceAndPictureForWeighList(storeGoodsExtendVOList,
                queryStoreGoodsAvailableForSalePosListDTO.getStoreId(), queryStoreGoodsAvailableForSalePosListDTO.getChannelId());
        // 组装秤内码数据
        goodsScaleCodeService.assembleGoodsScaleCode(storeGoodsExtendVOList, queryStoreGoodsAvailableForSalePosListDTO.getStoreId());
        // 根据关键字过滤商品列表, 包括商品售卖名称、商品拼音、秤内自编码、商品条形码
        commonService.filterStoreGoodsExtendVOListByKeywordsForSaleNameAndPinyinAndCustomCode(storeGoodsExtendVOList,
                queryStoreGoodsAvailableForSalePosListDTO.getKeywords());
        // 查询商品扩展信息
        commonService.assembleGoodsExpandInfo(storeGoodsExtendVOList, queryStoreGoodsAvailableForSalePosListDTO.getStoreGoodsExpandList(),
                queryStoreGoodsAvailableForSalePosListDTO.getStoreId(), queryStoreGoodsAvailableForSalePosListDTO.getChannelId(),
                queryStoreGoodsAvailableForSalePosListDTO.getScaleType());
        return new PageRespVO<>(storeGoodsExtendVOList, queryStoreGoodsAvailableForSalePosListDTO.getLimit(),
                queryStoreGoodsAvailableForSalePosListDTO.getPage());
    }

    @Override
    public GoodsScaleCodeVO generateStoreGoodsPluCode(GenerateStoreGoodsPluCodeDTO generateStoreGoodsPluCodeDTO) {
        StoreGoodsPO storeGoodsPO = storeGoodsMapperService.lambdaQuery()
                .select(StoreGoodsPO::getStoreId)
                .eq(StoreGoodsPO::getId, generateStoreGoodsPluCodeDTO.getStoreGoodsId())
                .eq(StoreGoodsPO::getStatus, StoreGoodsStatus.ENABLE.getValue())
                .one();
        if (storeGoodsPO == null) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "商品不存在或已下架");
        }
        RLock lock = redissonClient.getLock(RETAIL_GOODS_PLU_CODE_LOCK_PREFIX +
                storeGoodsPO.getStoreId() + "_" + generateStoreGoodsPluCodeDTO.getScaleType());
        lock.lock();
        try {
            // 填充四位
            String pluCode = String.format(POSITION_FILLING_FOUR, generateStoreGoodsPluCodeDTO.getPluCode());
            List<String> pluCodeList = goodsScaleCodeMapperService.lambdaQuery().select(GoodsScaleCodePO::getPluCode)
                    .eq(GoodsScaleCodePO::getStoreId, storeGoodsPO.getStoreId())
                    .eq(GoodsScaleCodePO::getScaleType, generateStoreGoodsPluCodeDTO.getScaleType())
                    .ne(GoodsScaleCodePO::getGoodsId, generateStoreGoodsPluCodeDTO.getStoreGoodsId())
                    .list().stream().map(GoodsScaleCodePO::getPluCode).toList();
            if (pluCodeList.contains(pluCode)) {
                throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "该秤内码已存在，请重新输入！");
            }
            GoodsScaleCodePO goodsScaleCodePOInDb = goodsScaleCodeMapperService.lambdaQuery()
                    .eq(GoodsScaleCodePO::getStoreId, storeGoodsPO.getStoreId())
                    .eq(GoodsScaleCodePO::getScaleType, generateStoreGoodsPluCodeDTO.getScaleType())
                    .eq(GoodsScaleCodePO::getGoodsId, generateStoreGoodsPluCodeDTO.getStoreGoodsId()).one();
            if (goodsScaleCodePOInDb != null) {
                if (goodsScaleCodePOInDb.getPluCode().equals(pluCode)) {
                    return goodsScaleCodeConverter.toGoodsScaleCodeVO(goodsScaleCodePOInDb);
                }
                goodsScaleCodePOInDb.setPluCode(pluCode);
                goodsScaleCodePOInDb.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
                goodsScaleCodeMapperService.updateById(goodsScaleCodePOInDb);
            } else {
                goodsScaleCodePOInDb = new GoodsScaleCodePO();
                goodsScaleCodePOInDb.setGoodsId(generateStoreGoodsPluCodeDTO.getStoreGoodsId());
                goodsScaleCodePOInDb.setStoreId(storeGoodsPO.getStoreId());
                goodsScaleCodePOInDb.setScaleType(generateStoreGoodsPluCodeDTO.getScaleType());
                goodsScaleCodePOInDb.setPluCode(pluCode);
                goodsScaleCodeMapperService.save(goodsScaleCodePOInDb);
            }
            return goodsScaleCodeConverter.toGoodsScaleCodeVO(goodsScaleCodePOInDb);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<GoodsScaleCodeVO> generateStoreGoodsPluCodeBatch(GenerateBatchStoreGoodsPluCodeDTO generateBatchStoreGoodsPluCodeDTO) {
        StoreGoodsPO storeGoodsPO = storeGoodsMapperService.lambdaQuery()
                .select(StoreGoodsPO::getStoreId)
                .eq(StoreGoodsPO::getId, generateBatchStoreGoodsPluCodeDTO.getStoreGoodsIdList().get(0))
                .eq(StoreGoodsPO::getStatus, StoreGoodsStatus.ENABLE.getValue())
                .one();
        if (storeGoodsPO == null) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "商品不存在或已下架");
        }
        RLock lock = redissonClient.getLock(RETAIL_GOODS_PLU_CODE_LOCK_PREFIX +
                storeGoodsPO.getStoreId() + "_" + generateBatchStoreGoodsPluCodeDTO.getScaleType());
        lock.lock();
        try {
            // 先查询数据库中已经存在的秤内码
            List<GoodsScaleCodePO> allGoodsScaleCodePOInDbList = goodsScaleCodeMapperService.lambdaQuery()
                    .eq(GoodsScaleCodePO::getScaleType, generateBatchStoreGoodsPluCodeDTO.getScaleType())
                    .eq(GoodsScaleCodePO::getStoreId, storeGoodsPO.getStoreId())
                    .list();
            // 区分需要生成秤内码但是已经有秤内码的商品
            List<GoodsScaleCodePO> goodsScaleCodePOAfferentButInDbList = new ArrayList<>();
            // 获取已存在的秤内码但是排除了当前商品的秤内码
            List<String> pluCodeExistsExcludeAfferentGoodsPluCodeList = new ArrayList<>();
            for (GoodsScaleCodePO goodsScaleCodePO : allGoodsScaleCodePOInDbList) {
                if (generateBatchStoreGoodsPluCodeDTO.getStoreGoodsIdList().contains(goodsScaleCodePO.getGoodsId())) {
                    goodsScaleCodePOAfferentButInDbList.add(goodsScaleCodePO);
                } else {
                    pluCodeExistsExcludeAfferentGoodsPluCodeList.add(goodsScaleCodePO.getPluCode());
                }
            }
            Map<Integer, GoodsScaleCodePO> storeGoodsIdAndInfoMap = goodsScaleCodePOAfferentButInDbList.stream()
                    .collect(toMap(GoodsScaleCodePO::getGoodsId, Function.identity()));
            // 生成新的秤内码
            List<String> newPluCodeList = generatePluCodeList(generateBatchStoreGoodsPluCodeDTO.getPluCodeStart(),
                    generateBatchStoreGoodsPluCodeDTO.getStoreGoodsIdList().size(),
                    pluCodeExistsExcludeAfferentGoodsPluCodeList);
            if (newPluCodeList.size() < generateBatchStoreGoodsPluCodeDTO.getStoreGoodsIdList().size()) {
                // 提示语示例: 生成失败，传入商品7个但当前9996~9999可生成秤内码数量为4，请重试！
                throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER,
                        "生成失败，传入商品" + generateBatchStoreGoodsPluCodeDTO.getStoreGoodsIdList().size() + "个但当前" +
                                generateBatchStoreGoodsPluCodeDTO.getPluCodeStart() + "~9999可生成秤内码数量为" + newPluCodeList.size() + "，请重试！");
            }
            List<GoodsScaleCodePO> needSaveGoodsScaleCodePOList = new ArrayList<>();
            for (int i = 0; i < generateBatchStoreGoodsPluCodeDTO.getStoreGoodsIdList().size(); i++) {
                Integer storeGoodsId = generateBatchStoreGoodsPluCodeDTO.getStoreGoodsIdList().get(i);
                if (storeGoodsIdAndInfoMap.containsKey(storeGoodsId)) {
                    GoodsScaleCodePO goodsScaleCodePO = storeGoodsIdAndInfoMap.get(storeGoodsId);
                    goodsScaleCodePO.setPluCode(newPluCodeList.get(i));
                    goodsScaleCodePO.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
                } else {
                    GoodsScaleCodePO goodsScaleCodePO = new GoodsScaleCodePO();
                    goodsScaleCodePO.setGoodsId(storeGoodsId);
                    goodsScaleCodePO.setStoreId(storeGoodsPO.getStoreId());
                    goodsScaleCodePO.setScaleType(generateBatchStoreGoodsPluCodeDTO.getScaleType());
                    goodsScaleCodePO.setPluCode(newPluCodeList.get(i));
                    needSaveGoodsScaleCodePOList.add(goodsScaleCodePO);
                }
            }
            if (CollectionUtils.isNotEmpty(needSaveGoodsScaleCodePOList)) {
                goodsScaleCodeMapperService.saveBatch(needSaveGoodsScaleCodePOList);
            }
            if (CollectionUtils.isNotEmpty(storeGoodsIdAndInfoMap.values())) {
                goodsScaleCodeMapperService.updateBatchById(storeGoodsIdAndInfoMap.values());
                needSaveGoodsScaleCodePOList.addAll(storeGoodsIdAndInfoMap.values());
            }
            return goodsScaleCodeConverter.toGoodsScaleCodeVOList(needSaveGoodsScaleCodePOList);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleScaleCode(
            VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO) {
        List<StoreGoodsExtendVO> storeGoodsExtendVOList = queryStoreGoodsAvailableForSaleList(verifyStoreGoodsAvailableForSaleScaleCodeDTO);
        // 组装校验结果
        VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleScaleCodeVO = assembleVerifyScaleCodeAndValuationResult(storeGoodsExtendVOList);
        // 生成秤内自编码
        generateScaleCustomCode(verifyStoreGoodsAvailableForSaleScaleCodeDTO, verifyStoreGoodsAvailableForSaleScaleCodeVO);
        return verifyStoreGoodsAvailableForSaleScaleCodeVO;
    }

    @Override
    public VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleExportScaleCode(
            VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO) {
        List<StoreGoodsExtendVO> storeGoodsExtendVOList = queryStoreGoodsAvailableForSaleList(verifyStoreGoodsAvailableForSaleScaleCodeDTO);
        // 组装校验结果
        return assembleVerifyValuationResult(storeGoodsExtendVOList);
    }

    @Override
    public PageRespVO<StoreGoodsExtendVO> queryStorePosGoodsAvailableForSaleListPage(
            QueryStorePosGoodsAvailableForSaleListDTO queryDTO) {
        StoreSalesProgramPO defaultStoreSalesProgramPO = storeSalesProgramMapperService.lambdaQuery()
                .eq(StoreSalesProgramPO::getStoreId, queryDTO.getStoreId())
                .eq(StoreSalesProgramPO::getChannelId, queryDTO.getChannelId())
                .eq(StoreSalesProgramPO::getIsDefault, true)
                .one();
        if (defaultStoreSalesProgramPO == null) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "POS通道未设置默认销售方案");
        }
        Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsListMap = new HashMap<>();
        QueryStorePosGoodsAvailableForSaleBO queryStorePosGoodsAvailableForSaleBO = QueryStorePosGoodsAvailableForSaleBO.builder()
                .keywords(queryDTO.getKeywords())
                .brandIdList(queryDTO.getBrandIdList())
                .saleCategoryId(queryDTO.getSaleCategoryId())
                .channelId(queryDTO.getChannelId())
                .storeSource(queryDTO.getStoreSource())
                .coverIdList(queryDTO.getCoverIdList())
                .goodsValuationMethodList(queryDTO.getGoodsValuationMethodList())
                .goodsSpec(queryDTO.getGoodsSpec())
                .build();
        List<StoreGoodsExtendVO> storeGoodsExtendVOList =
                commonService.queryStorePosGoodsAvailableForSaleList(queryStorePosGoodsAvailableForSaleBO, defaultStoreSalesProgramPO, storeGoodsIdAndSalesProgramGoodsListMap);
        if (CollectionUtils.isEmpty(storeGoodsExtendVOList)) {
            return new PageRespVO<>(queryDTO);
        }
        // 对商品进行排序
        storeGoodsExtendVOList = commonService.sortStoreGoodsBaseVOList(queryDTO.getStoreId(), queryDTO.getChannelId(), storeGoodsExtendVOList);
        PageRespVO<StoreGoodsExtendVO> storeGoodsExtendVOPageRespVO = new PageRespVO<>(storeGoodsExtendVOList, queryDTO.getLimit(), queryDTO.getPage());
        List<StoreGoodsExtendVO> storeGoodsExtendVOPageRespVOList = storeGoodsExtendVOPageRespVO.getList();
        if (CollectionUtils.isEmpty(storeGoodsExtendVOPageRespVOList)) {
            return storeGoodsExtendVOPageRespVO;
        }

        assemblePosGoodsExtendInfo(storeGoodsExtendVOPageRespVOList, queryDTO, defaultStoreSalesProgramPO, storeGoodsIdAndSalesProgramGoodsListMap);
        return storeGoodsExtendVOPageRespVO;
    }

    @Override
    public StoreGoodsExtendVO queryStorePosGoodsAvailableForSaleDetail(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO) {
        StoreGoodsPO storeGoodsPO = storeGoodsMapperService.getById(queryDTO.getStoreGoodsId());
        StoreGoodsExtendVO storeGoodsExtendVO = storeGoodsConverter.toStoreGoodsExtendVO(storeGoodsPO);
        if (storeGoodsExtendVO == null) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "商品不存在");
        }
        // 组装POS商品的售卖信息
        assemblePosGoodsSellingInfo(queryDTO, storeGoodsExtendVO);
        // 组装POS商品的分类信息
        assemblePosGoodsStoreGoodsCategoryInfo(storeGoodsExtendVO);
        // 组装POS商品的分组信息
        assemblePosGoodsSaleCategoryInfoList(queryDTO, storeGoodsExtendVO);
        // 组装POS商品的标签信息
        assemblePosGoodsLabelInfo(storeGoodsExtendVO, storeGoodsPO.getGoodsLabel());
        // 组装POS商品的品牌信息
        assemblePosGoodsBrandInfo(storeGoodsExtendVO);
        // 组装POS商品的单位信息
        assemblePosGoodsUnitInfo(storeGoodsExtendVO);
        // 组装POS商品的封面信息
        assemblePosGoodsCoverInfo(storeGoodsExtendVO);
        // 组装POS商品的秤内码信息
        assemblePosGoodsScaleCodeInfo(storeGoodsExtendVO, queryDTO.getStoreId());
        return storeGoodsExtendVO;
    }

    /**
     * 组装POS商品详情的秤内码信息
     *
     * @param storeGoodsExtendVO 商品详情信息
     */
    private void assemblePosGoodsScaleCodeInfo(StoreGoodsExtendVO storeGoodsExtendVO, Integer storeId) {
        List<GoodsScaleCodePO> goodsScaleCodePOList = goodsScaleCodeMapperService.lambdaQuery()
                .eq(GoodsScaleCodePO::getGoodsId, storeGoodsExtendVO.getId())
                .eq(GoodsScaleCodePO::getStoreId, storeId)
                .list();
        storeGoodsExtendVO.setGoodsScaleCodeList(goodsScaleCodeConverter.toStoreGoodsScaleCodeVOList(goodsScaleCodePOList));
    }

    /**
     * 组装POS商品详情的封面信息
     *
     * @param storeGoodsExtendVO 商品详情信息
     */
    private void assemblePosGoodsCoverInfo(StoreGoodsExtendVO storeGoodsExtendVO) {
        if (storeGoodsExtendVO.getCover() == null) {
            return;
        }
        storeGoodsExtendVO.setPicture(List.of(goodsPictureConverter.toGoodsPictureVO(
                uploadFilesMapperService.getById(storeGoodsExtendVO.getCover()))));
    }

    /**
     * 组装POS商品详情的单位信息
     *
     * @param storeGoodsExtendVO 商品详情信息
     */
    private void assemblePosGoodsUnitInfo(StoreGoodsExtendVO storeGoodsExtendVO) {
        if (storeGoodsExtendVO.getGoodsUnit() == null) {
            return;
        }
        SaleUnitMeasurementPO saleUnitMeasurementPO = saleUnitMeasurementMapperService.getById(storeGoodsExtendVO.getGoodsUnit());
        if (saleUnitMeasurementPO == null) {
            return;
        }
        storeGoodsExtendVO.setGoodsUnitName(saleUnitMeasurementPO.getUnitName());
    }

    /**
     * 组装POS商品详情的品牌信息
     *
     * @param storeGoodsExtendVO 商品详情信息
     */
    private void assemblePosGoodsBrandInfo(StoreGoodsExtendVO storeGoodsExtendVO) {
        if (storeGoodsExtendVO.getBrand() == null) {
            return;
        }
        List<GoodsBrandExtendPO> goodsBrandExtendPOS = goodsBrandMapperService.queryGoodsBrandExtendListByIdList(List.of(storeGoodsExtendVO.getBrand()));
        if (CollectionUtils.isEmpty(goodsBrandExtendPOS)) {
            return;
        }
        storeGoodsExtendVO.setBrandInfo(goodsBrandConverter.toGoodsBrandVOList(goodsBrandExtendPOS).get(0));
    }

    /**
     * 组装POS商品的标签信息
     *
     * @param storeGoodsExtendVO 商品详情信息
     * @param goodsLabelId 标签id
     */
    private void assemblePosGoodsLabelInfo(StoreGoodsExtendVO storeGoodsExtendVO, Integer goodsLabelId) {
        if (goodsLabelId == null) {
            return;
        }
        storeGoodsExtendVO.setLabelInfo(goodsLabelConverter.toGoodsLabelVO(goodsLabelMapperService.getById(goodsLabelId)));
    }

    /**
     * 组装POS渠道的可售商品的售卖信息
     *
     * @param queryDTO           请求参数
     * @param storeGoodsExtendVO 商品详情信息
     */
    private void assemblePosGoodsSellingInfo(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO, StoreGoodsExtendVO storeGoodsExtendVO) {
        StoreSalesProgramPO defaultStoreSalesProgramPO = storeSalesProgramMapperService.lambdaQuery()
                .eq(StoreSalesProgramPO::getStoreId, queryDTO.getStoreId())
                .eq(StoreSalesProgramPO::getChannelId, queryDTO.getChannelId())
                .eq(StoreSalesProgramPO::getIsDefault, true)
                .one();
        if (defaultStoreSalesProgramPO == null) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "POS通道未设置默认销售方案");
        }
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeSalesProgramGoodsMapperService.lambdaQuery()
                .eq(StoreSalesProgramGoodsPO::getGoodsId, queryDTO.getStoreGoodsId())
                .eq(StoreSalesProgramGoodsPO::getStoreSalesProgramId, defaultStoreSalesProgramPO.getId())
                .list();
        if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, storeGoodsExtendVO.getGoodsName() + "商品已不存在");
        }
        // 获取默认的销售策略的价格表，因为默认的只会有一条记录，则直接拿第一条记录即可
        List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList =
                storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                        .in(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, storeSalesProgramGoodsPOList.stream().map(StoreSalesProgramGoodsPO::getId).toList())
                        .list();
        Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndPricePOListMap =
                storeSalesProgramGoodsPricePOList.stream().collect(Collectors.groupingBy(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId));
        Map<Integer, GoodsPackageSkuPO> goodsPackageSkuIdAndInfoMap = goodsPackageSkuMapperService.lambdaQuery()
                .eq(GoodsPackageSkuPO::getStoreGoodsId, storeGoodsExtendVO.getId())
                .list().stream().collect(toMap(GoodsPackageSkuPO::getId, Function.identity()));
        Map<Integer, String> unitIdAndNameMap = saleUnitMeasurementMapperService.list().stream()
                .collect(toMap(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName));

        storeGoodsExtendVO.setStoreSalesProgramName(defaultStoreSalesProgramPO.getStoreSalesProgramName());
        storeGoodsExtendVO.setStoreSalesProgramId(defaultStoreSalesProgramPO.getId());
        StoreSalesProgramGoodsPO storeSalesProgramGoodsPOItem = storeSalesProgramGoodsPOList.get(0);
        storeGoodsExtendVO.setGoodsName(storeSalesProgramGoodsPOItem.getGoodsSaleName());
        storeGoodsExtendVO.setCover(storeSalesProgramGoodsPOItem.getCoverPicture());
        storeGoodsExtendVO.setGoodsLabel(storeSalesProgramGoodsPOItem.getTag());
        storeGoodsExtendVO.setSuggestedSellingPrice(storeSalesProgramGoodsPOItem.getSellingPrice());

        if (storeGoodsExtendVO.getGoodsSpec() == 2) {
            // 多规格商品
            assembleMultipleSpecDetails(queryDTO, storeGoodsExtendVO, storeSalesProgramGoodsPOList,
                    goodsPackageSkuIdAndInfoMap, unitIdAndNameMap, storeSalesProgramGoodsIdAndPricePOListMap, defaultStoreSalesProgramPO);
        } else {
            // 单规格商品
            assembleSingleSpecDetails(storeGoodsExtendVO, storeSalesProgramGoodsPOList, goodsPackageSkuIdAndInfoMap, unitIdAndNameMap, storeSalesProgramGoodsIdAndPricePOListMap, defaultStoreSalesProgramPO);
        }
    }

    /**
     * 组装单规格商品信息
     *
     * @param storeGoodsExtendVO 商品详情信息
     * @param storeSalesProgramGoodsPOList 商品的销售方案商品信息
     * @param goodsPackageSkuIdAndInfoMap 商品的规格信息
     * @param unitIdAndNameMap 规格单位信息
     * @param storeSalesProgramGoodsIdAndPricePOListMap 销售策略价格信息
     * @param defaultStoreSalesProgramPO 销售策略信息
     */
    private static void assembleSingleSpecDetails(StoreGoodsExtendVO storeGoodsExtendVO,
                                                  List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList,
                                                  Map<Integer, GoodsPackageSkuPO> goodsPackageSkuIdAndInfoMap,
                                                  Map<Integer, String> unitIdAndNameMap,
                                                  Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndPricePOListMap,
                                                  StoreSalesProgramPO defaultStoreSalesProgramPO) {
        List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList = new ArrayList<>();
        for (StoreSalesProgramGoodsPO storeSalesProgramGoodsPO : storeSalesProgramGoodsPOList) {
            StoreGoodsSpecAndDetailsFullVO storeGoodsSpecAndDetailsFullVO = new StoreGoodsSpecAndDetailsFullVO();
            storeGoodsSpecAndDetailsFullVO.setStoreSalesProgramGoodsId(storeSalesProgramGoodsPO.getId());
            GoodsPackageSkuPO goodsPackageSkuPO = goodsPackageSkuIdAndInfoMap.get(storeSalesProgramGoodsPO.getGoodsPackageSkuId());
            storeGoodsSpecAndDetailsFullVO.setBarcode(goodsPackageSkuPO.getBarcode());
            storeGoodsSpecAndDetailsFullVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
            storeGoodsSpecAndDetailsFullVO.setSkuId(goodsPackageSkuPO.getId());
            storeGoodsSpecAndDetailsFullVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
            storeGoodsSpecAndDetailsFullVO.setGoodsUnitName(unitIdAndNameMap.get(goodsPackageSkuPO.getGoodsUnit()));
            storeGoodsSpecAndDetailsFullVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
            // 新加字段数据
            storeGoodsSpecAndDetailsFullVO.setWeight(goodsPackageSkuPO.getWeight());
            storeGoodsSpecAndDetailsFullVO.setIsLimitBuy(goodsPackageSkuPO.getIsLimitBuy());
            storeGoodsSpecAndDetailsFullVO.setLimitBuyNum(goodsPackageSkuPO.getLimitBuyNum());
            storeGoodsSpecAndDetailsFullVO.setOnlineInitSales(goodsPackageSkuPO.getOnlineInitSales());
            storeGoodsSpecAndDetailsFullVO.setLinePrice(goodsPackageSkuPO.getLinePrice());
            // 选择数据
            StoreGoodsSpecAndDetailsFullVO.SectionVO sectionVO = new StoreGoodsSpecAndDetailsFullVO.SectionVO();
            StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO = storeSalesProgramGoodsIdAndPricePOListMap.get(storeSalesProgramGoodsPO.getId()).get(0);
            sectionVO.setSerialNumber(storeSalesProgramGoodsPricePO.getSerialNumber());
            sectionVO.setSellingPrice(storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute());
            sectionVO.setStart(defaultStoreSalesProgramPO.getTimeSection().get(0).getStart());
            sectionVO.setEnd(defaultStoreSalesProgramPO.getTimeSection().get(0).getEnd());
            sectionVO.setStoreSalesProgramGoodsPriceId(storeSalesProgramGoodsPricePO.getId());
            storeGoodsSpecAndDetailsFullVO.setSectionList(Collections.singletonList(sectionVO));
            storeGoodsSpecDetailsList.add(storeGoodsSpecAndDetailsFullVO);
        }
        storeGoodsExtendVO.setStoreGoodsSpecDetailsList(storeGoodsSpecDetailsList);
    }

    /**
     * 组装多规格商品信息
     *
     * @param queryDTO 查询条件
     * @param storeGoodsExtendVO 商品详情信息
     * @param storeSalesProgramGoodsPOList 商品的销售方案商品信息
     * @param goodsPackageSkuIdAndInfoMap 商品的规格信息
     * @param unitIdAndNameMap 规格单位信息
     * @param storeSalesProgramGoodsIdAndPricePOListMap 销售策略价格信息
     * @param defaultStoreSalesProgramPO 销售策略信息
     */
    private void assembleMultipleSpecDetails(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO,
                                             StoreGoodsExtendVO storeGoodsExtendVO,
                                             List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList,
                                             Map<Integer, GoodsPackageSkuPO> goodsPackageSkuIdAndInfoMap,
                                             Map<Integer, String> unitIdAndNameMap,
                                             Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndPricePOListMap,
                                             StoreSalesProgramPO defaultStoreSalesProgramPO) {
        Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap = new HashMap<>();
        Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap = new HashMap<>();
        Map<Integer, SpecPO> specIdAndSpecInfoMap;
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap;
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = new HashMap<>();
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = new HashMap<>();
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = new HashMap<>();
        Map<Integer, GoodsPictureVO> imageIdAndInfoMap;
        specIdAndSpecInfoMap = specMapperService.lambdaQuery()
                .select(SpecPO::getId, SpecPO::getName, SpecPO::getSort)
                .eq(SpecPO::getStoreId, queryDTO.getStoreId())
                .list().stream().collect(toMap(SpecPO::getId, Function.identity()));
        specDetailIdAndInfoMap = specDetailMapperService.lambdaQuery()
                .select(SpecDetailPO::getId, SpecDetailPO::getName, SpecDetailPO::getSort, SpecDetailPO::getSpecId)
                .in(SpecDetailPO::getSpecId, specIdAndSpecInfoMap.keySet())
                .list().stream().collect(toMap(SpecDetailPO::getId, Function.identity()));
        List<SkuSpecDetailsPO> skuSpecDetailsPOList = skuSpecDetailsMapperService.lambdaQuery()
                .select(SkuSpecDetailsPO::getGoodsSpecDetailId, SkuSpecDetailsPO::getSkuId)
                .in(SkuSpecDetailsPO::getSkuId, storeSalesProgramGoodsPOList.stream().map(StoreSalesProgramGoodsPO::getGoodsPackageSkuId).toList()).list();

        List<Integer> goodsSpecDetailIdList = new ArrayList<>();
        skuSpecDetailsPOList.forEach(skuSpecDetailsPO -> {
            goodsSpecDetailIdList.add(skuSpecDetailsPO.getGoodsSpecDetailId());
            skuIdAndGoodsSpecDetailIdListMap.computeIfAbsent(skuSpecDetailsPO.getSkuId(), k -> new ArrayList<>()).add(skuSpecDetailsPO.getGoodsSpecDetailId());
        });
        List<Integer> goodsSpecIdList = new ArrayList<>();

        List<GoodsSpecDetailPO> goodsSpecDetailPOList = goodsSpecDetailMapperService.lambdaQuery().in(GoodsSpecDetailPO::getId, goodsSpecDetailIdList).list();
        Set<Integer> imageIdSet = new HashSet<>();
        goodsSpecDetailPOList.forEach(goodsSpecDetailPO -> {
            if (goodsSpecDetailPO.getImgId() != null) {
                imageIdSet.add(goodsSpecDetailPO.getImgId());
            }
            goodsSpecDetailIdAndInfoMap.put(goodsSpecDetailPO.getId(), goodsSpecDetailPO);
            goodsSpecIdList.add(goodsSpecDetailPO.getGoodsSpecId());
            goodsSpecIdAndGoodsSpecDetailInfoMap.computeIfAbsent(goodsSpecDetailPO.getGoodsSpecId(), k -> new ArrayList<>()).add(goodsSpecDetailPO);
        });
        imageIdAndInfoMap = getIntegerGoodsPictureVOMap(imageIdSet);
        List<GoodsSpecPO> goodsSpecPOList = goodsSpecMapperService.lambdaQuery()
                .in(GoodsSpecPO::getId, goodsSpecIdList)
                .list();

        goodsSpecPOList.forEach(goodsSpecPO -> {
            storeGoodsIdAndGoodsSpecPOListMap.computeIfAbsent(goodsSpecPO.getStoreGoodsId(), k -> new ArrayList<>()).add(goodsSpecPO);
            goodsSpecIdAndInfoMap.put(goodsSpecPO.getId(), goodsSpecPO);
        });
        List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList = new ArrayList<>();
        for (StoreSalesProgramGoodsPO storeSalesProgramGoodsPO : storeSalesProgramGoodsPOList) {
            StoreGoodsSpecAndDetailsFullVO storeGoodsSpecAndDetailsFullVO = new StoreGoodsSpecAndDetailsFullVO();
            storeGoodsSpecAndDetailsFullVO.setStoreSalesProgramGoodsId(storeSalesProgramGoodsPO.getId());
            GoodsPackageSkuPO goodsPackageSkuPO = goodsPackageSkuIdAndInfoMap.get(storeSalesProgramGoodsPO.getGoodsPackageSkuId());
            storeGoodsSpecAndDetailsFullVO.setBarcode(goodsPackageSkuPO.getBarcode());
            storeGoodsSpecAndDetailsFullVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
            storeGoodsSpecAndDetailsFullVO.setSkuId(goodsPackageSkuPO.getId());
            storeGoodsSpecAndDetailsFullVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
            storeGoodsSpecAndDetailsFullVO.setImage(imageIdAndInfoMap.get(goodsPackageSkuPO.getCover()));
            storeGoodsSpecAndDetailsFullVO.setGoodsUnitName(unitIdAndNameMap.get(goodsPackageSkuPO.getGoodsUnit()));
            storeGoodsSpecAndDetailsFullVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
            // 新加字段数据
            storeGoodsSpecAndDetailsFullVO.setWeight(goodsPackageSkuPO.getWeight());
            storeGoodsSpecAndDetailsFullVO.setIsLimitBuy(goodsPackageSkuPO.getIsLimitBuy());
            storeGoodsSpecAndDetailsFullVO.setLimitBuyNum(goodsPackageSkuPO.getLimitBuyNum());
            storeGoodsSpecAndDetailsFullVO.setOnlineInitSales(goodsPackageSkuPO.getOnlineInitSales());
            storeGoodsSpecAndDetailsFullVO.setLinePrice(goodsPackageSkuPO.getLinePrice());
            // 选择字段
            StoreGoodsSpecAndDetailsFullVO.SectionVO sectionVO = new StoreGoodsSpecAndDetailsFullVO.SectionVO();
            StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO = storeSalesProgramGoodsIdAndPricePOListMap.get(storeSalesProgramGoodsPO.getId()).get(0);

            sectionVO.setSerialNumber(storeSalesProgramGoodsPricePO.getSerialNumber());
            sectionVO.setSellingPrice(storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute());
            sectionVO.setStart(defaultStoreSalesProgramPO.getTimeSection().get(0).getStart());
            sectionVO.setEnd(defaultStoreSalesProgramPO.getTimeSection().get(0).getEnd());
            sectionVO.setStoreSalesProgramGoodsPriceId(storeSalesProgramGoodsPricePO.getId());
            storeGoodsSpecAndDetailsFullVO.setSectionList(Collections.singletonList(sectionVO));
            // 组装规格信息
            List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList = new ArrayList<>();
            skuIdAndGoodsSpecDetailIdListMap.get(storeSalesProgramGoodsPO.getGoodsPackageSkuId()).forEach(goodsSpecDetailId -> {
                StoreGoodsSpecRelationVO storeGoodsSpecRelationVO = new StoreGoodsSpecRelationVO();
                storeGoodsSpecRelationVO.setGoodsSpecDetailId(goodsSpecDetailId);
                GoodsSpecDetailPO goodsSpecDetailPO = goodsSpecDetailIdAndInfoMap.get(goodsSpecDetailId);
                storeGoodsSpecRelationVO.setGoodsSpecDetailSort(goodsSpecDetailPO.getSort());
                SpecDetailPO specDetailPO = specDetailIdAndInfoMap.get(goodsSpecDetailPO.getSpecDetailId());
                storeGoodsSpecRelationVO.setGoodsSpecDetailName(specDetailPO.getName());
                storeGoodsSpecRelationVO.setGoodsSpecId(goodsSpecDetailPO.getGoodsSpecId());
                GoodsSpecPO goodsSpecPO = goodsSpecIdAndInfoMap.get(goodsSpecDetailPO.getGoodsSpecId());
                storeGoodsSpecRelationVO.setGoodsSpecSort(goodsSpecPO.getSort());
                SpecPO specPO = specIdAndSpecInfoMap.get(goodsSpecPO.getSpecId());
                storeGoodsSpecRelationVO.setGoodsSpecName(specPO.getName());
                storeGoodsSpecRelationList.add(storeGoodsSpecRelationVO);
            });
            commonService.sortStoreGoodsSpecList(storeGoodsSpecRelationList);
            storeGoodsSpecAndDetailsFullVO.setStoreGoodsSpecRelationList(storeGoodsSpecRelationList);
            storeGoodsSpecDetailsList.add(storeGoodsSpecAndDetailsFullVO);
        }
        // 排序商品规格详情列表
        sortStoreGoodsSpecDetailsList(storeGoodsExtendVO.getGoodsSpec(), storeGoodsSpecDetailsList);
        storeGoodsExtendVO.setStoreGoodsSpecDetailsList(storeGoodsSpecDetailsList);
    }

    /**
     * 组装POS商品详情的商品分类信息
     *
     * @param storeGoodsExtendVO 商品详情信息
     */
    private void assemblePosGoodsStoreGoodsCategoryInfo(StoreGoodsExtendVO storeGoodsExtendVO) {
        if (storeGoodsExtendVO.getCategory() == null) {
            return;
        }
        List<StoreGoodsCategoryVO> storeGoodsCategoryVOList = storeGoodsCategoryConverter.toStoreGoodsCategoryVOList(
                storeGoodsCategoryMapperService.queryGenerationsVOListByIdList(List.of(storeGoodsExtendVO.getCategory())));
        storeGoodsExtendVO.setStoreGoodsCategoryList(assembleStoreGoodsCategoryVOList(storeGoodsCategoryVOList));
    }

    /**
     * 组装POS商品详情的销售分组信息
     *
     * @param queryDTO           请求参数
     * @param storeGoodsExtendVO 商品详情信息
     */
    private void assemblePosGoodsSaleCategoryInfoList(QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO,
                                                      StoreGoodsExtendVO storeGoodsExtendVO) {
        Map<Integer, StoreGoodsBaseExtendVO.SaleCategoryVO> saleCategoryIdAndVOInfoMap =
                saleCategoryConverter.toStoreGoodsExtendVOSaleCategoryVOList(saleCategoryMapperService.lambdaQuery()
                        .eq(SaleCategoryPO::getStoreId, queryDTO.getStoreId())
                        .eq(SaleCategoryPO::getChannelId, queryDTO.getChannelId())
                        .list()).stream().collect(toMap(StoreGoodsBaseExtendVO.SaleCategoryVO::getId, Function.identity()));
        List<Integer> saleCategoryIdList = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getSaleCategoryId)
                .in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, saleCategoryIdAndVOInfoMap.keySet())
                .eq(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, queryDTO.getStoreGoodsId())
                .list().stream().map(SaleCategoryAndStoreGoodsPO::getSaleCategoryId).distinct().toList();
        List<StoreGoodsBaseExtendVO.SaleCategoryVO> saleCategoryVOListResult = new ArrayList<>();
        saleCategoryIdList.forEach(saleCategoryId -> {
            saleCategoryVOListResult.add(getCategoryLevel(saleCategoryId, saleCategoryIdAndVOInfoMap));
        });
        saleCategoryVOListResult.sort(Comparator.comparing(StoreGoodsBaseExtendVO.SaleCategoryVO::getSort));
        storeGoodsExtendVO.setCategoryList(saleCategoryVOListResult);
    }

    /**
     * 递归获取销售分组数据
     * @param saleCategoryId 销售分组id
     * @param saleCategoryIdAndVOInfoMap 销售分组数据
     * @return 查询结果
     */
    private StoreGoodsBaseExtendVO.SaleCategoryVO getCategoryLevel(Integer saleCategoryId,
                                                                   Map<Integer, StoreGoodsBaseExtendVO.SaleCategoryVO> saleCategoryIdAndVOInfoMap){
        StoreGoodsBaseExtendVO.SaleCategoryVO saleCategoryVO = saleCategoryIdAndVOInfoMap.get(saleCategoryId);
        // 查询父级
        if(!(saleCategoryVO.getParentId() == null || saleCategoryVO.getParentId() == 0)){
            StoreGoodsBaseExtendVO.SaleCategoryVO parentVO = saleCategoryIdAndVOInfoMap.get(saleCategoryVO.getParentId());
            parentVO.setChildList(Collections.singletonList(saleCategoryVO));
            // 查询爷级
            if(!(parentVO.getParentId() == null || parentVO.getParentId() == 0)){
                StoreGoodsBaseExtendVO.SaleCategoryVO grandParentVO = saleCategoryIdAndVOInfoMap.get(parentVO.getParentId());
                grandParentVO.setChildList(Collections.singletonList(parentVO));
                return grandParentVO;
            }
            return parentVO;
        }
        return saleCategoryVO;
    }

    /**
     * 组装商品分类列表
     *
     * @param storeGoodsCategoryVOList 商品分类列表
     * @return 顶级分类列表
     */
    private List<StoreGoodsCategoryVO> assembleStoreGoodsCategoryVOList(List<StoreGoodsCategoryVO> storeGoodsCategoryVOList) {
        List<StoreGoodsCategoryVO> parentList = storeGoodsCategoryVOList.stream()
                .filter(storeGoodsCategoryVO -> storeGoodsCategoryVO.getParentId() == null).toList();
        parentList.forEach(parent -> parent.setChildren(recursionAssembleStoreGoodsCategoryVOList(parent.getId(), storeGoodsCategoryVOList)));
        return parentList;
    }

    /**
     * 递归组装商品分类列表
     *
     * @param parentId                 父级ID
     * @param storeGoodsCategoryVOList 商品分类列表
     * @return 子集列表
     */
    private List<StoreGoodsCategoryVO> recursionAssembleStoreGoodsCategoryVOList(Integer parentId,
                                                                                 List<StoreGoodsCategoryVO> storeGoodsCategoryVOList) {
        List<StoreGoodsCategoryVO> childList = storeGoodsCategoryVOList.stream()
                .filter(storeGoodsCategoryVO -> parentId.equals(storeGoodsCategoryVO.getParentId())).toList();
        if (CollectionUtils.isNotEmpty(childList)) {
            childList.forEach(child -> child.setChildren(recursionAssembleStoreGoodsCategoryVOList(child.getId(), storeGoodsCategoryVOList)));
            return childList;
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 给POS渠道的可售商品列表添加销售分组、单位、销售价格、封面图片信息
     *
     * @param storeGoodsExtendVOPageRespVOList 商品列表
     * @param defaultStoreSalesProgramPO       默认销售方案
     * @param queryDTO 请求参数
     * @param defaultStoreSalesProgramPO 默认销售方案
     * @param storeGoodsIdAndSalesProgramGoodsListMap 商品ID和信息的映射
     */
    private void assemblePosGoodsExtendInfo(List<StoreGoodsExtendVO> storeGoodsExtendVOPageRespVOList,
                                            QueryStorePosGoodsAvailableForSaleListDTO queryDTO,
                                            StoreSalesProgramPO defaultStoreSalesProgramPO,
                                            Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsListMap) {

        List<Integer> storeGoodsIdResultList = new ArrayList<>();
        List<Integer> storeSalesProgramGoodsIdList = new ArrayList<>();
        Set<Integer> coverIdSet = new HashSet<>();
        Set<Integer> labelIdSet = new HashSet<>();
        assembleCoverAndLabelAndStoreGoodsId(storeGoodsExtendVOPageRespVOList, storeGoodsIdAndSalesProgramGoodsListMap,
                storeGoodsIdResultList, storeSalesProgramGoodsIdList, coverIdSet, labelIdSet);
        Map<Integer, GoodsLabelVO> labelIdAndInfoMap = getLabelIdAndInfoMap(labelIdSet);
        List<SaleCategoryAndStoreGoodsPO> saleCategoryAndStoreGoodsPOList =
                saleCategoryAndStoreGoodsMapperService.querySaleCategoryByStoreGoodsIdListAndChannelId(storeGoodsIdResultList, queryDTO.getChannelId());
        Map<Integer, List<Integer>> storeGoodsIdAndSaleCategoryIdListMap =
                saleCategoryAndStoreGoodsPOList.stream().collect(groupingBy(SaleCategoryAndStoreGoodsPO::getStoreGoodsId,
                        SaleCategoryAndStoreGoodsPO::getSaleCategoryId));
        Map<Integer, SaleCategoryPO> saleCategoryIdAndInfoMap =
                saleCategoryMapperService.queryGenerationsVOListByIdListAndChannelId(
                        storeGoodsIdAndSaleCategoryIdListMap.values().stream().flatMap(List::stream).distinct().toList(), queryDTO.getChannelId())
                        .stream()
                        .collect(toMap(SaleCategoryPO::getId, Function.identity()));
        Map<Integer, String> unitIdAndNameMap = saleUnitMeasurementMapperService.list().stream()
                .collect(toMap(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName));
        List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList =
                storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                        .in(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, storeSalesProgramGoodsIdList)
                        .list();
        Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndPricePOListMap =
                storeSalesProgramGoodsPricePOList.stream().collect(Collectors.groupingBy(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId));

        Set<Integer> skuIdSet = storeGoodsIdAndSalesProgramGoodsListMap.values().stream().flatMap(List::stream).map(StoreSalesProgramGoodsPO::getGoodsPackageSkuId).collect(Collectors.toSet());
        Map<Integer, GoodsPackageSkuPO> goodsPackageSkuIdAndInfoMap = goodsPackageSkuMapperService.lambdaQuery()
                .select(GoodsPackageSkuPO::getId, GoodsPackageSkuPO::getStoreGoodsId, GoodsPackageSkuPO::getSkuCode,
                        GoodsPackageSkuPO::getCosts, GoodsPackageSkuPO::getGoodsUnit, GoodsPackageSkuPO::getBarcode)
                .in(GoodsPackageSkuPO::getStoreGoodsId, storeGoodsIdResultList)
                .list().stream().collect(toMap(GoodsPackageSkuPO::getId, Function.identity()));
        boolean hasMultipleSpec = storeGoodsExtendVOPageRespVOList.stream().anyMatch(storeGoodsOnSaleExtendVO -> storeGoodsOnSaleExtendVO.getGoodsSpec() == 2);
        Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap = new HashMap<>();
        Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap = new HashMap<>();
        Map<Integer, SpecPO> specIdAndSpecInfoMap;
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap;
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = new HashMap<>();
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = new HashMap<>();
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = new HashMap<>();
        if (hasMultipleSpec) {
            specIdAndSpecInfoMap = specMapperService.lambdaQuery()
                    .select(SpecPO::getId, SpecPO::getName, SpecPO::getSort)
                    .eq(SpecPO::getStoreId, queryDTO.getStoreId())
                    .list().stream().collect(toMap(SpecPO::getId, Function.identity()));
            specDetailIdAndInfoMap = specDetailMapperService.lambdaQuery()
                    .select(SpecDetailPO::getId, SpecDetailPO::getName, SpecDetailPO::getSort, SpecDetailPO::getSpecId)
                    .in(SpecDetailPO::getSpecId, specIdAndSpecInfoMap.keySet())
                    .list().stream().collect(toMap(SpecDetailPO::getId, Function.identity()));
            List<SkuSpecDetailsPO> skuSpecDetailsPOList = skuSpecDetailsMapperService.lambdaQuery()
                    .select(SkuSpecDetailsPO::getGoodsSpecDetailId, SkuSpecDetailsPO::getSkuId)
                    .in(SkuSpecDetailsPO::getSkuId, skuIdSet).list();

            List<Integer> goodsSpecDetailIdList = new ArrayList<>();
            skuSpecDetailsPOList.forEach(skuSpecDetailsPO -> {
                goodsSpecDetailIdList.add(skuSpecDetailsPO.getGoodsSpecDetailId());
                skuIdAndGoodsSpecDetailIdListMap.computeIfAbsent(skuSpecDetailsPO.getSkuId(), k -> new ArrayList<>()).add(skuSpecDetailsPO.getGoodsSpecDetailId());
            });
            List<Integer> goodsSpecIdList = new ArrayList<>();

            List<GoodsSpecDetailPO> goodsSpecDetailPOList = goodsSpecDetailMapperService.lambdaQuery().in(GoodsSpecDetailPO::getId, goodsSpecDetailIdList).list();
            goodsSpecDetailPOList.forEach(goodsSpecDetailPO -> {
                if (goodsSpecDetailPO.getImgId() != null) {
                    coverIdSet.add(goodsSpecDetailPO.getImgId());
                }
                goodsSpecDetailIdAndInfoMap.put(goodsSpecDetailPO.getId(), goodsSpecDetailPO);
                goodsSpecIdList.add(goodsSpecDetailPO.getGoodsSpecId());
                goodsSpecIdAndGoodsSpecDetailInfoMap.computeIfAbsent(goodsSpecDetailPO.getGoodsSpecId(), k -> new ArrayList<>()).add(goodsSpecDetailPO);
            });
            List<GoodsSpecPO> goodsSpecPOList = goodsSpecMapperService.lambdaQuery()
                    .in(GoodsSpecPO::getId, goodsSpecIdList)
                    .list();

            goodsSpecPOList.forEach(goodsSpecPO -> {
                storeGoodsIdAndGoodsSpecPOListMap.computeIfAbsent(goodsSpecPO.getStoreGoodsId(), k -> new ArrayList<>()).add(goodsSpecPO);
                goodsSpecIdAndInfoMap.put(goodsSpecPO.getId(), goodsSpecPO);
            });
        } else {
            specIdAndSpecInfoMap = new HashMap<>();
            specDetailIdAndInfoMap = new HashMap<>();
        }
        Map<Integer, GoodsPictureVO> coverIdAndInfoMap = getIntegerGoodsPictureVOMap(coverIdSet);

        storeGoodsExtendVOPageRespVOList.forEach(storeGoodsExtendVO -> {
            // 组装销售分组
            List<Integer> categoryIdList = storeGoodsIdAndSaleCategoryIdListMap.get(storeGoodsExtendVO.getId());
            List<StoreGoodsBaseExtendVO.SaleCategoryVO> saleCategoryVOList = new ArrayList<>();
            assembleSaleCategoryVOList(categoryIdList, saleCategoryIdAndInfoMap, saleCategoryVOList);
            saleCategoryVOList.sort(Comparator.comparing(StoreGoodsBaseExtendVO.SaleCategoryVO::getSort));
            storeGoodsExtendVO.setCategoryList(saleCategoryVOList);
            // 组装单位
            storeGoodsExtendVO.setGoodsUnitName(unitIdAndNameMap.get(storeGoodsExtendVO.getGoodsUnit()));
            // 组装销售价格
            storeGoodsExtendVO.setStoreSalesProgramName(defaultStoreSalesProgramPO.getStoreSalesProgramName());
            storeGoodsExtendVO.setStoreSalesProgramId(defaultStoreSalesProgramPO.getId());
            storeGoodsExtendVO.setLabelInfo(labelIdAndInfoMap.get(storeGoodsExtendVO.getGoodsLabel()));
            List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList = new ArrayList<>();
            boolean isGoodsSpec = storeGoodsExtendVO.getGoodsSpec() == 2;
            List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeGoodsIdAndSalesProgramGoodsListMap.get(storeGoodsExtendVO.getId());
            assembleStoreGoodsCoverPicture(storeGoodsExtendVO, storeSalesProgramGoodsPOList, coverIdAndInfoMap);
            for (StoreSalesProgramGoodsPO storeSalesProgramGoodsPO : storeSalesProgramGoodsPOList) {
                StoreGoodsSpecAndDetailsFullVO storeGoodsSpecAndDetailsFullVO = new StoreGoodsSpecAndDetailsFullVO();
                storeGoodsSpecAndDetailsFullVO.setStoreSalesProgramGoodsId(storeSalesProgramGoodsPO.getId());
                GoodsPackageSkuPO goodsPackageSkuPO = goodsPackageSkuIdAndInfoMap.get(storeSalesProgramGoodsPO.getGoodsPackageSkuId());
                storeGoodsSpecAndDetailsFullVO.setBarcode(goodsPackageSkuPO.getBarcode());
                storeGoodsSpecAndDetailsFullVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
                storeGoodsSpecAndDetailsFullVO.setSkuId(goodsPackageSkuPO.getId());
                storeGoodsSpecAndDetailsFullVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
                storeGoodsSpecAndDetailsFullVO.setImage(coverIdAndInfoMap.get(goodsPackageSkuPO.getCover()));
                storeGoodsSpecAndDetailsFullVO.setGoodsUnitName(unitIdAndNameMap.get(goodsPackageSkuPO.getGoodsUnit()));
                storeGoodsSpecAndDetailsFullVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
                // 新加字段数据
                storeGoodsSpecAndDetailsFullVO.setWeight(goodsPackageSkuPO.getWeight());
                storeGoodsSpecAndDetailsFullVO.setIsLimitBuy(goodsPackageSkuPO.getIsLimitBuy());
                storeGoodsSpecAndDetailsFullVO.setLimitBuyNum(goodsPackageSkuPO.getLimitBuyNum());
                storeGoodsSpecAndDetailsFullVO.setOnlineInitSales(goodsPackageSkuPO.getOnlineInitSales());
                storeGoodsSpecAndDetailsFullVO.setLinePrice(goodsPackageSkuPO.getLinePrice());
                // 选择数据
                StoreGoodsSpecAndDetailsFullVO.SectionVO sectionVO = new StoreGoodsSpecAndDetailsFullVO.SectionVO();
                StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO = storeSalesProgramGoodsIdAndPricePOListMap.get(storeSalesProgramGoodsPO.getId()).get(0);

                sectionVO.setSerialNumber(storeSalesProgramGoodsPricePO.getSerialNumber());
                sectionVO.setSellingPrice(storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute());
                sectionVO.setStart(defaultStoreSalesProgramPO.getTimeSection().get(0).getStart());
                sectionVO.setEnd(defaultStoreSalesProgramPO.getTimeSection().get(0).getEnd());
                sectionVO.setStoreSalesProgramGoodsPriceId(storeSalesProgramGoodsPricePO.getId());
                storeGoodsSpecAndDetailsFullVO.setSectionList(Collections.singletonList(sectionVO));
                // 组装规格信息

                if (isGoodsSpec) {
                    List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList = new ArrayList<>();
                    skuIdAndGoodsSpecDetailIdListMap.get(storeSalesProgramGoodsPO.getGoodsPackageSkuId()).forEach(goodsSpecDetailId -> {
                        StoreGoodsSpecRelationVO storeGoodsSpecRelationVO = new StoreGoodsSpecRelationVO();
                        storeGoodsSpecRelationVO.setGoodsSpecDetailId(goodsSpecDetailId);
                        GoodsSpecDetailPO goodsSpecDetailPO = goodsSpecDetailIdAndInfoMap.get(goodsSpecDetailId);
                        storeGoodsSpecRelationVO.setGoodsSpecDetailSort(goodsSpecDetailPO.getSort());
                        SpecDetailPO specDetailPO = specDetailIdAndInfoMap.get(goodsSpecDetailPO.getSpecDetailId());
                        storeGoodsSpecRelationVO.setGoodsSpecDetailName(specDetailPO.getName());
                        storeGoodsSpecRelationVO.setGoodsSpecId(goodsSpecDetailPO.getGoodsSpecId());
                        GoodsSpecPO goodsSpecPO = goodsSpecIdAndInfoMap.get(goodsSpecDetailPO.getGoodsSpecId());
                        storeGoodsSpecRelationVO.setGoodsSpecSort(goodsSpecPO.getSort());
                        SpecPO specPO = specIdAndSpecInfoMap.get(goodsSpecPO.getSpecId());
                        storeGoodsSpecRelationVO.setGoodsSpecName(specPO.getName());
                        storeGoodsSpecRelationList.add(storeGoodsSpecRelationVO);
                    });
                    commonService.sortStoreGoodsSpecList(storeGoodsSpecRelationList);
                    storeGoodsSpecAndDetailsFullVO.setStoreGoodsSpecRelationList(storeGoodsSpecRelationList);
                }
                storeGoodsSpecDetailsList.add(storeGoodsSpecAndDetailsFullVO);
            }
            // 排序商品规格详情列表
            sortStoreGoodsSpecDetailsList(storeGoodsExtendVO.getGoodsSpec(), storeGoodsSpecDetailsList);
            storeGoodsExtendVO.setStoreGoodsSpecDetailsList(storeGoodsSpecDetailsList);
        });
    }

    /**
     * 组装商品封面图片
     *
     * @param storeGoodsExtendVO 商品扩展信息
     * @param storeSalesProgramGoodsPOList 销售方案商品列表
     * @param coverIdAndInfoMap 封面ID和封面信息的映射
     */
    private static void assembleStoreGoodsCoverPicture(StoreGoodsExtendVO storeGoodsExtendVO,
                                                       List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList,
                                                       Map<Integer, GoodsPictureVO> coverIdAndInfoMap) {
        if (Objects.nonNull(storeSalesProgramGoodsPOList.get(0).getCoverPicture()) && coverIdAndInfoMap.containsKey(storeSalesProgramGoodsPOList.get(0).getCoverPicture())) {
            storeGoodsExtendVO.setPicture(List.of(coverIdAndInfoMap.get(storeSalesProgramGoodsPOList.get(0).getCoverPicture())));
        }
    }

    /**
     * 组装标签id和标签VO的映射信息
     *
     * @param labelIdSet 标签ID集合
     * @return 标签ID和标签VO的映射信息
     */
    private Map<Integer, GoodsLabelVO> getLabelIdAndInfoMap(Set<Integer> labelIdSet) {
        if (CollectionUtils.isEmpty(labelIdSet)) {
            return Collections.emptyMap();
        }
        return goodsLabelConverter.toGoodsLabelVOList(
                goodsLabelMapperService.listByIds(labelIdSet)).stream().collect(toMap(GoodsLabelVO::getId, Function.identity()));
    }

    /**
     * 组装封面ID集合、标签ID集合、商品ID集合、销售方案商品ID集合
     *
     * @param storeGoodsExtendVOPageRespVOList 商品列表
     * @param storeGoodsIdAndSalesProgramGoodsListMap 商品ID和信息的映射
     * @param storeGoodsIdResultList 商品ID集合
     * @param storeSalesProgramGoodsIdList 销售方案商品ID集合
     * @param coverIdSet 封面ID集合
     * @param labelIdSet 标签ID集合
     */
    private static void assembleCoverAndLabelAndStoreGoodsId(List<StoreGoodsExtendVO> storeGoodsExtendVOPageRespVOList,
                                                             Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsListMap,
                                                             List<Integer> storeGoodsIdResultList,
                                                             List<Integer> storeSalesProgramGoodsIdList,
                                                             Set<Integer> coverIdSet,
                                                             Set<Integer> labelIdSet) {
        storeGoodsExtendVOPageRespVOList.forEach(storeGoodsExtendVO -> {
            storeGoodsIdResultList.add(storeGoodsExtendVO.getId());
            List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeGoodsIdAndSalesProgramGoodsListMap.get(storeGoodsExtendVO.getId());
            storeSalesProgramGoodsIdList.addAll(storeSalesProgramGoodsPOList.stream().map(StoreSalesProgramGoodsPO::getId).toList());
            if (Objects.nonNull(storeSalesProgramGoodsPOList.get(0).getCoverPicture())) {
                coverIdSet.add(storeSalesProgramGoodsPOList.get(0).getCoverPicture());
            }
            if (Objects.nonNull(storeGoodsExtendVO.getGoodsLabel())) {
                labelIdSet.add(storeGoodsExtendVO.getGoodsLabel());
            }
        });
    }

    /**
     * 组装商品销售分组详情
     *
     * @param categoryIdList 销售分组ID集合
     * @param saleCategoryIdAndInfoMap 销售分组ID和销售分组信息的映射
     * @param saleCategoryVOList 销售分组详情集合
     */
    private void assembleSaleCategoryVOList(List<Integer> categoryIdList,
                                            Map<Integer, SaleCategoryPO> saleCategoryIdAndInfoMap,
                                            List<StoreGoodsBaseExtendVO.SaleCategoryVO> saleCategoryVOList) {
        categoryIdList.forEach(categoryId -> {
            SaleCategoryPO saleCategoryPO = saleCategoryIdAndInfoMap.get(categoryId);
            if (saleCategoryPO.getParentId() == null || saleCategoryPO.getParentId() == 0) {
                // 如果没有父级，则它就是顶级
                saleCategoryVOList.add(saleCategoryConverter.toStoreGoodsExtendVOSaleCategoryVO(saleCategoryPO));
            } else {
                // 如果有父级，则需要找到它的父级并添加到它的子集中
                StoreGoodsBaseExtendVO.SaleCategoryVO saleCategoryParentVO =
                        saleCategoryConverter.toStoreGoodsExtendVOSaleCategoryVO(saleCategoryIdAndInfoMap.get(saleCategoryPO.getParentId()));
                saleCategoryParentVO.setChildList(Collections.singletonList(saleCategoryConverter.toStoreGoodsExtendVOSaleCategoryVO(saleCategoryPO)));
                saleCategoryVOList.add(saleCategoryParentVO);
            }
        });
    }

    @Override
    public List<StoreGoodsOnSaleExtendVO> queryStoreGoodsOnSaleFilterList(QueryStoreGoodsOnSaleFilterListDTO queryStoreGoodsOnSaleFilterListDTO) {
        List<StoreGoodsPO> storeGoodsPOList = storeGoodsMapperService.queryStoreGoodsByCode(
                CommonUtils.assembleSqlLikeString(queryStoreGoodsOnSaleFilterListDTO.getCode()), queryStoreGoodsOnSaleFilterListDTO.getStoreId(),
                queryStoreGoodsOnSaleFilterListDTO.getScaleType());
        if (CollectionUtils.isEmpty(storeGoodsPOList)) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, GOODS_NOT_EXIST);
        }
        List<Integer> storeGoodsIdList = new ArrayList<>();
        storeGoodsPOList.forEach(storeGoodsPO -> storeGoodsIdList.add(storeGoodsPO.getId()));
        // 根据店铺商品ID查询所有销售方案商品列表，如果不存在则代表该商品未上架，返回null
        List<StoreSalesProgramGoodsPO> allStoreSalesProgramGoodsPOList =
                storeSalesProgramGoodsMapperService.lambdaQuery()
                        .in(StoreSalesProgramGoodsPO::getGoodsId, storeGoodsIdList)
                        .list();
        if (CollectionUtils.isEmpty(allStoreSalesProgramGoodsPOList)) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, UNAVAILABLE_FOR_SALE);
        }
        // 查询所有销售方案 用于和此时此刻生效的方案去匹配
        Map<Integer, List<StoreSalesProgramGoodsPO>> storeSalesProgramIdAndGoodsInfoListMap =
                allStoreSalesProgramGoodsPOList.stream().collect(Collectors.groupingBy(StoreSalesProgramGoodsPO::getStoreSalesProgramId));
        // 查询默认销售方案 以及 自定义销售方案和当前时间段对应的销售方案的时间段序号的映射
        Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair =
                commonService.queryDefaultSalesProgramAndCustomSalesProgramAndTimeSerialNumberPair(
                        queryStoreGoodsOnSaleFilterListDTO.getStoreId(), queryStoreGoodsOnSaleFilterListDTO.getChannelId());
        // 可售商品的销售方案对应的商品售卖信息，用于后续的数据组装
        Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsPOMap;
        // 可售商品的销售价格信息，用于后续的数据组装
        Map<Integer, StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap;
        // 判断当前是自定义销售方案生效，还是默认销售方案生效
        if (resultPair.getValue() != null) {
            storeGoodsIdAndSalesProgramGoodsPOMap = new HashMap<>();
            storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap = new HashMap<>();
            // 先看自定义销售方案是否有对应商品，再看默认方案有没有对应商品；
            // 规则：自定义销售方案优先级高于默认销售方案
            assembleSalesProgramGoodsAndPriceMapForHasCustomProgram(resultPair, storeSalesProgramIdAndGoodsInfoListMap,
                    storeGoodsIdAndSalesProgramGoodsPOMap, storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap);
        } else {
            // 只有默认销售方案生效
            Integer defaultSalesProgramId = resultPair.getKey().getId();
            if (!storeSalesProgramIdAndGoodsInfoListMap.containsKey(defaultSalesProgramId)) {
                // 商品未上架，返回 null
                throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, UNAVAILABLE_FOR_SALE);
            }
            storeGoodsIdAndSalesProgramGoodsPOMap = storeSalesProgramIdAndGoodsInfoListMap.get(defaultSalesProgramId)
                    .stream().collect(groupingBy(StoreSalesProgramGoodsPO::getGoodsId));
            // 查询销售价格信息
            storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap = storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                    .in(StoreSalesProgramGoodsPricePO::getGoodsId, storeGoodsIdAndSalesProgramGoodsPOMap.keySet())
                    .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, defaultSalesProgramId)
                    .eq(StoreSalesProgramGoodsPricePO::getSerialNumber, resultPair.getKey().getTimeSection().get(0).getSerialNumber())
                    .list().stream()
                    .collect(toMap(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, Function.identity()));
        }
        if (storeGoodsIdAndSalesProgramGoodsPOMap.isEmpty()) {
            // 商品未上架，返回 null
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, UNAVAILABLE_FOR_SALE);
        }
        storeGoodsPOList.removeIf(storeGoodsPO -> !storeGoodsIdAndSalesProgramGoodsPOMap.containsKey(storeGoodsPO.getId()));
        return batchAssembleStoreGoodsOnSaleExtendVO(queryStoreGoodsOnSaleFilterListDTO, storeGoodsPOList,
                storeGoodsIdAndSalesProgramGoodsPOMap, storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap);
    }

    /**
     * 组装可售商品的销售方案对应的商品售卖信息，用于后续的数据组装
     *
     * @param resultPair                                             默认销售方案 以及 自定义销售方案和当前时间段对应的销售方案的
     * @param storeSalesProgramIdAndGoodsInfoListMap                 所有销售方案商品列表
     * @param storeGoodsIdAndSalesProgramGoodsPOListMap              可售商品的销售方案对应的商品售卖信息
     * @param storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap 可售商品的销售价格信息
     */
    private void assembleSalesProgramGoodsAndPriceMapForHasCustomProgram(Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair,
                                                                         Map<Integer, List<StoreSalesProgramGoodsPO>> storeSalesProgramIdAndGoodsInfoListMap,
                                                                         Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsPOListMap,
                                                                         Map<Integer, StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap) {
        Integer customSalesProgramId = resultPair.getValue().getKey().getId();
        if (storeSalesProgramIdAndGoodsInfoListMap.containsKey(customSalesProgramId)) {
            storeGoodsIdAndSalesProgramGoodsPOListMap.putAll(storeSalesProgramIdAndGoodsInfoListMap.get(customSalesProgramId)
                    .stream().collect(groupingBy(StoreSalesProgramGoodsPO::getGoodsId)));
            if (!storeGoodsIdAndSalesProgramGoodsPOListMap.isEmpty()) {
                storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap.putAll(storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                        .in(StoreSalesProgramGoodsPricePO::getGoodsId, storeGoodsIdAndSalesProgramGoodsPOListMap.keySet())
                        .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, customSalesProgramId)
                        .eq(StoreSalesProgramGoodsPricePO::getSerialNumber, resultPair.getValue().getValue())
                        .list()
                        .stream().collect(toMap(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, Function.identity())));
            }

        }
        Integer defaultSalesProgramId = resultPair.getKey().getId();
        if (storeSalesProgramIdAndGoodsInfoListMap.containsKey(defaultSalesProgramId)) {
            List<Integer> defaultSalesStoreGoodsIdList = new ArrayList<>();
            storeSalesProgramIdAndGoodsInfoListMap.get(defaultSalesProgramId).forEach(po -> {
                if (!storeGoodsIdAndSalesProgramGoodsPOListMap.containsKey(po.getGoodsId())) {
                    storeGoodsIdAndSalesProgramGoodsPOListMap.computeIfAbsent(po.getGoodsId(), k -> new ArrayList<>()).add(po);
                    defaultSalesStoreGoodsIdList.add(po.getGoodsId());
                }
            });
            if (!defaultSalesStoreGoodsIdList.isEmpty()) {
                storeSalesProgramGoodsIdAndSalesProgramGoodsPricePOMap.putAll(storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                        .in(StoreSalesProgramGoodsPricePO::getGoodsId, defaultSalesStoreGoodsIdList)
                        .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, defaultSalesProgramId)
                        .eq(StoreSalesProgramGoodsPricePO::getSerialNumber, resultPair.getKey().getTimeSection().get(0).getSerialNumber())
                        .list()
                        .stream().collect(toMap(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, Function.identity())));
            }
        }
    }

    /**
     * 组装封面图片和标签信息的映射集合
     *
     * @param storeGoodsIdAndSalesProgramGoodsPOListMap 销售方案商品信息
     * @param coverIdAndInfoMap                         封面图片ID集合
     * @param labelIdAndInfoMap                         标签ID集合
     * @param skuIdList                                 skuId集合
     */
    private void assembleCoverAndLabelInfoMap(Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsPOListMap,
                                              Map<Integer, GoodsPictureVO> coverIdAndInfoMap,
                                              Map<Integer, GoodsLabelVO> labelIdAndInfoMap,
                                              List<Integer> skuIdList) {
        List<Integer> coverIdList = new ArrayList<>();
        List<Integer> labelIdList = new ArrayList<>();
        storeGoodsIdAndSalesProgramGoodsPOListMap.values().forEach(storeSalesProgramGoodsPOList -> {
            storeSalesProgramGoodsPOList.forEach(storeSalesProgramGoodsPO -> skuIdList.add(storeSalesProgramGoodsPO.getGoodsPackageSkuId()));
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramGoodsPOList.get(0);
            // 组装封面图片
            if (!Objects.isNull(storeSalesProgramGoodsPO.getCoverPicture())) {
                coverIdList.add(storeSalesProgramGoodsPO.getCoverPicture());
            }
            // 组装标签信息
            if (!Objects.isNull(storeSalesProgramGoodsPO.getTag())) {
                labelIdList.add(storeSalesProgramGoodsPO.getTag());
            }
        });

        if (CollectionUtils.isNotEmpty(coverIdList)) {
            coverIdAndInfoMap.putAll(goodsPictureConverter.toGoodsPictureVOList(
                    uploadFilesMapperService.listByIds(coverIdList)).stream().collect(toMap(GoodsPictureVO::getId, Function.identity())));
        }
        if (CollectionUtils.isNotEmpty(labelIdList)) {
            labelIdAndInfoMap.putAll(goodsLabelConverter.toGoodsLabelVOList(
                    goodsLabelMapperService.listByIds(labelIdList)).stream().collect(toMap(GoodsLabelVO::getId, Function.identity())));
        }
    }

    /**
     * 组装在售商品详情VO
     *
     * @param queryStoreGoodsOnSaleFilterListDTO         查询条件
     * @param storeGoodsPOList                           店铺商品信息集合
     * @param storeGoodsIdAndSalesProgramGoodsPOListMap  销售方案商品信息
     * @param storeGoodsIdAndSalesProgramGoodsPricePOMap 销售方案商品价格信息
     * @return 在售商品详情VO
     */
    private List<StoreGoodsOnSaleExtendVO> batchAssembleStoreGoodsOnSaleExtendVO(QueryStoreGoodsOnSaleFilterListDTO queryStoreGoodsOnSaleFilterListDTO,
                                                                                 List<StoreGoodsPO> storeGoodsPOList,
                                                                                 Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsPOListMap,
                                                                                 Map<Integer, StoreSalesProgramGoodsPricePO> storeGoodsIdAndSalesProgramGoodsPricePOMap) {
        Map<Integer, GoodsPictureVO> coverIdAndInfoMap = new HashMap<>();
        Map<Integer, GoodsLabelVO> labelIdAndInfoMap = new HashMap<>();
        List<Integer> skuIdList = new ArrayList<>();
        // 组装封面图片和标签信息的映射集合
        assembleCoverAndLabelInfoMap(storeGoodsIdAndSalesProgramGoodsPOListMap, coverIdAndInfoMap, labelIdAndInfoMap, skuIdList);
        List<Integer> brandIdList = new ArrayList<>();
        Set<Integer> storeGoodsIdSet = new HashSet<>();
        AtomicBoolean hasMultipleSpec = new AtomicBoolean(false);
        List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList = new ArrayList<>();
        assembleBrandIdAndStoreGoodsIdAndStoreGoodsOnSaleExtendVO(storeGoodsPOList, storeGoodsIdSet,
                hasMultipleSpec, brandIdList, storeGoodsOnSaleExtendVOList);
        List<GoodsPackageSkuPO> goodsPackageSkuPOList = goodsPackageSkuMapperService.lambdaQuery()
                .select(GoodsPackageSkuPO::getId, GoodsPackageSkuPO::getStoreGoodsId, GoodsPackageSkuPO::getSkuCode,
                        GoodsPackageSkuPO::getCosts, GoodsPackageSkuPO::getGoodsUnit, GoodsPackageSkuPO::getBarcode)
                .in(GoodsPackageSkuPO::getStoreGoodsId, storeGoodsIdSet)
                .list();
        Map<Integer, List<GoodsPackageSkuPO>> storeGoodsIdAndGoodsPackageSkuPOListMap = new HashMap<>();
        List<Integer> goodsUnitIdList = new ArrayList<>();
        goodsPackageSkuPOList.forEach(goodsPackageSkuPO -> {
            storeGoodsIdAndGoodsPackageSkuPOListMap.computeIfAbsent(goodsPackageSkuPO.getStoreGoodsId(), k -> new ArrayList<>())
                    .add(goodsPackageSkuPO);
            goodsUnitIdList.add(goodsPackageSkuPO.getGoodsUnit());
        });
        // 这儿组装一些映射关系的集合，用于后续对商品规格信息的组装
        Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap = new HashMap<>();
        Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap = new HashMap<>();
        Map<Integer, SpecPO> specIdAndSpecInfoMap = null;
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap = null;
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = new HashMap<>();
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = new HashMap<>();
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = new HashMap<>();
        if (hasMultipleSpec.get()) {
            specIdAndSpecInfoMap = specMapperService.lambdaQuery()
                    .select(SpecPO::getId, SpecPO::getName, SpecPO::getSort)
                    .eq(SpecPO::getStoreId, queryStoreGoodsOnSaleFilterListDTO.getStoreId())
                    .list().stream().collect(toMap(SpecPO::getId, Function.identity()));
            specDetailIdAndInfoMap = specDetailMapperService.lambdaQuery()
                    .select(SpecDetailPO::getId, SpecDetailPO::getName, SpecDetailPO::getSort, SpecDetailPO::getSpecId)
                    .in(SpecDetailPO::getSpecId, specIdAndSpecInfoMap.keySet())
                    .list().stream().collect(toMap(SpecDetailPO::getId, Function.identity()));
            List<SkuSpecDetailsPO> skuSpecDetailsPOList = skuSpecDetailsMapperService.lambdaQuery()
                    .select(SkuSpecDetailsPO::getGoodsSpecDetailId, SkuSpecDetailsPO::getSkuId)
                    .in(SkuSpecDetailsPO::getSkuId, skuIdList).list();

            List<Integer> goodsSpecDetailIdList = new ArrayList<>();
            skuSpecDetailsPOList.forEach(skuSpecDetailsPO -> {
                goodsSpecDetailIdList.add(skuSpecDetailsPO.getGoodsSpecDetailId());
                skuIdAndGoodsSpecDetailIdListMap.computeIfAbsent(skuSpecDetailsPO.getSkuId(), k -> new ArrayList<>()).add(skuSpecDetailsPO.getGoodsSpecDetailId());
            });
            List<Integer> goodsSpecIdList = new ArrayList<>();

            List<GoodsSpecDetailPO> goodsSpecDetailPOList = goodsSpecDetailMapperService.lambdaQuery().in(GoodsSpecDetailPO::getId, goodsSpecDetailIdList).list();
            goodsSpecDetailPOList.forEach(goodsSpecDetailPO -> {
                goodsSpecDetailIdAndInfoMap.put(goodsSpecDetailPO.getId(), goodsSpecDetailPO);
                goodsSpecIdList.add(goodsSpecDetailPO.getGoodsSpecId());
                goodsSpecIdAndGoodsSpecDetailInfoMap.computeIfAbsent(goodsSpecDetailPO.getGoodsSpecId(), k -> new ArrayList<>()).add(goodsSpecDetailPO);
            });
            List<GoodsSpecPO> goodsSpecPOList = goodsSpecMapperService.lambdaQuery()
                    .in(GoodsSpecPO::getId, goodsSpecIdList)
                    .list();

            goodsSpecPOList.forEach(goodsSpecPO -> {
                storeGoodsIdAndGoodsSpecPOListMap.computeIfAbsent(goodsSpecPO.getStoreGoodsId(), k -> new ArrayList<>()).add(goodsSpecPO);
                goodsSpecIdAndInfoMap.put(goodsSpecPO.getId(), goodsSpecPO);
            });
        }

        // 查询品牌信息
        Map<Integer, GoodsBrandVO> brandIdAndInfoMap = getBrandIdAndInfoMap(brandIdList);
        // 查询单位信息
        Map<Integer, String> unitIdAndNameMap = getUnitIdAndNameMap(goodsUnitIdList);

        // 组装销售分组信息
        Map<Integer, List<Integer>> storeGoodsIdAndSaleCategoryIdListMap = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                .in(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, storeGoodsIdSet)
                .list().stream().distinct().collect(Collectors.groupingBy(SaleCategoryAndStoreGoodsPO::getStoreGoodsId,
                        Collectors.mapping(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, toList())));
        Map<Integer, StoreGoodsOnSaleExtendVO.SaleCategoryVO> saleCategoryIdAndInfoMap = new HashMap<>();
        List<SaleCategoryPO> saleCategoryPOList = null;
        if (MapUtils.isNotEmpty(storeGoodsIdAndSaleCategoryIdListMap)) {
            saleCategoryPOList = saleCategoryMapperService.lambdaQuery()
                    .eq(SaleCategoryPO::getChannelId, queryStoreGoodsOnSaleFilterListDTO.getChannelId())
                    .in(SaleCategoryPO::getId,
                            storeGoodsIdAndSaleCategoryIdListMap.values()
                                    .stream().flatMap(List::stream).distinct().toArray())
                    .list();
        }
        if (CollectionUtils.isNotEmpty(saleCategoryPOList)) {
            saleCategoryIdAndInfoMap.putAll(saleCategoryConverter.toStoreGoodsOnSaleExtendVOSaleCategoryVOList(
                            saleCategoryPOList).stream()
                    .collect(toMap(StoreGoodsOnSaleExtendVO.SaleCategoryVO::getId, Function.identity())));
        }

        for (StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO : storeGoodsOnSaleExtendVOList) {
            List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeGoodsIdAndSalesProgramGoodsPOListMap.get(storeGoodsOnSaleExtendVO.getId());
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramGoodsPOList.get(0);
            // 组装封面图片
            if (!Objects.isNull(storeSalesProgramGoodsPO.getCoverPicture())) {
                storeGoodsOnSaleExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
                storeGoodsOnSaleExtendVO.setPicture(List.of(coverIdAndInfoMap.get(storeSalesProgramGoodsPO.getCoverPicture())));
            }
            // 组装标签信息
            if (!Objects.isNull(storeSalesProgramGoodsPO.getTag())) {
                storeGoodsOnSaleExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
                storeGoodsOnSaleExtendVO.setLabelInfo(labelIdAndInfoMap.get(storeSalesProgramGoodsPO.getTag()));
            }
            storeGoodsOnSaleExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());

            // 组装品牌信息
            if (!Objects.isNull(storeGoodsOnSaleExtendVO.getBrand())) {
                storeGoodsOnSaleExtendVO.setBrandInfo(brandIdAndInfoMap.get(storeGoodsOnSaleExtendVO.getBrand()));
            }
            // 组装销售分组信息
            if (storeGoodsIdAndSaleCategoryIdListMap.containsKey(storeGoodsOnSaleExtendVO.getId())) {
                storeGoodsOnSaleExtendVO.setCategoryList(storeGoodsIdAndSaleCategoryIdListMap.get(storeGoodsOnSaleExtendVO.getId()).stream()
                        .map(saleCategoryIdAndInfoMap::get).filter(Objects::nonNull).toList());
            }

            // 组装规格信息
            commonService.assembleStoreGoodsSpecList(storeGoodsOnSaleExtendVO, storeGoodsIdAndGoodsSpecPOListMap, goodsSpecIdAndGoodsSpecDetailInfoMap,
                    specIdAndSpecInfoMap, specDetailIdAndInfoMap);
            // 组装规格详情列表信息
            assembleStoreGoodsSpecDetailList(storeGoodsOnSaleExtendVO, AssembleForFilterStoreGoodsSpecDetailListBO.builder()
                    .storeGoodsIdAndSalesProgramGoodsPricePOMap(storeGoodsIdAndSalesProgramGoodsPricePOMap)
                    .storeSalesProgramGoodsPOList(storeSalesProgramGoodsPOList)
                    .storeGoodsIdAndGoodsPackageSkuPOListMap(storeGoodsIdAndGoodsPackageSkuPOListMap)
                    .unitIdAndNameMap(unitIdAndNameMap)
                    .goodsSpecDetailIdAndInfoMap(goodsSpecDetailIdAndInfoMap)
                    .skuIdAndGoodsSpecDetailIdListMap(skuIdAndGoodsSpecDetailIdListMap)
                    .goodsSpecIdAndInfoMap(goodsSpecIdAndInfoMap)
                    .specIdAndSpecInfoMap(specIdAndSpecInfoMap)
                    .specDetailIdAndInfoMap(specDetailIdAndInfoMap)
                    .build());
        }
        return storeGoodsOnSaleExtendVOList;
    }

    /**
     * 组装商品品牌、商品id、商品规格信息等
     *
     * @param storeGoodsPOList             商品列表
     * @param storeGoodsIdSet              商品ID集合
     * @param hasMultipleSpec              是否存在多规格
     * @param brandIdList                  品牌ID集合
     * @param storeGoodsOnSaleExtendVOList 商品详情VO集合
     */
    private void assembleBrandIdAndStoreGoodsIdAndStoreGoodsOnSaleExtendVO(List<StoreGoodsPO> storeGoodsPOList,
                                                                           Set<Integer> storeGoodsIdSet,
                                                                           AtomicBoolean hasMultipleSpec,
                                                                           List<Integer> brandIdList,
                                                                           List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList) {
        storeGoodsPOList.forEach(storeGoodsPO -> {
            storeGoodsIdSet.add(storeGoodsPO.getId());
            if (storeGoodsPO.getGoodsSpec() == 2) {
                hasMultipleSpec.set(true);
            }
            if (!Objects.isNull(storeGoodsPO.getBrand())) {
                brandIdList.add(storeGoodsPO.getBrand());
            }
            storeGoodsOnSaleExtendVOList.add(storeGoodsConverter.toStoreGoodsOnSaleExtendVO(storeGoodsPO));
        });
    }

    /**
     * 获取单位信息
     *
     * @param goodsUnitIdList 商品单位ID集合
     * @return 商品单位ID和单位名称的映射
     */
    private Map<Integer, String> getUnitIdAndNameMap(List<Integer> goodsUnitIdList) {
        Map<Integer, String> unitIdAndNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(goodsUnitIdList)) {
            unitIdAndNameMap = saleUnitMeasurementMapperService.listByIds(goodsUnitIdList).stream()
                    .collect(toMap(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName));
        }
        return unitIdAndNameMap;
    }

    /**
     * 获取品牌信息
     *
     * @param brandIdList 品牌ID集合
     * @return 品牌ID和品牌信息的映射
     */
    private Map<Integer, GoodsBrandVO> getBrandIdAndInfoMap(List<Integer> brandIdList) {
        Map<Integer, GoodsBrandVO> brandIdAndInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(brandIdList)) {
            brandIdAndInfoMap = goodsBrandConverter.toGoodsBrandVOList(
                    goodsBrandMapperService.queryGoodsBrandExtendListByIdList(brandIdList)).stream().collect(toMap(GoodsBrandVO::getId, Function.identity()));
        }
        return brandIdAndInfoMap;
    }

    /**
     * 组装商品规格详情列表信息
     *
     * @param storeGoodsOnSaleExtendVO                    商品详情VO
     * @param assembleForFilterStoreGoodsSpecDetailListBO 组装商品规格详情列表信息参数对象
     */
    private void assembleStoreGoodsSpecDetailList(StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                                  AssembleForFilterStoreGoodsSpecDetailListBO assembleForFilterStoreGoodsSpecDetailListBO) {
        // 门店商品ID和商品SKU信息的映射
        Map<Integer, List<GoodsPackageSkuPO>> storeGoodsIdAndGoodsPackageSkuPOListMap = assembleForFilterStoreGoodsSpecDetailListBO.getStoreGoodsIdAndGoodsPackageSkuPOListMap();
        List<StoreGoodsSpecAndDetailsVO> storeGoodsSpecAndDetailsVOList = new ArrayList<>();
        Map<Integer, GoodsPackageSkuPO> skuIdAndGoodsPackageSkuPOMap = storeGoodsIdAndGoodsPackageSkuPOListMap.get(storeGoodsOnSaleExtendVO.getId())
                .stream().collect(toMap(GoodsPackageSkuPO::getId, Function.identity()));
        // 每个SKU商品对应的销售方案商品信息
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = assembleForFilterStoreGoodsSpecDetailListBO.getStoreSalesProgramGoodsPOList();
        // 商品单位ID和单位名称的映射
        Map<Integer, String> unitIdAndNameMap = assembleForFilterStoreGoodsSpecDetailListBO.getUnitIdAndNameMap();
        // 门店商品ID和销售方案商品时段价格信息的映射
        Map<Integer, StoreSalesProgramGoodsPricePO> storeGoodsIdAndSalesProgramGoodsPricePOMap = assembleForFilterStoreGoodsSpecDetailListBO.getStoreGoodsIdAndSalesProgramGoodsPricePOMap();
        // 商品SkuID和商品规格值ID的映射
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = assembleForFilterStoreGoodsSpecDetailListBO.getSkuIdAndGoodsSpecDetailIdListMap();
        // 商品规格值的ID和商品规格值的信息的映射
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = assembleForFilterStoreGoodsSpecDetailListBO.getGoodsSpecDetailIdAndInfoMap();
        // 商品规格ID和商品规格信息的映射
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = assembleForFilterStoreGoodsSpecDetailListBO.getGoodsSpecIdAndInfoMap();
        Map<Integer, SpecPO> specIdAndSpecInfoMap = assembleForFilterStoreGoodsSpecDetailListBO.getSpecIdAndSpecInfoMap();
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap = assembleForFilterStoreGoodsSpecDetailListBO.getSpecDetailIdAndInfoMap();
        for (StoreSalesProgramGoodsPO salesProgramGoodsPO : storeSalesProgramGoodsPOList) {
            GoodsPackageSkuPO goodsPackageSkuPO = skuIdAndGoodsPackageSkuPOMap.get(salesProgramGoodsPO.getGoodsPackageSkuId());
            StoreGoodsSpecAndDetailsVO storeGoodsSpecAndDetailsVO = new StoreGoodsSpecAndDetailsVO();
            storeGoodsSpecAndDetailsVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
            storeGoodsSpecAndDetailsVO.setBarcode(goodsPackageSkuPO.getBarcode());
            storeGoodsSpecAndDetailsVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
            storeGoodsSpecAndDetailsVO.setSkuId(goodsPackageSkuPO.getId());
            storeGoodsSpecAndDetailsVO.setGoodsUnitName(unitIdAndNameMap.get(storeGoodsOnSaleExtendVO.getGoodsUnit()));
            storeGoodsSpecAndDetailsVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
            storeGoodsSpecAndDetailsVO.setStoreSalesProgramGoodsId(salesProgramGoodsPO.getId());
            Optional.ofNullable(storeGoodsIdAndSalesProgramGoodsPricePOMap.get(salesProgramGoodsPO.getId()))
                    .map(StoreSalesProgramGoodsPricePO::getTimeSectionPriceCompute)
                    .ifPresent(price ->
                            storeGoodsSpecAndDetailsVO.setGoodsSpecSellingPrice(price.setScale(2, RoundingMode.HALF_UP).toString())
                    );
            // 如果商品规格为2，则组装商品规格详情列表
            if (storeGoodsOnSaleExtendVO.getGoodsSpec() == 2 && skuIdAndGoodsSpecDetailIdListMap.containsKey(salesProgramGoodsPO.getGoodsPackageSkuId())) {
                List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList = new ArrayList<>();
                skuIdAndGoodsSpecDetailIdListMap.get(salesProgramGoodsPO.getGoodsPackageSkuId()).forEach(goodsSpecDetailId -> {
                    StoreGoodsSpecRelationVO storeGoodsSpecRelationVO = new StoreGoodsSpecRelationVO();
                    storeGoodsSpecRelationVO.setGoodsSpecDetailId(goodsSpecDetailId);
                    GoodsSpecDetailPO goodsSpecDetailPO = goodsSpecDetailIdAndInfoMap.get(goodsSpecDetailId);
                    storeGoodsSpecRelationVO.setGoodsSpecDetailSort(goodsSpecDetailPO.getSort());
                    storeGoodsSpecRelationVO.setGoodsSpecDetailName(specDetailIdAndInfoMap.get(goodsSpecDetailPO.getSpecDetailId()).getName());
                    GoodsSpecPO goodsSpecPO = goodsSpecIdAndInfoMap.get(goodsSpecDetailPO.getGoodsSpecId());
                    storeGoodsSpecRelationVO.setGoodsSpecId(goodsSpecPO.getId());
                    storeGoodsSpecRelationVO.setGoodsSpecSort(goodsSpecPO.getSort());
                    storeGoodsSpecRelationVO.setGoodsSpecName(specIdAndSpecInfoMap.get(goodsSpecPO.getSpecId()).getName());
                    storeGoodsSpecRelationList.add(storeGoodsSpecRelationVO);
                });
                commonService.sortStoreGoodsSpecList(storeGoodsSpecRelationList);
                storeGoodsSpecAndDetailsVO.setStoreGoodsSpecRelationList(storeGoodsSpecRelationList);
            }
            storeGoodsSpecAndDetailsVOList.add(storeGoodsSpecAndDetailsVO);
        }
        // 排序商品规格详情列表
        if (storeGoodsOnSaleExtendVO.getGoodsSpec() == 2) {
            commonService.sortStoreGoodsSpecAndDetailsList(storeGoodsSpecAndDetailsVOList);
        }
        storeGoodsOnSaleExtendVO.setStoreGoodsSpecDetailsList(storeGoodsSpecAndDetailsVOList);
    }

    /**
     * 查询可售商品列表
     *
     * @param verifyStoreGoodsAvailableForSaleScaleCodeDTO 校验DTO
     * @return 可售商品列表
     */
    private List<StoreGoodsExtendVO> queryStoreGoodsAvailableForSaleList(
            VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO) {
        List<StoreGoodsPO> storeGoodsPOList =
                storeGoodsMapperService.listByIds(verifyStoreGoodsAvailableForSaleScaleCodeDTO.getStoreGoodsIdList());
        List<StoreGoodsExtendVO> storeGoodsExtendVOList = storeGoodsConverter.toStoreGoodsExtendVOList(storeGoodsPOList);
        // 组装商品名称、销售价格、商品图片
        commonService.assembleGoodsNameAndSalePriceAndPictureForWeighList(storeGoodsExtendVOList,
                verifyStoreGoodsAvailableForSaleScaleCodeDTO.getStoreId(), verifyStoreGoodsAvailableForSaleScaleCodeDTO.getChannelId());
        // 查询商品扩展信息
        commonService.assembleGoodsExpandInfo(storeGoodsExtendVOList, verifyStoreGoodsAvailableForSaleScaleCodeDTO.getStoreGoodsExpandList(),
                verifyStoreGoodsAvailableForSaleScaleCodeDTO.getStoreId(), verifyStoreGoodsAvailableForSaleScaleCodeDTO.getChannelId(),
                verifyStoreGoodsAvailableForSaleScaleCodeDTO.getScaleType());
        return storeGoodsExtendVOList;
    }

    /**
     * 生成秤内自编码
     *
     * @param verifyStoreGoodsAvailableForSaleScaleCodeDTO 校验DTO
     * @param verifyStoreGoodsAvailableForSaleScaleCodeVO  校验结果
     */
    private void generateScaleCustomCode(VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO,
                                         VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleScaleCodeVO) {
        if (CollectionUtils.isNotEmpty(verifyStoreGoodsAvailableForSaleScaleCodeVO.getGoodsList())) {
            // 先查询数据库中已经存在的秤内码
            List<GoodsScaleCodePO> goodsScaleCodePOList = goodsScaleCodeMapperService.lambdaQuery()
                    .eq(GoodsScaleCodePO::getScaleType, verifyStoreGoodsAvailableForSaleScaleCodeDTO.getScaleType())
                    .eq(GoodsScaleCodePO::getStoreId, verifyStoreGoodsAvailableForSaleScaleCodeDTO.getStoreId())
                    .list();
            // 获取所有的秤内自编码，用于找到最大的自编码
            List<String> scaleCustomCodeList = new ArrayList<>();
            Map<Integer, GoodsScaleCodePO> storeGoodsIdAndInfoMap = new HashMap<>();
            for (GoodsScaleCodePO goodsScaleCodePO : goodsScaleCodePOList) {
                storeGoodsIdAndInfoMap.put(goodsScaleCodePO.getGoodsId(), goodsScaleCodePO);
                if (StringUtils.isNotEmpty(goodsScaleCodePO.getScaleCustomCode())) {
                    scaleCustomCodeList.add(goodsScaleCodePO.getScaleCustomCode());
                }
            }
            int maxScaleCustomCode = 0;
            if (CollectionUtils.isNotEmpty(scaleCustomCodeList)) {
                scaleCustomCodeList.sort(Comparator.reverseOrder());
                maxScaleCustomCode = Integer.parseInt(scaleCustomCodeList.get(0));
            }
            // 获取所有需要生成秤内自编码的商品ID
            List<Integer> needGenerateScaleCustomCodeStoreGoodsIdList =
                    verifyStoreGoodsAvailableForSaleScaleCodeVO.getGoodsList().stream()
                            .filter(goodsExtendVO -> StringUtils.isEmpty(goodsExtendVO.getGoodsScaleCodeList().get(0).getScaleCustomCode()))
                            .map(StoreGoodsExtendVO::getId)
                            .toList();
            Map<Integer, StoreGoodsExtendVO> storeGoodsIdAndStoreGoodsExtendVOInfoMap =
                    verifyStoreGoodsAvailableForSaleScaleCodeVO.getGoodsList().stream()
                            .collect(toMap(StoreGoodsExtendVO::getId, Function.identity()));
            if (CollectionUtils.isNotEmpty(needGenerateScaleCustomCodeStoreGoodsIdList)) {
                // 生成秤内自编码
                List<String> needGenerateScaleCustomCodeList = generateScaleCustomCodeList(maxScaleCustomCode + 1, needGenerateScaleCustomCodeStoreGoodsIdList.size());
                // 更新数据库
                List<GoodsScaleCodePO> goodsScaleCodePOListForUpdate = new ArrayList<>();
                for (int i = 0; i < needGenerateScaleCustomCodeStoreGoodsIdList.size(); i++) {
                    Integer storeGoodsId = needGenerateScaleCustomCodeStoreGoodsIdList.get(i);
                    GoodsScaleCodePO goodsScaleCodePO = storeGoodsIdAndInfoMap.get(storeGoodsId);
                    goodsScaleCodePO.setScaleCustomCode(needGenerateScaleCustomCodeList.get(i));
                    goodsScaleCodePO.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
                    goodsScaleCodePOListForUpdate.add(goodsScaleCodePO);
                    storeGoodsIdAndStoreGoodsExtendVOInfoMap.get(storeGoodsId).getGoodsScaleCodeList()
                            .get(0).setScaleCustomCode(needGenerateScaleCustomCodeList.get(i));
                }
                goodsScaleCodeMapperService.updateBatchById(goodsScaleCodePOListForUpdate);
            }
        }
    }

    /**
     * 生成秤内自编码列表
     *
     * @param start 开始值
     * @param count 数量
     * @return 秤内自编码列表
     */
    public List<String> generateScaleCustomCodeList(int start, int count) {
        List<Integer> uniqueNumberList = new ArrayList<>();
        for (int i = start; i < start + count; i++) {
            uniqueNumberList.add(i);
        }
        return convertIntegersToStringsWithPadding(uniqueNumberList, POSITION_FILLING_FIVE);
    }

    /**
     * 组装校验秤内码、秤内自编码、计价方式等结果
     *
     * @param storeGoodsExtendVOList 商品列表
     * @return 校验结果
     */
    private static VerifyStoreGoodsAvailableForSaleScaleCodeVO assembleVerifyValuationResult(
            List<StoreGoodsExtendVO> storeGoodsExtendVOList) {
        // 计价方式错误的商品列表
        VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo goodsChangeReminderInfoForValuationMethodError =
                new VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo();
        goodsChangeReminderInfoForValuationMethodError.setErrorType(ScaleCodeChangeReminderType.VALUATION_ERROR.getValue());
        goodsChangeReminderInfoForValuationMethodError.setGoodsList(new ArrayList<>());
        Iterator<StoreGoodsExtendVO> storeGoodsExtendVOIterator = storeGoodsExtendVOList.iterator();
        while (storeGoodsExtendVOIterator.hasNext()) {
            StoreGoodsExtendVO storeGoodsExtendVO = storeGoodsExtendVOIterator.next();
            boolean isErrorFlag = false;
            // 商品计价方式为普通，则提示错误
            if (storeGoodsExtendVO.getValuationMethod() == GoodsValuationMethod.NORMAL.getId()) {
                goodsChangeReminderInfoForValuationMethodError.getGoodsList().add(storeGoodsExtendVO);
                isErrorFlag = true;
            }
            // 如果有错误，则移除该商品
            if (isErrorFlag) {
                storeGoodsExtendVOIterator.remove();
            }
        }
        VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleScaleCodeVO = new VerifyStoreGoodsAvailableForSaleScaleCodeVO();
        verifyStoreGoodsAvailableForSaleScaleCodeVO.setErrorGoodsList(List.of(goodsChangeReminderInfoForValuationMethodError));
        verifyStoreGoodsAvailableForSaleScaleCodeVO.setGoodsList(storeGoodsExtendVOList);
        return verifyStoreGoodsAvailableForSaleScaleCodeVO;
    }

    /**
     * 组装校验秤内码、秤内自编码、计价方式等结果
     *
     * @param storeGoodsExtendVOList 商品列表
     * @return 校验结果
     */
    private static VerifyStoreGoodsAvailableForSaleScaleCodeVO assembleVerifyScaleCodeAndValuationResult(
            List<StoreGoodsExtendVO> storeGoodsExtendVOList) {
        // 计价方式错误的商品列表
        VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo goodsChangeReminderInfoForValuationMethodError =
                new VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo();
        goodsChangeReminderInfoForValuationMethodError.setErrorType(ScaleCodeChangeReminderType.VALUATION_ERROR.getValue());
        goodsChangeReminderInfoForValuationMethodError.setGoodsList(new ArrayList<>());
        // 秤内自编码错误的商品列表
        VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo goodsChangeReminderInfoForCustomCodeError =
                new VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo();
        goodsChangeReminderInfoForCustomCodeError.setErrorType(ScaleCodeChangeReminderType.CUSTOM_CODE_ERROR.getValue());
        goodsChangeReminderInfoForCustomCodeError.setGoodsList(new ArrayList<>());
        // 秤内码错误的商品列表
        VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo goodsChangeReminderInfoForPluCodeError =
                new VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo();
        goodsChangeReminderInfoForPluCodeError.setErrorType(ScaleCodeChangeReminderType.PLU_CODE_ERROR.getValue());
        goodsChangeReminderInfoForPluCodeError.setGoodsList(new ArrayList<>());
        Iterator<StoreGoodsExtendVO> storeGoodsExtendVOIterator = storeGoodsExtendVOList.iterator();
        while (storeGoodsExtendVOIterator.hasNext()) {
            StoreGoodsExtendVO storeGoodsExtendVO = storeGoodsExtendVOIterator.next();
            boolean isErrorFlag = filterVerifyScaleCodeAndValuationResult(storeGoodsExtendVO,
                    goodsChangeReminderInfoForValuationMethodError, goodsChangeReminderInfoForPluCodeError, goodsChangeReminderInfoForCustomCodeError);
            // 如果有错误，则移除该商品
            if (isErrorFlag) {
                storeGoodsExtendVOIterator.remove();
            }
        }
        VerifyStoreGoodsAvailableForSaleScaleCodeVO verifyStoreGoodsAvailableForSaleScaleCodeVO = new VerifyStoreGoodsAvailableForSaleScaleCodeVO();
        verifyStoreGoodsAvailableForSaleScaleCodeVO.setErrorGoodsList(List.of(goodsChangeReminderInfoForValuationMethodError,
                goodsChangeReminderInfoForCustomCodeError, goodsChangeReminderInfoForPluCodeError));
        verifyStoreGoodsAvailableForSaleScaleCodeVO.setGoodsList(storeGoodsExtendVOList);
        return verifyStoreGoodsAvailableForSaleScaleCodeVO;
    }

    /**
     * 过滤校验秤内码、秤内自编码、计价方式等结果
     *
     * @param storeGoodsExtendVO                             商品信息
     * @param goodsChangeReminderInfoForValuationMethodError 计价方式错误的商品列表
     * @param goodsChangeReminderInfoForPluCodeError         秤内码错误的商品列表
     * @param goodsChangeReminderInfoForCustomCodeError      秤内自编码错误的商品列表
     * @return 是否有错误
     */
    private static boolean filterVerifyScaleCodeAndValuationResult(
            StoreGoodsExtendVO storeGoodsExtendVO,
            VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo goodsChangeReminderInfoForValuationMethodError,
            VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo goodsChangeReminderInfoForPluCodeError,
            VerifyStoreGoodsAvailableForSaleScaleCodeVO.GoodsChangeReminderInfo goodsChangeReminderInfoForCustomCodeError) {
        boolean isErrorFlag = false;
        // 商品计价方式为普通，则提示错误
        if (storeGoodsExtendVO.getValuationMethod() == GoodsValuationMethod.NORMAL.getId()) {
            goodsChangeReminderInfoForValuationMethodError.getGoodsList().add(storeGoodsExtendVO);
            isErrorFlag = true;
        }
        if (CollectionUtils.isEmpty(storeGoodsExtendVO.getGoodsScaleCodeList())) {
            goodsChangeReminderInfoForPluCodeError.getGoodsList().add(storeGoodsExtendVO);
            isErrorFlag = true;
        } else {
            StoreGoodsBaseExtendVO.GoodsScaleCodeVO goodsScaleCodeVO = storeGoodsExtendVO.getGoodsScaleCodeList().get(0);
            // 秤内自编码非5位则提示错误
            if (StringUtils.isNotEmpty(goodsScaleCodeVO.getScaleCustomCode()) && goodsScaleCodeVO.getScaleCustomCode().length() != 5) {
                goodsChangeReminderInfoForCustomCodeError.getGoodsList().add(storeGoodsExtendVO);
                isErrorFlag = true;
            }
            // 秤内码非4位则提示错误
            if (StringUtils.isEmpty(goodsScaleCodeVO.getPluCode()) || goodsScaleCodeVO.getPluCode().length() != 4) {
                goodsChangeReminderInfoForPluCodeError.getGoodsList().add(storeGoodsExtendVO);
                isErrorFlag = true;
            }
        }
        return isErrorFlag;
    }

    /**
     * 生成指定数量的唯一的四位数，且不与已存在的四位数重复
     *
     * @param start           起始的四位数
     * @param count           需要生成的数量
     * @param existingNumbers 已存在的四位数集合
     * @return 生成的唯一四位数集合
     */
    public List<String> generatePluCodeList(int start, int count, List<String> existingNumbers) {
        List<Integer> existingNumberList = existingNumbers.stream().map(Integer::parseInt).toList();
        List<Integer> uniqueNumbers = new ArrayList<>();
        int currentNumber = start;
        while (uniqueNumbers.size() < count) {
            if (!existingNumberList.contains(currentNumber)) {
                uniqueNumbers.add(currentNumber);
            }
            currentNumber++;
            if (currentNumber > 9999) {
                break;
            }
        }
        return convertIntegersToStringsWithPadding(uniqueNumbers, POSITION_FILLING_FOUR);
    }

    /**
     * 将整数列表转换为四位数字符串列表，并在左侧填充0
     *
     * @param uniqueNumbers  整数列表
     * @param paddingPattern 填充字符
     * @return 四位数字符串列表
     */
    public List<String> convertIntegersToStringsWithPadding(List<Integer> uniqueNumbers, String paddingPattern) {
        return uniqueNumbers.stream()
                .map(number -> String.format(paddingPattern, number))
                .toList();
    }

    /**
     * 组装可售商品的封面、售卖名称、价格、时段价格等信息
     *
     * @param storeGoodsAvailableForSalePOSExtendVOList      可售商品列表
     * @param storeGoodsIdAndStoreSalesProgramGoodsPOListMap 店铺商品ID和销售方案商品信息集合的映射
     * @param storeGoodsSpecDetailListForAvailableForSaleBO  用于组装商品规格详情列表的业务对象(针对商品调价列表接口)
     * @param storeSalesProgramGoodsIdAndPricePOListMap      销售方案商品ID和对应时段价格信息列表的映射
     * @param serialNumberForUnitPriceDisplay                商品某个时段的序号
     * @param storeSalesProgramPO                            销售方案信息
     */
    private void assembleStoreGoodsAvailableForSaleVOListAndReturnCoverIdSet(
            List<StoreGoodsAvailableForSalePOSExtendVO> storeGoodsAvailableForSalePOSExtendVOList,
            Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndStoreSalesProgramGoodsPOListMap,
            AssembleStoreGoodsSpecDetailListForAvailableForSaleBO storeGoodsSpecDetailListForAvailableForSaleBO,
            Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndPricePOListMap,
            int serialNumberForUnitPriceDisplay,
            StoreSalesProgramPO storeSalesProgramPO) {
        // 封面ID和封面信息的映射
        Map<Integer, GoodsPictureVO> coverIdAndPictureVOMap = storeGoodsSpecDetailListForAvailableForSaleBO.getCoverIdAndPictureVOMap();
        // 商品skuID和SKU信息的映射
        Map<Integer, GoodsPackageSkuPO> goodsPackageSkuIdAndInfoMap = storeGoodsSpecDetailListForAvailableForSaleBO.getGoodsPackageSkuIdAndInfoMap();
        // 商品单位ID和商品单位名称的映射
        Map<Integer, String> storeGoodsUnitIdAndNameMap = storeGoodsSpecDetailListForAvailableForSaleBO.getStoreGoodsUnitIdAndNameMap();
        for (StoreGoodsAvailableForSalePOSExtendVO storeGoodsAvailableForSalePOSExtendVO : storeGoodsAvailableForSalePOSExtendVOList) {
            List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList =
                    storeGoodsIdAndStoreSalesProgramGoodsPOListMap.get(storeGoodsAvailableForSalePOSExtendVO.getId());
            if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
                continue;
            }
            StoreGoodsAvailableForSalePOSExtendVO.StoreGoodsSalesInfoVO storeGoodsSalesInfoVO = new StoreGoodsAvailableForSalePOSExtendVO.StoreGoodsSalesInfoVO();
            // 因为这儿是只考虑一种销售方案的情况，所以一个商品的不同SKU在同一个销售方案中的售卖名称、封面、标签都是一样的，所以取第一个即可
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramGoodsPOList.get(0);
            storeGoodsSalesInfoVO.setCoverPicture(storeSalesProgramGoodsPO.getCoverPicture());
            storeGoodsSalesInfoVO.setCoverPictureInfo(coverIdAndPictureVOMap.get(storeSalesProgramGoodsPO.getCoverPicture()));
            storeGoodsSalesInfoVO.setGoodsSaleName(storeSalesProgramGoodsPO.getGoodsSaleName());
            storeGoodsSalesInfoVO.setTag(storeSalesProgramGoodsPO.getTag());
            List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList = new ArrayList<>();
            storeGoodsIdAndStoreSalesProgramGoodsPOListMap.get(storeGoodsAvailableForSalePOSExtendVO.getId()).forEach(storeSalesProgramGoodsPOItem -> {
                GoodsPackageSkuPO goodsPackageSkuPO = goodsPackageSkuIdAndInfoMap.get(storeSalesProgramGoodsPOItem.getGoodsPackageSkuId());
                StoreGoodsSpecAndDetailsFullVO storeGoodsSpecAndDetailsFullVO = new StoreGoodsSpecAndDetailsFullVO();
                storeGoodsSpecAndDetailsFullVO.setBarcode(goodsPackageSkuPO.getBarcode());
                storeGoodsSpecAndDetailsFullVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
                storeGoodsSpecAndDetailsFullVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
                storeGoodsSpecAndDetailsFullVO.setSkuId(goodsPackageSkuPO.getId());
                // 设置商品封面
                storeGoodsSpecAndDetailsFullVO.setImage(coverIdAndPictureVOMap.get(goodsPackageSkuPO.getCover()));
                storeGoodsSpecAndDetailsFullVO.setGoodsUnitName(storeGoodsUnitIdAndNameMap.get(storeGoodsAvailableForSalePOSExtendVO.getGoodsUnit()));
                storeGoodsSpecAndDetailsFullVO.setStoreSalesProgramGoodsId(storeSalesProgramGoodsPOItem.getId());
                storeGoodsSpecAndDetailsFullVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
                // 设置商品时段信息（包含价格）
                setStoreGoodsSectionList(storeSalesProgramGoodsIdAndPricePOListMap, storeSalesProgramPO, storeSalesProgramGoodsPOItem, storeGoodsSpecAndDetailsFullVO);
                // 如果商品规格为2，则组装商品规格详情列表
                setStoreGoodsSpecDetailsList(storeGoodsAvailableForSalePOSExtendVO, storeSalesProgramGoodsPOItem,
                        storeGoodsSpecAndDetailsFullVO, storeGoodsSpecDetailListForAvailableForSaleBO);
                storeGoodsSpecDetailsList.add(storeGoodsSpecAndDetailsFullVO);
            });
            // 排序商品规格详情列表
            sortStoreGoodsSpecDetailsList(storeGoodsAvailableForSalePOSExtendVO.getGoodsSpec(), storeGoodsSpecDetailsList);
            storeGoodsSalesInfoVO.setStoreGoodsSpecDetailsList(storeGoodsSpecDetailsList);
            // 设置前端列表处需要展示的售价
            setStoreGoodsDefaultSellingPrice(serialNumberForUnitPriceDisplay, storeGoodsSpecDetailsList, storeGoodsSalesInfoVO);
            storeGoodsAvailableForSalePOSExtendVO.setStoreGoodsSalesInfoVO(storeGoodsSalesInfoVO);
            storeGoodsAvailableForSalePOSExtendVO.setStoreSalesProgramInfo(storeSalesProgramConverter.toStoreSalesProgramExtendVO(storeSalesProgramPO));
        }
    }

    /**
     * 设置商品规格详情列表
     *
     * @param storeGoodsAvailableForSalePOSExtendVO         可售商品信息
     * @param storeSalesProgramGoodsPOItem                  销售方案商品信息
     * @param storeGoodsSpecAndDetailsFullVO                商品规格详情信息
     * @param storeGoodsSpecDetailListForAvailableForSaleBO 用于组装商品规格详情列表的业务对象(针对商品调价列表接口)
     */
    private void setStoreGoodsSpecDetailsList(StoreGoodsAvailableForSalePOSExtendVO storeGoodsAvailableForSalePOSExtendVO,
                                              StoreSalesProgramGoodsPO storeSalesProgramGoodsPOItem,
                                              StoreGoodsSpecAndDetailsFullVO storeGoodsSpecAndDetailsFullVO,
                                              AssembleStoreGoodsSpecDetailListForAvailableForSaleBO storeGoodsSpecDetailListForAvailableForSaleBO) {
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = storeGoodsSpecDetailListForAvailableForSaleBO.getSkuIdAndGoodsSpecDetailIdListMap();
        if (storeGoodsAvailableForSalePOSExtendVO.getGoodsSpec() != 2 ||
                !skuIdAndGoodsSpecDetailIdListMap.containsKey(storeSalesProgramGoodsPOItem.getGoodsPackageSkuId())) {
            return;
        }
        // 规格ID和规格信息的映射
        Map<Integer, SpecPO> specIdAndSpecInfoMap = storeGoodsSpecDetailListForAvailableForSaleBO.getSpecIdAndSpecInfoMap();
        // 规格详情ID和规格详情信息的映射
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap = storeGoodsSpecDetailListForAvailableForSaleBO.getSpecDetailIdAndInfoMap();
        // 商品规格详情ID和商品规格详情信息的映射
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = storeGoodsSpecDetailListForAvailableForSaleBO.getGoodsSpecDetailIdAndInfoMap();
        // 商品SKUId和商品规格详情ID的映射
        // 商品规格ID和商品规格信息的映射
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = storeGoodsSpecDetailListForAvailableForSaleBO.getGoodsSpecIdAndInfoMap();
        List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList = new ArrayList<>();
        skuIdAndGoodsSpecDetailIdListMap.get(storeSalesProgramGoodsPOItem.getGoodsPackageSkuId()).forEach(goodsSpecDetailId -> {
            StoreGoodsSpecRelationVO storeGoodsSpecRelationVO = new StoreGoodsSpecRelationVO();
            storeGoodsSpecRelationVO.setGoodsSpecDetailId(goodsSpecDetailId);
            GoodsSpecDetailPO goodsSpecDetailPO = goodsSpecDetailIdAndInfoMap.get(goodsSpecDetailId);
            storeGoodsSpecRelationVO.setGoodsSpecDetailSort(goodsSpecDetailPO.getSort());
            storeGoodsSpecRelationVO.setGoodsSpecDetailName(specDetailIdAndInfoMap.get(goodsSpecDetailPO.getSpecDetailId()).getName());
            GoodsSpecPO goodsSpecPO = goodsSpecIdAndInfoMap.get(goodsSpecDetailPO.getGoodsSpecId());
            storeGoodsSpecRelationVO.setGoodsSpecId(goodsSpecPO.getId());
            storeGoodsSpecRelationVO.setGoodsSpecSort(goodsSpecPO.getSort());
            storeGoodsSpecRelationVO.setGoodsSpecName(specIdAndSpecInfoMap.get(goodsSpecPO.getSpecId()).getName());
            storeGoodsSpecRelationList.add(storeGoodsSpecRelationVO);
        });
        commonService.sortStoreGoodsSpecList(storeGoodsSpecRelationList);
        storeGoodsSpecAndDetailsFullVO.setStoreGoodsSpecRelationList(storeGoodsSpecRelationList);
    }

    /**
     * 设置规格商品的时段信息（包含时段价格）
     *
     * @param storeSalesProgramGoodsIdAndPricePOListMap 存储销售方案商品ID和销售方案商品价格列表的Map
     * @param storeSalesProgramPO                       销售方案信息
     * @param storeSalesProgramGoodsPOItem              销售方案商品信息
     * @param storeGoodsSpecAndDetailsFullVO            商品规格详情信息
     */
    private static void setStoreGoodsSectionList(Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramGoodsIdAndPricePOListMap,
                                                 StoreSalesProgramPO storeSalesProgramPO,
                                                 StoreSalesProgramGoodsPO storeSalesProgramGoodsPOItem,
                                                 StoreGoodsSpecAndDetailsFullVO storeGoodsSpecAndDetailsFullVO) {
        List<StoreGoodsSpecAndDetailsFullVO.SectionVO> sectionList = new ArrayList<>();
        Map<Integer, StoreSalesProgramGoodsPricePO> serialNumberAndPriceInfoMap =
                storeSalesProgramGoodsIdAndPricePOListMap.get(storeSalesProgramGoodsPOItem.getId()).stream()
                        .collect(toMap(StoreSalesProgramGoodsPricePO::getSerialNumber, Function.identity()));
        storeSalesProgramPO.getTimeSection().forEach(section -> {
            StoreGoodsSpecAndDetailsFullVO.SectionVO sectionVO = new StoreGoodsSpecAndDetailsFullVO.SectionVO();
            sectionVO.setSerialNumber(section.getSerialNumber());
            sectionVO.setStart(section.getStart());
            sectionVO.setEnd(section.getEnd());
            StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO = serialNumberAndPriceInfoMap.get(section.getSerialNumber());
            sectionVO.setSellingPrice(storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute());
            sectionVO.setStoreSalesProgramGoodsPriceId(storeSalesProgramGoodsPricePO.getId());
            sectionList.add(sectionVO);
        });
        storeGoodsSpecAndDetailsFullVO.setSectionList(sectionList);
    }

    /**
     * 排序商品规格详情列表
     *
     * @param storeGoodsSpecDetailsList 商品规格详情列表
     */
    private void sortStoreGoodsSpecDetailsList(Integer goodsSpec,
                                               List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList) {
        if (goodsSpec != 2) {
            return;
        }
        sortStoreGoodsSpecAndDetailsList(storeGoodsSpecDetailsList);
    }

    /**
     * 设置前端列表处需要展示的售价
     * 1、需要取第一个规格的时段价格
     * 2、针对时段：如果是生效中的方案对应的商品，则价格需要获取生效时段对应的价格
     * 如果是未生效的方案对应的商品，则价格需要获取方案中第一个时段对应的价格
     *
     * @param serialNumberForUnitPriceDisplay 商品某个时段的序号
     * @param storeGoodsSpecDetailsList       商品规格详情列表
     * @param storeGoodsSalesInfoVO           商品销售信息VO
     */
    private static void setStoreGoodsDefaultSellingPrice(int serialNumberForUnitPriceDisplay,
                                                         List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList,
                                                         StoreGoodsAvailableForSalePOSExtendVO.StoreGoodsSalesInfoVO storeGoodsSalesInfoVO) {
        // 设置前端列表处需要展示的售价
        for (StoreGoodsSpecAndDetailsFullVO.SectionVO sectionVO : storeGoodsSpecDetailsList.get(0).getSectionList()) {
            if (sectionVO.getSerialNumber() == serialNumberForUnitPriceDisplay) {
                storeGoodsSalesInfoVO.setSellingPrice(sectionVO.getSellingPrice().setScale(2, RoundingMode.HALF_UP).toString());
            }
        }
    }

    /**
     * 根据 StoreGoodsSpecAndDetailsVO 内部的 storeGoodsSpecRelationList 排序
     * 排序规则：
     * 1. 依次比较 storeGoodsSpecRelationList 中每个元素的 goodsSpecDetailSort，升序排序；
     * 2. 如果 goodsSpecDetailSort 相等，则比较 goodsSpecDetailId，降序排序；
     * 3. 如果当前比较的元素都相等，则继续比较下一个元素；
     * 4. 如果其中一个列表比较完毕，则列表长度较短的排前面（可根据业务需求调整）。
     *
     * @param specList 待排序的列表，类型为 StoreGoodsSpecAndDetailsVO 或其子类
     */
    public <T extends StoreGoodsSpecAndDetailsFullVO> void sortStoreGoodsSpecAndDetailsList(List<T> specList) {
        specList.sort((Comparator<StoreGoodsSpecAndDetailsFullVO>) (o1, o2) -> {
            List<StoreGoodsSpecRelationVO> list1 = o1.getStoreGoodsSpecRelationList();
            List<StoreGoodsSpecRelationVO> list2 = o2.getStoreGoodsSpecRelationList();

            // 计算两个列表的最小长度
            int minSize = Math.min(list1.size(), list2.size());
            for (int i = 0; i < minSize; i++) {
                StoreGoodsSpecRelationVO r1 = list1.get(i);
                StoreGoodsSpecRelationVO r2 = list2.get(i);

                // 首先按照 goodsSpecDetailSort 正序排序
                int sortCompare = Integer.compare(r1.getGoodsSpecDetailSort(), r2.getGoodsSpecDetailSort());
                if (sortCompare != 0) {
                    return sortCompare;
                }

                // 如果 goodsSpecDetailSort 相等，则按照 goodsSpecDetailId 倒序排序
                int idCompare = -Integer.compare(r1.getGoodsSpecDetailId(), r2.getGoodsSpecDetailId());
                if (idCompare != 0) {
                    return idCompare;
                }
                // 如果两个字段都相等，则继续比较下一组数据
            }

            // 如果前 minSize 个元素均相同，则根据列表长度排序，较短的排在前面
            return Integer.compare(list1.size(), list2.size());
        });
    }

    /**
     * 查询销售方案商品对应的时段价格列表
     *
     * @param storeSalesProgramPO 店铺销售方案
     * @param storeGoodsIdList    店铺商品ID列表
     * @return 销售方案商品对应的时段价格列表
     */
    private Map<Integer, List<StoreSalesProgramGoodsPricePO>> getStoreSalesProgramGoodsIdAndPricePOListMap(
            StoreSalesProgramPO storeSalesProgramPO, List<Integer> storeGoodsIdList) {
        return storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, storeSalesProgramPO.getId())
                .in(StoreSalesProgramGoodsPricePO::getGoodsId, storeGoodsIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId));
    }

    /**
     * 根据销售分组的ID过滤商品的ID
     *
     * @param queryStoreGoodsAvailableForSalePosListDTO 查询可售商品列表DTO
     * @param storeGoodsIdList                          可售商品ID列表
     * @return 过滤后的商品ID列表
     */
    private List<Integer> filterStoreGoodsBySaleCategoryId(
            QueryStoreGoodsAvailableForSalePosListDTO queryStoreGoodsAvailableForSalePosListDTO,
            List<Integer> storeGoodsIdList) {
        if (queryStoreGoodsAvailableForSalePosListDTO.getSaleCategoryId() != null) {
            List<Integer> saleCategoryIdList = new ArrayList<>(List.of(queryStoreGoodsAvailableForSalePosListDTO.getSaleCategoryId()));
            // 需要查询连带查询销售分组的子级分组关联的商品
            saleCategoryIdList.addAll(saleCategoryMapperService.lambdaQuery()
                    .select(SaleCategoryPO::getId)
                    .eq(SaleCategoryPO::getParentId, queryStoreGoodsAvailableForSalePosListDTO.getSaleCategoryId())
                    .list().stream().map(SaleCategoryPO::getId).distinct().toList());
            // 根据销售分组ID过滤商品
            Set<Integer> needFilterGoodsIdSet = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                    .select(SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                    .in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, saleCategoryIdList)
                    .list().stream().map(SaleCategoryAndStoreGoodsPO::getStoreGoodsId).collect(Collectors.toSet());
            storeGoodsIdList = storeGoodsIdList.stream().filter(needFilterGoodsIdSet::contains).toList();
        }
        return storeGoodsIdList;
    }

    /**
     * 获取商品某个时段的序号
     * 用于获取商品的时段价格，因为在列表出需要展示一个价格，估只能获取某一个时段的价格来展示
     * 规则如下：
     * 1. 如果是生效中的方案对应的商品，则价格需要获取生效时段对应的价格
     * 2. 如果是未生效的方案对应的商品，则价格需要获取方案中第一个时段对应的价格
     *
     * @param storeSalesProgramPO 店铺销售方案
     * @return 商品某个时段的序号
     */
    private Integer getSerialNumberForUnitPriceDisplay(StoreSalesProgramPO storeSalesProgramPO) {
        if (Boolean.TRUE.equals(storeSalesProgramPO.getIsDefault())) {
            // 默认销售方案，时段只有一个00:00-23:59，直接返回第一个时段的序号
            return 1;
        }
        LocalTime currentLocalTime = DateUtils.getCurrentLocalTime();
        for (StoreSalesProgramPO.Section section : storeSalesProgramPO.getTimeSection()) {
            // 当前时间在开始时间和结束时间之间 或者 等于开始时间、结束时间
            if ((currentLocalTime.isAfter(section.getStart()) && currentLocalTime.isBefore(section.getEnd())) ||
                    currentLocalTime.equals(section.getStart()) ||
                    currentLocalTime.equals(section.getEnd())) {
                // 如果当前时段在开始时间和结束时间之间 或者 等于开始时间、结束时间，则返回当前时段的序号
                return section.getSerialNumber();
            }
        }
        // 如果当前时段不在任何时段之间，则返回第一个时段的序号
        return 1;
    }

    /**
     * 查询可售商品ID列表
     *
     * @param queryDTO 查询可售商品列表DTO
     * @return 可售商品ID列表
     */
    private Set<Integer> getAvailableGoodsIdSet(QueryStoreGoodsAvailableForSaleMemberListDTO queryDTO) {
        // 获取所有店铺销售方案ID
        List<Integer> allStoreSalesProgramIdList = getStoreSalesProgramIdList(queryDTO);

        if (CollectionUtils.isEmpty(allStoreSalesProgramIdList)) {
            return Collections.emptySet();
        }

        // 获取所有销售方案对应的可售商品ID
        Set<Integer> allAvailableGoodsIdSet = getAvailableGoodsIdFromSalesProgram(allStoreSalesProgramIdList);

        // 根据销售分组ID 过滤商品
        if (CollectionUtils.isNotEmpty(queryDTO.getCategory())) {
            return filterGoodsByCategory(allAvailableGoodsIdSet, queryDTO.getCategory());
        }

        return allAvailableGoodsIdSet;
    }

    /**
     * 根据Id、店铺ID、渠道ID查询所有的销售方案ID列表
     *
     * @param queryDTO 查询可售商品列表DTO
     * @return 所有的销售方案ID列表
     */
    private List<Integer> getStoreSalesProgramIdList(QueryStoreGoodsAvailableForSaleMemberListDTO queryDTO) {
        return storeSalesProgramMapperService.lambdaQuery()
                .select(StoreSalesProgramPO::getId)
                .in(StoreSalesProgramPO::getStoreId, queryDTO.getStoreIdList())
                .in(StoreSalesProgramPO::getChannelId, queryDTO.getChannelIdList())
                .list().stream().map(StoreSalesProgramPO::getId).toList();
    }

    /**
     * 根据销售方案ID列表获取所有可售商品ID
     *
     * @param storeSalesProgramIds 销售方案ID列表
     * @return 可售商品ID列表
     */
    private Set<Integer> getAvailableGoodsIdFromSalesProgram(List<Integer> storeSalesProgramIds) {
        return storeSalesProgramGoodsMapperService.lambdaQuery()
                .select(StoreSalesProgramGoodsPO::getGoodsId)
                .in(StoreSalesProgramGoodsPO::getStoreSalesProgramId, storeSalesProgramIds)
                .list().stream().map(StoreSalesProgramGoodsPO::getGoodsId).collect(Collectors.toSet());
    }

    /**
     * 根据销售分组ID过滤商品ID
     *
     * @param availableGoodsIdSet 可售商品ID集合
     * @param categoryIdList      销售分组ID列表
     * @return 过滤后的商品ID集合
     */
    private Set<Integer> filterGoodsByCategory(Set<Integer> availableGoodsIdSet, List<Integer> categoryIdList) {
        List<Integer> allStoreGoodsIdList = commonService.queryStoreGoodsIdListBySaleCategoryIdListAndChild(categoryIdList);
        // 取并集
        return availableGoodsIdSet.stream().filter(allStoreGoodsIdList::contains).collect(Collectors.toSet());
    }

    /**
     * 查询可售商品列表
     *
     * @param queryDTO            查询可售商品列表DTO
     * @param availableGoodsIdSet 可售商品ID集合
     * @return 可售商品列表
     */
    private List<StoreGoodsPO> queryStoreGoodsList(QueryStoreGoodsAvailableForSaleMemberListDTO queryDTO, Set<Integer> availableGoodsIdSet) {
        return new ArrayList<>(new HashSet<>(storeGoodsMapperService.lambdaQuery()
                .select(StoreGoodsPO::getId, StoreGoodsPO::getGoodsName, StoreGoodsPO::getComboType, StoreGoodsPO::getSpuCode)
                .in(StoreGoodsPO::getId, availableGoodsIdSet)
                .in(CollectionUtils.isNotEmpty(queryDTO.getComboType()), StoreGoodsPO::getComboType, queryDTO.getComboType())
                .in(CollectionUtils.isNotEmpty(queryDTO.getSpuCodeList()), StoreGoodsPO::getSpuCode, queryDTO.getSpuCodeList())
                .and(StringUtils.isNotBlank(queryDTO.getKeywords()), storeGoods -> storeGoods
                        .like(StoreGoodsPO::getGoodsName, queryDTO.getKeywords())
                        .or()
                        .like(StoreGoodsPO::getSpuCode, queryDTO.getKeywords()))
                .list()));
    }

    /**
     * 构建分页响应
     *
     * @param queryDTO       查询可售商品列表DTO
     * @param storeGoodsList 可售商品列表
     * @return 分页响应
     */
    private PageRespVO<StoreGoodsBaseVO> buildPagedResponse(QueryStoreGoodsAvailableForSaleMemberListDTO queryDTO,
                                                            List<StoreGoodsPO> storeGoodsList) {
        int size = storeGoodsList.size();
        int startIndex = (queryDTO.getPage() - 1) * queryDTO.getLimit();
        int endIndex = Math.min(startIndex + queryDTO.getLimit(), size);

        // 如果起始索引大于总数据量，返回空列表
        if (startIndex > size) {
            return new PageRespVO<>(Collections.emptyList(), size, queryDTO.getLimit(), queryDTO.getPage());
        }

        // 截取分页数据
        List<StoreGoodsBaseVO> pagedList = storeGoodsList.subList(startIndex, endIndex).stream()
                .map(item -> {
                    StoreGoodsBaseVO storeGoodsBaseVO = storeGoodsConverter.toStoreGoodsBaseVO(item);
                    storeGoodsBaseVO.setSellingPrice(String.valueOf(item.getSellingPrice()));
                    return storeGoodsBaseVO;
                })
                .toList();

        return new PageRespVO<>(pagedList, size, queryDTO.getLimit(), queryDTO.getPage());
    }


    /**
     * 校验查询可售商品列表DTO参数
     *
     * @param queryStoreGoodsAvailableForSaleMemberListDTO 查询可售商品列表DTO
     */
    private void checkQueryStoreGoodsAvailableForSaleListDTO(QueryStoreGoodsAvailableForSaleMemberListDTO queryStoreGoodsAvailableForSaleMemberListDTO) {
        if (CollectionUtils.isEmpty(queryStoreGoodsAvailableForSaleMemberListDTO.getComboType())) {
            return;
        }
        Set<Integer> allComboTypeCodeList = Arrays.stream(StoreComboType.values()).map(StoreComboType::getId).collect(Collectors.toSet());
        if (!allComboTypeCodeList.containsAll(queryStoreGoodsAvailableForSaleMemberListDTO.getComboType())) {
            log.error("查询可售商品列表DTO参数错误，comboType参数值不合法，合法值：{}，传入值：{}",
                    allComboTypeCodeList, queryStoreGoodsAvailableForSaleMemberListDTO.getComboType());
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INVALID_PARAMETER,
                    "查询可售商品列表DTO参数错误，comboType参数值不合法");
        }
    }

    /**
     * 获取在售商品列表
     *
     * @param verifyStoreGoodsOnSaleDTO 校验在售商品信息DTO
     * @return 在售商品列表
     */
    private List<StoreGoodsOnSaleExtendVO> getStoreGoodsOnSaleList(VerifyStoreGoodsOnSaleDTO verifyStoreGoodsOnSaleDTO) {
        return commonService.queryStoreGoodsBaseListForOnSale(
                QueryStoreGoodsBaseListForOnSaleBO.builder()
                        .storeId(verifyStoreGoodsOnSaleDTO.getStoreId())
                        .channelId(verifyStoreGoodsOnSaleDTO.getChannelId())
                        .storeGoodsIdList(verifyStoreGoodsOnSaleDTO.getGoodsList().stream()
                                .map(VerifyStoreGoodsOnSaleDTO.GoodsInfo::getGoodsId).distinct()
                                .toList())
                        .page(1)
                        .limit(Integer.MAX_VALUE)
                        .storeGoodsExpandList(List.of(StoreGoodsExpand.COVER, StoreGoodsExpand.UNIT, StoreGoodsExpand.LABEL))
                        .build()).getList();
    }

    /**
     * 处理商品变更提醒, 用于所有商品已下架
     *
     * @param verifyStoreGoodsOnSaleDTO 校验在售商品信息DTO
     * @param verifyStoreGoodsOnSaleVO  校验在售商品信息VO
     */
    private void handleGoodsChangeReminderForListingStatus(VerifyStoreGoodsOnSaleDTO verifyStoreGoodsOnSaleDTO,
                                                           VerifyStoreGoodsOnSaleVO verifyStoreGoodsOnSaleVO) {
        List<VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo> goodsChangeReminder = new ArrayList<>();
        VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo goodsChangeReminderInfo = new VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo();
        goodsChangeReminderInfo.setChangeType(StoreGoodsChangeReminderType.LISTING_STATUS.getValue());
        goodsChangeReminderInfo.setGoodsList(storeGoodsConverter.toStoreGoodsBaseInfoList(verifyStoreGoodsOnSaleDTO.getGoodsList()));
        goodsChangeReminder.add(goodsChangeReminderInfo);
        verifyStoreGoodsOnSaleVO.setGoodsChangeReminder(goodsChangeReminder);
    }

    /**
     * 处理在售商品信息, 处理存在下架商品的情况
     *
     * @param verifyStoreGoodsOnSaleDTO    校验在售商品信息DTO
     * @param storeGoodsOnSaleExtendVOList 在售商品列表
     * @param verifyStoreGoodsOnSaleVO     校验在售商品信息VO
     */
    private void handleGoodsOnSaleInfo(VerifyStoreGoodsOnSaleDTO verifyStoreGoodsOnSaleDTO,
                                       List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList,
                                       VerifyStoreGoodsOnSaleVO verifyStoreGoodsOnSaleVO) {
        // 组装店铺商品SkuCode和需要校验的商品信息的映射
        Map<String, VerifyStoreGoodsOnSaleDTO.GoodsInfo> skuCodeAndGoodsInfoMap =
                assembleSkuCodeAndGoodsInfoMap(verifyStoreGoodsOnSaleDTO);
        // 克隆一份商品SkuCode列表，用于校验商品价格和是否下架
        List<String> allNeedVerifySkuCodeList = new ArrayList<>(skuCodeAndGoodsInfoMap.keySet());
        // 组装店铺商品SkuCode和在售商品信息的映射
        Map<String, StoreGoodsOnSaleExtendVO> skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap =
                assembleSkuCodeAndGoodsStoreGoodsOnSaleExtendVOMap(storeGoodsOnSaleExtendVOList);
        // 克隆一份在售商品SkuCode列表，用于校验商品价格和是否下架
        List<String> allOnSaleSkuCodeList = new ArrayList<>(skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap.keySet());
        // 克隆一份需要校验的商品SkuCode列表，用于校验商品是否下架
        List<String> allNeedVerifySkuCodeListForCheckListingStatus = new ArrayList<>(allNeedVerifySkuCodeList);
        // 需要校验的商品SkuCode列表中移除在售商品ID列表，剩下的就是已经下架的商品了
        allNeedVerifySkuCodeListForCheckListingStatus.removeAll(allOnSaleSkuCodeList);
        // 如果有下架商品，处理下架提醒
        if (CollectionUtils.isNotEmpty(allNeedVerifySkuCodeListForCheckListingStatus)) {
            handleGoodsChangeReminderForListingStatus(allNeedVerifySkuCodeListForCheckListingStatus,
                    skuCodeAndGoodsInfoMap, verifyStoreGoodsOnSaleVO);
            // 过滤下架商品，剩下的就是需要校验销售价格的商品
            allNeedVerifySkuCodeList.removeAll(allNeedVerifySkuCodeListForCheckListingStatus);
        }
        // 如果有需要校验价格的商品，处理销售价格校验
        if (CollectionUtils.isNotEmpty(allNeedVerifySkuCodeList)) {
            // 处理销售价格校验
            handleSellingPriceVerification(allNeedVerifySkuCodeList, skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap,
                    skuCodeAndGoodsInfoMap, verifyStoreGoodsOnSaleVO);
        }
    }

    /**
     * 组装店铺商品Sku和需要校验的商品信息的映射
     *
     * @param verifyStoreGoodsOnSaleDTO 校验在售商品信息DTO
     * @return 店铺商品Sku和需要校验的商品信息的映射
     */
    private Map<String, VerifyStoreGoodsOnSaleDTO.GoodsInfo> assembleSkuCodeAndGoodsInfoMap(
            VerifyStoreGoodsOnSaleDTO verifyStoreGoodsOnSaleDTO) {
        return verifyStoreGoodsOnSaleDTO.getGoodsList().stream()
                .collect(toMap(VerifyStoreGoodsOnSaleDTO.GoodsInfo::getSkuCode, Function.identity()));
    }

    /**
     * 组装店铺商品SKU和在售商品信息的映射
     *
     * @param storeGoodsOnSaleExtendVOList 在售商品列表
     * @return 店铺商品SKU和在售商品信息的映射
     */
    private Map<String, StoreGoodsOnSaleExtendVO> assembleSkuCodeAndGoodsStoreGoodsOnSaleExtendVOMap(
            List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList) {
        Map<String, StoreGoodsOnSaleExtendVO> skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap = new HashMap<>();
        storeGoodsOnSaleExtendVOList.forEach(storeGoodsOnSaleExtendVO ->
                storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList().forEach(storeGoodsSpecDetail ->
                        skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap.put(storeGoodsSpecDetail.getSkuCode(), storeGoodsOnSaleExtendVO)));
        return skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap;
    }

    /**
     * 处理商品变更提醒, 用于存在下架商品的情况
     *
     * @param removedSkuCodeList       已下架商品Sku列表
     * @param skuCodeAndGoodsInfoMap   店铺商品Sku和需要校验的商品信息的映射
     * @param verifyStoreGoodsOnSaleVO 校验在售商品信息VO
     */
    private void handleGoodsChangeReminderForListingStatus(List<String> removedSkuCodeList,
                                                           Map<String, VerifyStoreGoodsOnSaleDTO.GoodsInfo> skuCodeAndGoodsInfoMap,
                                                           VerifyStoreGoodsOnSaleVO verifyStoreGoodsOnSaleVO) {
        List<VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo> goodsChangeReminder = new ArrayList<>();
        VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo reminderInfo = new VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo();
        reminderInfo.setChangeType(StoreGoodsChangeReminderType.LISTING_STATUS.getValue());
        reminderInfo.setGoodsList(storeGoodsConverter.toStoreGoodsBaseInfoList(
                removedSkuCodeList.stream().map(skuCodeAndGoodsInfoMap::get).toList()));
        goodsChangeReminder.add(reminderInfo);
        verifyStoreGoodsOnSaleVO.setGoodsChangeReminder(goodsChangeReminder);
    }

    /**
     * 处理销售价格校验
     *
     * @param allNeedVerifySkuCodeList                   需要校验的商品skuCode列表
     * @param skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap 店铺商品skuCode和在售商品信息的映射
     * @param skuCodeAndGoodsInfoMap                     店铺商品skuCode和需要校验的商品信息的映射
     * @param verifyStoreGoodsOnSaleVO                   校验在售商品信息VO
     */
    private void handleSellingPriceVerification(List<String> allNeedVerifySkuCodeList,
                                                Map<String, StoreGoodsOnSaleExtendVO> skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap,
                                                Map<String, VerifyStoreGoodsOnSaleDTO.GoodsInfo> skuCodeAndGoodsInfoMap,
                                                VerifyStoreGoodsOnSaleVO verifyStoreGoodsOnSaleVO) {
        List<VerifyStoreGoodsOnSaleVO.StoreGoodsExtendInfo> storeGoodsExtendInfoList = new ArrayList<>();
        VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo sellingPriceReminderInfo = new VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo();
        sellingPriceReminderInfo.setChangeType(StoreGoodsChangeReminderType.SELLING_PRICE.getValue());
        sellingPriceReminderInfo.setGoodsList(new ArrayList<>());
        for (String needVerifySkuCode : allNeedVerifySkuCodeList) {
            VerifyStoreGoodsOnSaleVO.StoreGoodsExtendInfo storeGoodsExtendInfo = toStoreGoodsExtendInfo(
                    skuCodeAndGoodsStoreGoodsOnSaleExtendVOMap.get(needVerifySkuCode), skuCodeAndGoodsInfoMap.get(needVerifySkuCode), needVerifySkuCode);
            if (storeGoodsExtendInfo == null) {
                continue;
            }
            storeGoodsExtendInfoList.add(storeGoodsExtendInfo);
            VerifyStoreGoodsOnSaleDTO.GoodsInfo storeGoodsInfoForNeedVerify = skuCodeAndGoodsInfoMap.get(needVerifySkuCode);
            // 销售价格校验
            if (storeGoodsInfoForNeedVerify.getSellingPrice().compareTo(new BigDecimal(storeGoodsExtendInfo.getSellingPrice())) != 0) {
                sellingPriceReminderInfo.getGoodsList().add(storeGoodsConverter.toStoreGoodsBaseInfo(storeGoodsInfoForNeedVerify));
            }
        }
        // 如果有销售价格变更的商品，添加到变更提醒中
        if (CollectionUtils.isNotEmpty(sellingPriceReminderInfo.getGoodsList())) {
            List<VerifyStoreGoodsOnSaleVO.GoodsChangeReminderInfo> goodsChangeReminder = verifyStoreGoodsOnSaleVO.getGoodsChangeReminder();
            if (CollectionUtils.isEmpty(goodsChangeReminder)) {
                goodsChangeReminder = new ArrayList<>();
            }
            goodsChangeReminder.add(sellingPriceReminderInfo);
            verifyStoreGoodsOnSaleVO.setGoodsChangeReminder(goodsChangeReminder);
        }
        // 设置在售商品信息
        verifyStoreGoodsOnSaleVO.setGoodsList(storeGoodsExtendInfoList);
    }

    /**
     * 组装在售商品信息
     *
     * @param storeGoodsOnSaleExtendVO 在售商品扩展信息
     * @param goodsInfo                需要校验的商品信息
     * @param skuCode                  商品skuCode
     * @return 在售商品信息
     */
    private VerifyStoreGoodsOnSaleVO.StoreGoodsExtendInfo toStoreGoodsExtendInfo(StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                                                                 VerifyStoreGoodsOnSaleDTO.GoodsInfo goodsInfo, String skuCode) {
        if (storeGoodsOnSaleExtendVO == null) {
            return null;
        }
        VerifyStoreGoodsOnSaleVO.StoreGoodsExtendInfo storeGoodsExtendInfo = new VerifyStoreGoodsOnSaleVO.StoreGoodsExtendInfo();
        List<StoreGoodsSpecVO> storeGoodsSpecList = storeGoodsOnSaleExtendVO.getStoreGoodsSpecList();
        Map<Integer, GoodsPictureVO> goodsSpecDetailIdAndImageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(storeGoodsSpecList)) {
            goodsSpecDetailIdAndImageMap.putAll(storeGoodsSpecList.stream()
                    .map(StoreGoodsSpecVO::getStoreGoodsSpecDetailList)
                    .flatMap(Collection::stream)
                    .filter(storeGoodsSpecDetailVO -> storeGoodsSpecDetailVO.getImgId() != null)
                    .collect(toMap(StoreGoodsSpecDetailVO::getId, StoreGoodsSpecDetailVO::getImage)));
        }
        List<StoreGoodsSpecAndDetailsVO> storeGoodsSpecDetailsList = storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList();
        AtomicBoolean hasSpecPicture = new AtomicBoolean(false);
        storeGoodsSpecDetailsList.forEach(storeGoodsSpecAndDetailsVO -> {
            if (!storeGoodsSpecAndDetailsVO.getSkuCode().equals(skuCode)) {
                return;
            }
            storeGoodsExtendInfo.setSellingPrice(storeGoodsSpecAndDetailsVO.getGoodsSpecSellingPrice());
            storeGoodsExtendInfo.setSkuCode(storeGoodsSpecAndDetailsVO.getSkuCode());
            storeGoodsExtendInfo.setBarcode(storeGoodsSpecAndDetailsVO.getBarcode());
            storeGoodsExtendInfo.setGoodsUnit(storeGoodsSpecAndDetailsVO.getGoodsUnit());
            storeGoodsExtendInfo.setGoodsUnitName(storeGoodsSpecAndDetailsVO.getGoodsUnitName());
            setStoreGoodsPicture(storeGoodsSpecAndDetailsVO, goodsSpecDetailIdAndImageMap, storeGoodsExtendInfo, hasSpecPicture);
            if (CollectionUtils.isEmpty(goodsInfo.getGoodsSpecRelationList())) {
                return;
            }
            List<VerifyStoreGoodsOnSaleVO.GoodsSpecRelation> goodsSpecRelationList = new ArrayList<>();
            goodsInfo.getGoodsSpecRelationList().forEach(goodsSpecRelationItem -> {
                VerifyStoreGoodsOnSaleVO.GoodsSpecRelation goodsSpecRelation = new VerifyStoreGoodsOnSaleVO.GoodsSpecRelation();
                goodsSpecRelation.setGoodsSpecId(goodsSpecRelationItem.getGoodsSpecId());
                goodsSpecRelation.setGoodsSpecDetailId(goodsSpecRelationItem.getGoodsSpecDetailId());
                goodsSpecRelation.setGoodsSpecName(goodsSpecRelationItem.getGoodsSpecName());
                goodsSpecRelation.setGoodsSpecSort(goodsSpecRelationItem.getGoodsSpecSort());
                goodsSpecRelation.setGoodsSpecDetailName(goodsSpecRelationItem.getGoodsSpecDetailName());
                goodsSpecRelation.setGoodsSpecDetailSort(goodsSpecRelationItem.getGoodsSpecDetailSort());
                goodsSpecRelationList.add(goodsSpecRelation);
            });
            storeGoodsExtendInfo.setGoodsSpecRelationList(goodsSpecRelationList);
        });

        storeGoodsExtendInfo.setComboTypeName(converterCombinationType(storeGoodsOnSaleExtendVO.getComboType()));
        storeGoodsExtendInfo.setGoodsId(storeGoodsOnSaleExtendVO.getId());
        storeGoodsExtendInfo.setGoodsName(storeGoodsOnSaleExtendVO.getGoodsName());

        storeGoodsExtendInfo.setStoreSalesProgramId(storeGoodsOnSaleExtendVO.getStoreSalesProgramId());
        if (!hasSpecPicture.get()) {
            List<GoodsPictureVO> list = storeGoodsOnSaleExtendVO.getPicture();
            if (list != null) {
                storeGoodsExtendInfo.setPicture(new ArrayList<>(list));
            }
        }
        storeGoodsExtendInfo.setGoodsCustomCode(storeGoodsOnSaleExtendVO.getGoodsCustomCode());
        storeGoodsExtendInfo.setValuationMethod(storeGoodsOnSaleExtendVO.getValuationMethod());
        storeGoodsExtendInfo.setComboType(storeGoodsOnSaleExtendVO.getComboType());
        storeGoodsExtendInfo.setSpuCode(storeGoodsOnSaleExtendVO.getSpuCode());
        storeGoodsExtendInfo.setTagName(storeGoodsOnSaleExtendVO.getTagName());

        return storeGoodsExtendInfo;
    }

    /**
     * 设置商品的封面图片
     *
     * @param storeGoodsSpecAndDetailsVO   商品规格信息
     * @param goodsSpecDetailIdAndImageMap 商品规格详情ID和图片的映射
     * @param storeGoodsExtendInfo         商品扩展信息
     * @param hasSpecPicture               是否有规格图片
     */
    private static void setStoreGoodsPicture(StoreGoodsSpecAndDetailsVO storeGoodsSpecAndDetailsVO,
                                             Map<Integer, GoodsPictureVO> goodsSpecDetailIdAndImageMap,
                                             VerifyStoreGoodsOnSaleVO.StoreGoodsExtendInfo storeGoodsExtendInfo,
                                             AtomicBoolean hasSpecPicture) {
        if (CollectionUtils.isEmpty(storeGoodsSpecAndDetailsVO.getStoreGoodsSpecRelationList())) {
            return;
        }
        storeGoodsSpecAndDetailsVO.getStoreGoodsSpecRelationList().forEach(storeGoodsSpecRelationVO -> {
            if (goodsSpecDetailIdAndImageMap.containsKey(storeGoodsSpecRelationVO.getGoodsSpecDetailId())) {
                storeGoodsExtendInfo.setPicture(List.of(goodsSpecDetailIdAndImageMap.get(storeGoodsSpecRelationVO.getGoodsSpecDetailId())));
                hasSpecPicture.set(true);
            }
        });
    }

    /**
     * 组装组合类型
     *
     * @param comboTypeCode 组合类型编码
     * @return 组合类型名称
     */
    private String converterCombinationType(Integer comboTypeCode) {
        return StoreComboType.getNameFromCode(comboTypeCode);
    }

    /**
     * 检查查询在售商品详情DTO
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询在售商品详情DTO
     */
    private void checkQueryStoreGoodsOnSaleDetailDTO(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO) {
        if (Objects.isNull(queryStoreGoodsOnSaleDetailDTO.getStoreGoodsId()) &&
                Objects.isNull(queryStoreGoodsOnSaleDetailDTO.getBarcode()) &&
                Objects.isNull(queryStoreGoodsOnSaleDetailDTO.getGoodsCustomCode()) &&
                Objects.isNull(queryStoreGoodsOnSaleDetailDTO.getScaleCustomCode())) {
            log.error("查询在售商品详情失败，店铺商品ID、条码、自编码、秤内自编码不能同时为空");
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_BAD_REQUEST);
        }
        if (Objects.nonNull(queryStoreGoodsOnSaleDetailDTO.getScaleCustomCode()) &&
                Objects.isNull(queryStoreGoodsOnSaleDetailDTO.getScaleType())) {
            log.error("查询在售商品详情失败，秤内自编码不为空时，秤类型不能为空");
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_BAD_REQUEST);
        }
    }

    /**
     * 组装在售商品详情VO
     *
     * @param queryStoreGoodsOnSaleDetailDTO    查询在售商品详情DTO
     * @param storeGoodsPO                      店铺商品信息
     * @param storeSalesProgramGoodsPOList      销售方案商品信息
     * @param storeSalesProgramGoodsPricePOList 销售方案商品价格信息
     * @return 在售商品详情VO
     */
    private StoreGoodsOnSaleExtendVO assembleStoreGoodsOnSaleExtendVO(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO,
                                                                      StoreGoodsPO storeGoodsPO,
                                                                      List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList,
                                                                      List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList) {
        StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO = storeGoodsConverter.toStoreGoodsOnSaleExtendVO(storeGoodsPO);
        StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramGoodsPOList.get(0);
        // 组装封面图片
        if (!Objects.isNull(storeSalesProgramGoodsPO.getCoverPicture())) {
            storeGoodsOnSaleExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
            storeGoodsOnSaleExtendVO.setPicture(List.of(goodsPictureConverter.toGoodsPictureVO(
                    uploadFilesMapperService.getById(storeSalesProgramGoodsPO.getCoverPicture()))));
        }
        // 组装标签信息
        if (!Objects.isNull(storeSalesProgramGoodsPO.getTag())) {
            storeGoodsOnSaleExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
            storeGoodsOnSaleExtendVO.setLabelInfo(goodsLabelConverter.toGoodsLabelVO(goodsLabelMapperService.getById(storeSalesProgramGoodsPO.getTag())));
        }
        // 组装品牌信息
        if (!Objects.isNull(storeGoodsOnSaleExtendVO.getBrand())) {
            List<GoodsBrandExtendPO> goodsBrandExtendPOList =
                    goodsBrandMapperService.queryGoodsBrandExtendListByIdList(List.of(storeGoodsOnSaleExtendVO.getBrand()));
            if (CollectionUtils.isNotEmpty(goodsBrandExtendPOList)) {
                storeGoodsOnSaleExtendVO.setBrandInfo(goodsBrandConverter.toGoodsBrandVO(goodsBrandExtendPOList.get(0)));
            }
        }
        // 组装销售分组信息
        List<Integer> saleCategoryIdList = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getSaleCategoryId)
                .eq(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, storeGoodsOnSaleExtendVO.getId())
                .list().stream()
                .map(SaleCategoryAndStoreGoodsPO::getSaleCategoryId)
                .distinct()
                .toList();
        if (CollectionUtils.isNotEmpty(saleCategoryIdList)) {
            storeGoodsOnSaleExtendVO.setCategoryList(saleCategoryConverter.toStoreGoodsOnSaleExtendVOSaleCategoryVOList(
                    saleCategoryMapperService.lambdaQuery()
                            .eq(SaleCategoryPO::getChannelId, queryStoreGoodsOnSaleDetailDTO.getChannelId())
                            .in(SaleCategoryPO::getId, saleCategoryIdList).list()));
        }
        storeGoodsOnSaleExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
        // 组装规格信息
        assembleSpecAndSpecDetails(queryStoreGoodsOnSaleDetailDTO, storeSalesProgramGoodsPOList,
                storeSalesProgramGoodsPricePOList, storeGoodsOnSaleExtendVO);
        // 查询秤内自编码, 加个注视
        GoodsScaleCodePO goodsScaleCodePO = goodsScaleCodeMapperService.findByStoreGoodsId(storeGoodsPO.getId());
        if (Objects.nonNull(goodsScaleCodePO)) {
            storeGoodsOnSaleExtendVO.setScaleCustomCode(goodsScaleCodePO.getScaleCustomCode());
        }
        return storeGoodsOnSaleExtendVO;
    }

    /**
     * 组装规格信息
     *
     * @param queryStoreGoodsOnSaleDetailDTO    查询在售商品详情DTO
     * @param storeSalesProgramGoodsPOList      销售方案商品信息
     * @param storeSalesProgramGoodsPricePOList 销售方案商品价格信息
     * @param storeGoodsOnSaleExtendVO          在售商品详情VO
     */
    private void assembleSpecAndSpecDetails(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO,
                                            List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList,
                                            List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList,
                                            StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO) {
        Map<Integer, BigDecimal> storeSalesProgramGoodsIdAndPriceMap = storeSalesProgramGoodsPricePOList.stream()
                .collect(toMap(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, StoreSalesProgramGoodsPricePO::getTimeSectionPriceCompute));
        List<Integer> skuIdList = new ArrayList<>();
        Map<Integer, String> skuIdAndPriceStringMap = new HashMap<>();
        storeSalesProgramGoodsPOList.forEach(storeSalesProgramGoodsPOItem -> {
            skuIdList.add(storeSalesProgramGoodsPOItem.getGoodsPackageSkuId());
            if (storeSalesProgramGoodsIdAndPriceMap.containsKey(storeSalesProgramGoodsPOItem.getId())) {
                skuIdAndPriceStringMap.put(storeSalesProgramGoodsPOItem.getGoodsPackageSkuId(),
                        storeSalesProgramGoodsIdAndPriceMap.get(storeSalesProgramGoodsPOItem.getId()).setScale(2, RoundingMode.HALF_UP).toString());
            }
        });

        List<GoodsPackageSkuPO> goodsPackageSkuPOList = goodsPackageSkuMapperService.lambdaQuery()
                .select(GoodsPackageSkuPO::getId, GoodsPackageSkuPO::getStoreGoodsId, GoodsPackageSkuPO::getSkuCode,
                        GoodsPackageSkuPO::getCosts, GoodsPackageSkuPO::getGoodsUnit, GoodsPackageSkuPO::getBarcode)
                .in(GoodsPackageSkuPO::getId, skuIdList)
                .list();
        Map<Integer, GoodsPackageSkuPO> skuIdAndInfoMap = new HashMap<>();
        for (GoodsPackageSkuPO goodsPackageSkuPO : goodsPackageSkuPOList) {
            skuIdAndInfoMap.put(goodsPackageSkuPO.getId(), goodsPackageSkuPO);
        }

        if (storeGoodsOnSaleExtendVO.getGoodsSpec() == 1) {
            // 单规格
            handleSingleSpec(storeGoodsOnSaleExtendVO, skuIdAndInfoMap, skuIdAndPriceStringMap);
        } else {
            // 多规格
            handleMultipleSpec(queryStoreGoodsOnSaleDetailDTO, storeGoodsOnSaleExtendVO, skuIdList, skuIdAndInfoMap, skuIdAndPriceStringMap);
        }
    }

    /**
     * 处理多规格信息
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询在售商品详情DTO
     * @param storeGoodsOnSaleExtendVO       在售商品详情VO
     * @param skuIdList                      商品SKU ID列表
     * @param skuIdAndInfoMap                商品SKU ID和信息映射
     * @param skuIdAndPriceStringMap         商品SKU ID和价格字符串映射
     */
    private void handleMultipleSpec(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO,
                                    StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                    List<Integer> skuIdList,
                                    Map<Integer, GoodsPackageSkuPO> skuIdAndInfoMap,
                                    Map<Integer, String> skuIdAndPriceStringMap) {
        Map<Integer, SpecPO> specIdAndSpecInfoMap = specMapperService.lambdaQuery()
                .select(SpecPO::getId, SpecPO::getName, SpecPO::getSort)
                .eq(SpecPO::getStoreId, queryStoreGoodsOnSaleDetailDTO.getStoreId())
                .list().stream().collect(toMap(SpecPO::getId, Function.identity()));
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap = specDetailMapperService.lambdaQuery()
                .select(SpecDetailPO::getId, SpecDetailPO::getName, SpecDetailPO::getSort, SpecDetailPO::getSpecId)
                .in(SpecDetailPO::getSpecId, specIdAndSpecInfoMap.keySet())
                .list().stream().collect(toMap(SpecDetailPO::getId, Function.identity()));
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = skuSpecDetailsMapperService.lambdaQuery()
                .select(SkuSpecDetailsPO::getGoodsSpecDetailId, SkuSpecDetailsPO::getSkuId)
                .in(SkuSpecDetailsPO::getSkuId, skuIdList).list().stream().collect(groupingBy(SkuSpecDetailsPO::getSkuId, Collectors.mapping(SkuSpecDetailsPO::getGoodsSpecDetailId, toList())));
        Map<Integer, List<Integer>> allSkuIdAndGoodsSpecDetailIdListMap = new HashMap<>();
        Set<Integer> allGoodsSpecDetailIdSet = new HashSet<>();
        skuIdList.forEach(skuId -> {
            if (skuIdAndGoodsSpecDetailIdListMap.containsKey(skuId)) {
                allGoodsSpecDetailIdSet.addAll(skuIdAndGoodsSpecDetailIdListMap.get(skuId));
                allSkuIdAndGoodsSpecDetailIdListMap.computeIfAbsent(skuId, k -> new ArrayList<>()).addAll(skuIdAndGoodsSpecDetailIdListMap.get(skuId));
            } else {
                allSkuIdAndGoodsSpecDetailIdListMap.computeIfAbsent(skuId, k -> new ArrayList<>());
            }
        });
        List<Integer> goodsSpecIdList = new ArrayList<>();
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = new HashMap<>();
        Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap = new HashMap<>();
        List<GoodsSpecDetailPO> goodsSpecDetailPOList = goodsSpecDetailMapperService.lambdaQuery().in(GoodsSpecDetailPO::getId, allGoodsSpecDetailIdSet).list();
        goodsSpecDetailPOList.forEach(goodsSpecDetailPO -> {
            goodsSpecDetailIdAndInfoMap.put(goodsSpecDetailPO.getId(), goodsSpecDetailPO);
            goodsSpecIdList.add(goodsSpecDetailPO.getGoodsSpecId());
            goodsSpecIdAndGoodsSpecDetailInfoMap.computeIfAbsent(goodsSpecDetailPO.getGoodsSpecId(), k -> new ArrayList<>()).add(goodsSpecDetailPO);
        });
        List<GoodsSpecPO> goodsSpecPOList = goodsSpecMapperService.lambdaQuery()
                .in(GoodsSpecPO::getId, goodsSpecIdList)
                .list();
        Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap = new HashMap<>();
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = new HashMap<>();
        goodsSpecPOList.forEach(goodsSpecPO -> {
            storeGoodsIdAndGoodsSpecPOListMap.computeIfAbsent(goodsSpecPO.getStoreGoodsId(), k -> new ArrayList<>()).add(goodsSpecPO);
            goodsSpecIdAndInfoMap.put(goodsSpecPO.getId(), goodsSpecPO);
        });

        commonService.assembleStoreGoodsSpecList(storeGoodsOnSaleExtendVO, storeGoodsIdAndGoodsSpecPOListMap, goodsSpecIdAndGoodsSpecDetailInfoMap,
                specIdAndSpecInfoMap, specDetailIdAndInfoMap);

        List<StoreGoodsSpecAndDetailsVO> storeGoodsSpecDetailsList = new ArrayList<>();
        Set<Integer> unitIdSet = new HashSet<>();
        allSkuIdAndGoodsSpecDetailIdListMap.forEach((skuId, goodsSpecDetailIdList) -> {
            StoreGoodsSpecAndDetailsVO storeGoodsSpecAndDetailsVO = new StoreGoodsSpecAndDetailsVO();
            GoodsPackageSkuPO goodsPackageSkuPO = skuIdAndInfoMap.get(skuId);
            storeGoodsSpecAndDetailsVO.setSkuId(skuId);
            storeGoodsSpecAndDetailsVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
            storeGoodsSpecAndDetailsVO.setBarcode(goodsPackageSkuPO.getBarcode());
            if (Objects.nonNull(goodsPackageSkuPO.getGoodsUnit())) {
                storeGoodsSpecAndDetailsVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
                unitIdSet.add(goodsPackageSkuPO.getGoodsUnit());
            }
            storeGoodsSpecAndDetailsVO.setGoodsSpecSellingPrice(skuIdAndPriceStringMap.get(skuId));
            storeGoodsSpecAndDetailsVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
            if (CollectionUtils.isEmpty(goodsSpecDetailIdList)) {
                return;
            }
            List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList = new ArrayList<>();
            goodsSpecDetailIdList.forEach(goodsSpecDetailId -> {
                StoreGoodsSpecRelationVO storeGoodsSpecRelationVO = new StoreGoodsSpecRelationVO();
                GoodsSpecDetailPO goodsSpecDetailPO = goodsSpecDetailIdAndInfoMap.get(goodsSpecDetailId);
                storeGoodsSpecRelationVO.setGoodsSpecDetailId(goodsSpecDetailPO.getId());
                storeGoodsSpecRelationVO.setGoodsSpecDetailSort(goodsSpecDetailPO.getSort());
                storeGoodsSpecRelationVO.setGoodsSpecDetailName(specDetailIdAndInfoMap.get(goodsSpecDetailPO.getSpecDetailId()).getName());
                GoodsSpecPO goodsSpecPO = goodsSpecIdAndInfoMap.get(goodsSpecDetailPO.getGoodsSpecId());
                storeGoodsSpecRelationVO.setGoodsSpecId(goodsSpecPO.getId());
                storeGoodsSpecRelationVO.setGoodsSpecSort(goodsSpecPO.getSort());
                storeGoodsSpecRelationVO.setGoodsSpecName(specIdAndSpecInfoMap.get(goodsSpecPO.getSpecId()).getName());
                storeGoodsSpecRelationList.add(storeGoodsSpecRelationVO);
            });
            commonService.sortStoreGoodsSpecList(storeGoodsSpecRelationList);
            storeGoodsSpecAndDetailsVO.setStoreGoodsSpecRelationList(storeGoodsSpecRelationList);
            storeGoodsSpecDetailsList.add(storeGoodsSpecAndDetailsVO);
        });
        if (CollectionUtils.isNotEmpty(unitIdSet)) {
            Map<Integer, String> integerStringMap = assembleStoreGoodsUnitIdAndNameMap(unitIdSet);
            storeGoodsSpecDetailsList.forEach(storeGoodsSpecAndDetailsVO ->
                    storeGoodsSpecAndDetailsVO.setGoodsUnitName(integerStringMap.get(storeGoodsSpecAndDetailsVO.getGoodsUnit())));
        }
        commonService.sortStoreGoodsSpecAndDetailsList(storeGoodsSpecDetailsList);
        storeGoodsOnSaleExtendVO.setStoreGoodsSpecDetailsList(storeGoodsSpecDetailsList);
    }

    /**
     * 处理单规格信息
     *
     * @param storeGoodsOnSaleExtendVO 在售商品详情VO
     * @param skuIdAndInfoMap          商品SKU ID和信息映射
     * @param skuIdAndPriceStringMap   商品SKU ID和价格字符串映射
     */
    private void handleSingleSpec(StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                  Map<Integer, GoodsPackageSkuPO> skuIdAndInfoMap,
                                  Map<Integer, String> skuIdAndPriceStringMap) {
        List<StoreGoodsSpecAndDetailsVO> storeGoodsSpecDetailsList = new ArrayList<>();
        Set<Integer> unitIdSet = new HashSet<>();
        skuIdAndInfoMap.forEach((skuId, goodsPackageSkuPO) -> {
            StoreGoodsSpecAndDetailsVO storeGoodsSpecAndDetailsVO = new StoreGoodsSpecAndDetailsVO();
            storeGoodsSpecAndDetailsVO.setSkuId(skuId);
            storeGoodsSpecAndDetailsVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
            storeGoodsSpecAndDetailsVO.setBarcode(goodsPackageSkuPO.getBarcode());
            if (Objects.nonNull(goodsPackageSkuPO.getGoodsUnit())) {
                storeGoodsSpecAndDetailsVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
                unitIdSet.add(goodsPackageSkuPO.getGoodsUnit());
            }
            storeGoodsSpecAndDetailsVO.setGoodsSpecSellingPrice(skuIdAndPriceStringMap.get(skuId));
            storeGoodsSpecAndDetailsVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
            storeGoodsSpecDetailsList.add(storeGoodsSpecAndDetailsVO);
        });
        if (CollectionUtils.isNotEmpty(unitIdSet)) {
            Map<Integer, String> integerStringMap = assembleStoreGoodsUnitIdAndNameMap(unitIdSet);
            storeGoodsSpecDetailsList.forEach(storeGoodsSpecAndDetailsVO ->
                    storeGoodsSpecAndDetailsVO.setGoodsUnitName(integerStringMap.get(storeGoodsSpecAndDetailsVO.getGoodsUnit())));
        }
        storeGoodsOnSaleExtendVO.setStoreGoodsSpecDetailsList(storeGoodsSpecDetailsList);
    }

    /**
     * 组装店铺商品单位ID和商品单位名称的映射
     *
     * @param storeGoodsUnitIdSet 店铺商品单位ID列表
     * @return 商品单位ID和商品单位名称的映射
     */
    private Map<Integer, String> assembleStoreGoodsUnitIdAndNameMap(Set<Integer> storeGoodsUnitIdSet) {
        return saleUnitMeasurementMapperService.listByIds(storeGoodsUnitIdSet)
                .stream()
                .collect(toMap(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName));
    }

    /**
     * 检验传入的店铺商品是否存在，存在则返回商品信息，不存在则抛出异常
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询在售商品详情DTO
     * @return 店铺商品信息
     */
    private List<StoreGoodsPO> checkStoreGoodsIsExistsAndReturnInfo(QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO) {
        if (StringUtils.isNotEmpty(queryStoreGoodsOnSaleDetailDTO.getScaleCustomCode())) {
            return storeGoodsMapperService.queryStoreGoodsByScaleCustomCode(queryStoreGoodsOnSaleDetailDTO.getStoreId(),
                    queryStoreGoodsOnSaleDetailDTO.getScaleCustomCode(), queryStoreGoodsOnSaleDetailDTO.getScaleType());
        } else if (StringUtils.isNotEmpty(queryStoreGoodsOnSaleDetailDTO.getBarcode())) {
            List<GoodsPackageSkuPO> goodsPackageSkuPOList = goodsPackageSkuMapperService.lambdaQuery()
                    .select(GoodsPackageSkuPO::getStoreGoodsId, GoodsPackageSkuPO::getBarcode)
                    .like(GoodsPackageSkuPO::getBarcode, queryStoreGoodsOnSaleDetailDTO.getBarcode())
                    .eq(GoodsPackageSkuPO::getSpecType, 2)
                    .list();
            for (GoodsPackageSkuPO goodsPackageSkuPO : goodsPackageSkuPOList) {
                if (!CommonUtils.filterLikeBarcodeListByKeywords(goodsPackageSkuPO.getBarcode(), queryStoreGoodsOnSaleDetailDTO.getBarcode())) {
                    continue;
                }
                return storeGoodsMapperService.lambdaQuery()
                        .eq(StoreGoodsPO::getId, goodsPackageSkuPO.getStoreGoodsId())
                        .list();
            }
        } else {
            return storeGoodsMapperService.lambdaQuery()
                    .eq(StoreGoodsPO::getStoreId, queryStoreGoodsOnSaleDetailDTO.getStoreId())
                    .eq(StoreGoodsPO::getStatus, StoreGoodsStatus.ENABLE.getValue())
                    .eq(StoreGoodsPO::getListingStatus, StoreGoodsListingStatus.ON_SALE.getValue())
                    .eq(!Objects.isNull(queryStoreGoodsOnSaleDetailDTO.getStoreGoodsId()),
                            StoreGoodsPO::getId, queryStoreGoodsOnSaleDetailDTO.getStoreGoodsId())
                    .like(StringUtils.isNotEmpty(queryStoreGoodsOnSaleDetailDTO.getBarcode()),
                            StoreGoodsPO::getBarcode, queryStoreGoodsOnSaleDetailDTO.getBarcode())
                    .eq(StringUtils.isNotEmpty(queryStoreGoodsOnSaleDetailDTO.getGoodsCustomCode()),
                            StoreGoodsPO::getGoodsCustomCode, queryStoreGoodsOnSaleDetailDTO.getGoodsCustomCode())
                    .list();
        }
        return Collections.emptyList();
    }
}

package com.holderzone.holderpaasmdm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.holderpaasmdm.common.constant.UrlPathConstants;
import com.holderzone.holderpaasmdm.common.context.impl.StoreGoodsAnalysisContextImpl;
import com.holderzone.holderpaasmdm.common.datasource.DataSourceContextHolder;
import com.holderzone.holderpaasmdm.common.exception.BaseException;
import com.holderzone.holderpaasmdm.common.utils.CommonUtils;
import com.holderzone.holderpaasmdm.common.utils.DateUtils;
import com.holderzone.holderpaasmdm.converter.GoodsBrandConverter;
import com.holderzone.holderpaasmdm.converter.GoodsLabelConverter;
import com.holderzone.holderpaasmdm.converter.GoodsPictureConverter;
import com.holderzone.holderpaasmdm.converter.GoodsScaleCodeConverter;
import com.holderzone.holderpaasmdm.converter.SaleCategoryConverter;
import com.holderzone.holderpaasmdm.converter.StoreGoodsConverter;
import com.holderzone.holderpaasmdm.converter.StoreSalesProgramConverter;
import com.holderzone.holderpaasmdm.enumeraton.GoodsValuationMethod;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.enumeraton.SalesProgramPeriodType;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpand;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpandQueryType;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsListingStatus;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsStatus;
import com.holderzone.holderpaasmdm.enumeraton.StoreSalesProgramStatus;
import com.holderzone.holderpaasmdm.enumeraton.StoreSource;
import com.holderzone.holderpaasmdm.mapper.service.GoodsBrandMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsLabelMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsPackageSkuMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsScaleCodeMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsSpecDetailMapperService;
import com.holderzone.holderpaasmdm.mapper.service.GoodsSpecMapperService;
import com.holderzone.holderpaasmdm.mapper.service.PlatformSalesChannelMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleCategoryAndStoreGoodsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleCategoryMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleChannelGoodsSortMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SaleUnitMeasurementMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SkuSpecDetailsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SpecDetailMapperService;
import com.holderzone.holderpaasmdm.mapper.service.SpecMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreGoodsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramGoodsPriceMapperService;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramMapperService;
import com.holderzone.holderpaasmdm.mapper.service.UploadFilesMapperService;
import com.holderzone.holderpaasmdm.model.bo.AssembleForListStoreGoodsSpecDetailListBO;
import com.holderzone.holderpaasmdm.model.bo.QueryStoreGoodsBaseListForOnSaleBO;
import com.holderzone.holderpaasmdm.model.bo.QueryStoreGoodsForAvailableForSaleWeighListBO;
import com.holderzone.holderpaasmdm.model.bo.QueryStorePosGoodsAvailableForSaleBO;
import com.holderzone.holderpaasmdm.model.bo.StoreSalesProgramSectionBO;
import com.holderzone.holderpaasmdm.model.po.GoodsPackageSkuPO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecPO;
import com.holderzone.holderpaasmdm.model.po.PlatformSalesChannelPO;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryAndStoreGoodsPO;
import com.holderzone.holderpaasmdm.model.po.SaleChannelGoodsSortPO;
import com.holderzone.holderpaasmdm.model.po.SkuSpecDetailsPO;
import com.holderzone.holderpaasmdm.model.po.SpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.SpecPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;
import com.holderzone.holderpaasmdm.model.po.SaleCategoryPO;
import com.holderzone.holderpaasmdm.model.po.SaleUnitMeasurementPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPricePO;
import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import com.holderzone.holderpaasmdm.model.po.UploadFilesPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import com.holderzone.holderpaasmdm.model.vo.*;
import com.holderzone.holderpaasmdm.service.CommonService;
import com.holderzone.holderpaasmdm.service.GoodsPackageSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.holderpaasmdm.common.constant.DataSourceConstants.DATASOURCE_PREFIX;
import static com.holderzone.holderpaasmdm.common.constant.ErrorMessageKeys.NO_SALE_PROGRAM;
import static com.holderzone.holderpaasmdm.common.constant.RequestConstants.COMPANY_ID_HEADER_KEY;
import static com.holderzone.holderpaasmdm.common.constant.RequestConstants.REQUEST_PARAM_REFUND_NUMBER;


/**
 * Description: 公共 ServiceImpl
 * Author: 向超
 * Date: 2024/11/25 15:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CommonServiceImpl implements CommonService {
    private final StoreGoodsMapperService storeGoodsMapperService;
    private final StoreSalesProgramMapperService storeSalesProgramMapperService;
    private final StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService;
    private final StoreSalesProgramGoodsPriceMapperService storeSalesProgramGoodsPriceMapperService;
    private final StoreGoodsConverter storeGoodsConverter;
    private final GoodsLabelConverter goodsLabelConverter;
    private final SaleCategoryConverter saleCategoryConverter;
    private final SaleCategoryAndStoreGoodsMapperService saleCategoryAndStoreGoodsMapperService;
    private final GoodsBrandMapperService goodsBrandMapperService;
    private final GoodsLabelMapperService goodsLabelMapperService;
    private final SaleCategoryMapperService saleCategoryMapperService;
    private final UploadFilesMapperService uploadFilesMapperService;
    private final GoodsBrandConverter goodsBrandConverter;
    private final GoodsPictureConverter goodsPictureConverter;
    private final StoreSalesProgramConverter storeSalesProgramConverter;
    private final SaleUnitMeasurementMapperService saleUnitMeasurementMapperService;
    private final GoodsScaleCodeMapperService goodsScaleCodeMapperService;
    private final SaleChannelGoodsSortMapperService saleChannelGoodsSortMapperService;
    private final GoodsPackageSkuMapperService goodsPackageSkuMapperService;
    private final SpecMapperService specMapperService;
    private final SpecDetailMapperService specDetailMapperService;
    private final SkuSpecDetailsMapperService skuSpecDetailsMapperService;
    private final GoodsSpecMapperService goodsSpecMapperService;
    private final GoodsSpecDetailMapperService goodsSpecDetailMapperService;
    private final GoodsScaleCodeConverter goodsScaleCodeConverter;
    private final RestTemplate restTemplate;
    private final PlatformSalesChannelMapperService platformSalesChannelMapperService;
    private final GoodsPackageSkuService goodsPackageSkuService;

    /**
     * 库存服务的内网域名
     */
    @Value("${inventory-service.intranet-host}")
    private String inventoryServiceIntranetHost;

    @Override
    public PageRespVO<StoreGoodsOnSaleExtendVO> queryStoreGoodsBaseListForOnSale(
            QueryStoreGoodsBaseListForOnSaleBO queryStoreGoodsBaseListForOnSaleBO) {
        // 查询默认销售方案 以及 自定义销售方案和当前时间段对应的销售方案的时间段序号的映射
        Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair =
                queryDefaultSalesProgramAndCustomSalesProgramAndTimeSerialNumberPair(
                        queryStoreGoodsBaseListForOnSaleBO.getStoreId(), queryStoreGoodsBaseListForOnSaleBO.getChannelId());
        // 获取当前时刻满足的销售方案 和 时段序号的映射，用户后续商品时段价格的组装，需要哪个时段什么价格匹配上
        Pair<StoreSalesProgramPO, Integer> customStoreSalesProgramIdAndTimeSerialNumberPair = resultPair.getValue();
        // 获取默认销售方案
        StoreSalesProgramPO defaultStoreSalesProgramPO = resultPair.getKey();
        // 根据传入的spu编码列表查询出所有的商品ID列表，用于后续的查询
        assembleGoodsIdListBySkuCodeList(queryStoreGoodsBaseListForOnSaleBO);
        // 根据销售方案的ID列表查询出所有的销售方案商品信息
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList =
                getStoreSalesProgramGoodsPOListBySalesProgramIdList(defaultStoreSalesProgramPO,
                        customStoreSalesProgramIdAndTimeSerialNumberPair, queryStoreGoodsBaseListForOnSaleBO.getStoreGoodsIdList());
        if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
            return new PageRespVO<>(Collections.emptyList(), queryStoreGoodsBaseListForOnSaleBO.getLimit(),
                    queryStoreGoodsBaseListForOnSaleBO.getPage());
        }
        // 定义默认方案的 店铺商品ID 和 默认销售方案信息的 映射，用于后续的在售商品信息的组装
        Map<Integer, List<StoreSalesProgramGoodsPO>> defaultStoreGoodsIdAndInfoListMap = new HashMap<>();
        // 定义自定义方案的 店铺商品ID 和 自定义销售方案信息的 映射，用于后续的在售商品信息的组装
        Map<Integer, List<StoreSalesProgramGoodsPO>> customStoreGoodsIdAndInfoListMap = new HashMap<>();
        // 组装销售方案的ID和销售方案商品信息的映射，用于后续的在售商品信息的组装 （默认和自定义一并组装）
        assembleSalesProgramIdAndStoreSalesProgramGoodsInfoMap(storeSalesProgramGoodsPOList, defaultStoreSalesProgramPO,
                defaultStoreGoodsIdAndInfoListMap, customStoreGoodsIdAndInfoListMap);
        Set<Integer> storeGoodsIdSet = new HashSet<>();
        storeGoodsIdSet.addAll(defaultStoreGoodsIdAndInfoListMap.keySet());
        storeGoodsIdSet.addAll(customStoreGoodsIdAndInfoListMap.keySet());
        // 根据方案商品ID查询出所有的店铺商品信息
        List<StoreGoodsPO> storeGoodsPOList = storeGoodsMapperService.lambdaQuery()
                .in(StoreGoodsPO::getId, storeGoodsIdSet)
                .eq(StoreGoodsPO::getStatus, StoreGoodsStatus.ENABLE.getValue())
                .eq(StoreGoodsPO::getListingStatus, StoreGoodsListingStatus.ON_SALE.getValue())
                .in(CollectionUtils.isNotEmpty(queryStoreGoodsBaseListForOnSaleBO.getSkuCodeList()), StoreGoodsPO::getSpuCode,
                        queryStoreGoodsBaseListForOnSaleBO.getSkuCodeList())
                .list();
        // 根据销售分组ID筛选商品
        filterStoreGoodsPOListBySaleCategoryId(storeGoodsPOList, queryStoreGoodsBaseListForOnSaleBO.getSelaCategoryIdList());
        if (CollectionUtils.isEmpty(storeGoodsPOList)) {
            return new PageRespVO<>(Collections.emptyList(), queryStoreGoodsBaseListForOnSaleBO.getLimit(),
                    queryStoreGoodsBaseListForOnSaleBO.getPage());
        }
        List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList = storeGoodsConverter.toStoreGoodsOnSaleExtendVOList(storeGoodsPOList);
        List<GoodsPackageSkuPO> goodsPackageSkuPOList = goodsPackageSkuMapperService.lambdaQuery()
                .select(GoodsPackageSkuPO::getId, GoodsPackageSkuPO::getStoreGoodsId, GoodsPackageSkuPO::getSkuCode,
                        GoodsPackageSkuPO::getCosts, GoodsPackageSkuPO::getGoodsUnit, GoodsPackageSkuPO::getBarcode)
                .in(GoodsPackageSkuPO::getStoreGoodsId, storeGoodsIdSet)
                .list();
        Set<Integer> barcodeMatchSuccessfulStoreGoodsIdSet = new HashSet<>();
        Map<Integer, GoodsPackageSkuPO> skuIdAndInfoMap = new HashMap<>();
        filterBarcodeAndSpecDetailNameReturnMatchSuccessMap(queryStoreGoodsBaseListForOnSaleBO, goodsPackageSkuPOList,
                barcodeMatchSuccessfulStoreGoodsIdSet, skuIdAndInfoMap);
        for (StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO : storeGoodsOnSaleExtendVOList) {
            // 开始组装销售方案商品信息
            assembleStoreGoods(customStoreGoodsIdAndInfoListMap, defaultStoreGoodsIdAndInfoListMap, storeGoodsOnSaleExtendVO, skuIdAndInfoMap);
        }

        // 根据传入的关键字模糊匹配商品的销售名称、商品条码、商品自编码、拼音简码
        filterStoreGoodsOnSaleExtendVOListByKeyword(storeGoodsOnSaleExtendVOList, queryStoreGoodsBaseListForOnSaleBO.getKeyword(), barcodeMatchSuccessfulStoreGoodsIdSet);
        if (CollectionUtils.isEmpty(storeGoodsOnSaleExtendVOList)) {
            return new PageRespVO<>(Collections.emptyList(), queryStoreGoodsBaseListForOnSaleBO.getLimit(),
                    queryStoreGoodsBaseListForOnSaleBO.getPage());
        }
        // 对可售商品进行排序
        storeGoodsOnSaleExtendVOList = sortStoreGoodsBaseVOList(queryStoreGoodsBaseListForOnSaleBO.getStoreId(),
                queryStoreGoodsBaseListForOnSaleBO.getChannelId(), storeGoodsOnSaleExtendVOList);
        PageRespVO<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOPageRespVO = new PageRespVO<>(storeGoodsOnSaleExtendVOList,
                queryStoreGoodsBaseListForOnSaleBO.getLimit(), queryStoreGoodsBaseListForOnSaleBO.getPage());
        storeGoodsOnSaleExtendVOList = storeGoodsOnSaleExtendVOPageRespVO.getList();
        if (CollectionUtils.isEmpty(storeGoodsOnSaleExtendVOList)) {
            return new PageRespVO<>(Collections.emptyList(), queryStoreGoodsBaseListForOnSaleBO.getLimit(),
                    queryStoreGoodsBaseListForOnSaleBO.getPage());
        }
        // 定义默认方案的 店铺商品ID 和 时段价格的映射，用于后续的在售商品信息的组装
        Map<Integer, BigDecimal> defaultStoreProgramGoodsIdAndPriceMap = new HashMap<>();
        // 定义自定义方案的 店铺商品ID 和 时段价格的映射，用于后续的在售商品信息的组装
        Map<Integer, BigDecimal> customStoreProgramGoodsIdAndPriceMap = new HashMap<>();
        // 根据销售方案的ID列表和时段序号列表查询出所有的销售方案商品价格信息，用于后续组装销售价格
        List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList =
                getStoreSalesProgramGoodsPricePOByStoreSalesProgramIdListAndSerialNumber(
                        customStoreSalesProgramIdAndTimeSerialNumberPair, defaultStoreSalesProgramPO,
                        storeGoodsOnSaleExtendVOList.stream().map(StoreGoodsOnSaleExtendVO::getId).distinct().toList());
        // 组装销售方案的ID和时段价格的映射，用于后续的在售商品信息的组装 （默认和自定义一并组装）
        assembleSalesProgramIdAndStoreSalesProgramGoodsPriceMap(storeSalesProgramGoodsPricePOList, defaultStoreSalesProgramPO,
                defaultStoreProgramGoodsIdAndPriceMap, customStoreProgramGoodsIdAndPriceMap);
        // 将在售商品信息的PO对象转为 VO对象
        // 组装商品的销售价格、销售方案商品信息（包含商品销售名称、销售标签的ID、销售封面的ID，这三个需要根据生效的销售方案来定）、商品品牌扩展信息、销售分类扩展信息
        convertAndAssembleStoreGoodsOnSaleInfo(storeGoodsOnSaleExtendVOList, customStoreProgramGoodsIdAndPriceMap,
                defaultStoreProgramGoodsIdAndPriceMap, queryStoreGoodsBaseListForOnSaleBO);
        // 组装商品的扩展信息（包含：商品封面、商品标签等映射关系）
        assembleStoreGoodsLabelAndCoverMap(storeGoodsOnSaleExtendVOList, queryStoreGoodsBaseListForOnSaleBO.getStoreGoodsExpandList());
        return new PageRespVO<>(storeGoodsOnSaleExtendVOList, storeGoodsOnSaleExtendVOPageRespVO.getTotal(),
                storeGoodsOnSaleExtendVOPageRespVO.getPageSize(), storeGoodsOnSaleExtendVOPageRespVO.getPageNum(),
                storeGoodsOnSaleExtendVOPageRespVO.getTotalPage());
    }

    /**
     * 根据传入的关键字模糊匹配商品的商品条码、规格值名称
     *
     * @param queryStoreGoodsBaseListForOnSaleBO    查询参数
     * @param goodsPackageSkuPOList                 商品包装SKU信息列表
     * @param barcodeMatchSuccessfulStoreGoodsIdSet 条码匹配成功的商品ID集合
     * @param skuIdAndInfoMap                       SKU ID和信息的映射
     */
    private void filterBarcodeAndSpecDetailNameReturnMatchSuccessMap(QueryStoreGoodsBaseListForOnSaleBO queryStoreGoodsBaseListForOnSaleBO,
                                                                     List<GoodsPackageSkuPO> goodsPackageSkuPOList,
                                                                     Set<Integer> barcodeMatchSuccessfulStoreGoodsIdSet,
                                                                     Map<Integer, GoodsPackageSkuPO> skuIdAndInfoMap) {
        if (CollectionUtils.isNotEmpty(goodsPackageSkuPOList)) {
            List<Integer> matchSuccessfulSkuIdList = skuSpecDetailsMapperService.queryMatchSuccessfulSkuIdByKeywords(
                    CommonUtils.assembleSqlLikeString(queryStoreGoodsBaseListForOnSaleBO.getKeyword()), queryStoreGoodsBaseListForOnSaleBO.getStoreId());
            boolean matchSuccessfulSkuIdListNotEmpty = CollectionUtils.isNotEmpty(matchSuccessfulSkuIdList);
            for (GoodsPackageSkuPO goodsPackageSkuPO : goodsPackageSkuPOList) {
                if (StringUtils.isNotEmpty(queryStoreGoodsBaseListForOnSaleBO.getKeyword()) &&
                        CommonUtils.filterLikeBarcodeListByKeywords(goodsPackageSkuPO.getBarcode(), queryStoreGoodsBaseListForOnSaleBO.getKeyword())) {
                    barcodeMatchSuccessfulStoreGoodsIdSet.add(goodsPackageSkuPO.getStoreGoodsId());
                }
                if (matchSuccessfulSkuIdListNotEmpty && matchSuccessfulSkuIdList.contains(goodsPackageSkuPO.getId())) {
                    barcodeMatchSuccessfulStoreGoodsIdSet.add(goodsPackageSkuPO.getStoreGoodsId());
                }
                skuIdAndInfoMap.put(goodsPackageSkuPO.getId(), goodsPackageSkuPO);
            }
        }
    }

    /**
     * 根据传入的SPU编码列表，查询出对应的商品ID列表，用于后续的查询
     *
     * @param queryStoreGoodsBaseListForOnSaleBO 查询参数
     */
    private void assembleGoodsIdListBySkuCodeList(QueryStoreGoodsBaseListForOnSaleBO queryStoreGoodsBaseListForOnSaleBO) {
        if (CollectionUtils.isEmpty(queryStoreGoodsBaseListForOnSaleBO.getSkuCodeList())) {
            return;
        }
        // 如果传入了SPU编码，则根据SPU编码查询出所有的商品ID
        List<Integer> goodsIdListBySpuCodeList = storeGoodsMapperService.lambdaQuery().select(StoreGoodsPO::getId)
                .in(StoreGoodsPO::getSpuCode, queryStoreGoodsBaseListForOnSaleBO.getSkuCodeList())
                .list().stream().map(StoreGoodsPO::getId).toList();
        if (CollectionUtils.isEmpty(goodsIdListBySpuCodeList)) {
            return;
        }
        if (CollectionUtils.isEmpty(queryStoreGoodsBaseListForOnSaleBO.getStoreGoodsIdList())) {
            queryStoreGoodsBaseListForOnSaleBO.setStoreGoodsIdList(goodsIdListBySpuCodeList);
        } else {
            queryStoreGoodsBaseListForOnSaleBO.getStoreGoodsIdList().addAll(goodsIdListBySpuCodeList);
        }
    }

    @Override
    public List<Integer> queryStoreGoodsIdListBySaleCategoryIdListAndChild(List<Integer> categoryIdList) {
        // 需要查询连带查询销售分组的子级分组关联的商品
        categoryIdList.addAll(saleCategoryMapperService.lambdaQuery()
                .select(SaleCategoryPO::getId)
                .in(SaleCategoryPO::getParentId, categoryIdList)
                .list().stream().map(SaleCategoryPO::getId).distinct().toList());
        return saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                .in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, categoryIdList)
                .list().stream().map(SaleCategoryAndStoreGoodsPO::getStoreGoodsId).toList();
    }

    /**
     * 对可售商品进行排序，排序规则如下：
     * 1、如果商品有排序的数据，则按照数据库中的排序数据来进行排序，sort值越小越靠前
     * 2、如果sort值相同，则按照创建时间倒序来进行排序
     * 3、如果商品没有排序的数据，则按照创建时间倒序来进行排序
     *
     * @param storeId                      店铺ID
     * @param channelId                    渠道ID
     * @param storeGoodsOnSaleExtendVOList 可售商品列表
     * @return 排序后的可售商品列表
     */
    @Override
    public <T extends StoreGoodsBaseVO> List<T> sortStoreGoodsBaseVOList(
            Integer storeId, Integer channelId, List<T> storeGoodsOnSaleExtendVOList) {
        List<Integer> allStoreGoodsIdList = new ArrayList<>();
        Map<Integer, T> storeGoodsOnSaleExtendVOMap = new HashMap<>();
        for (T storeGoodsOnSaleExtendVO : storeGoodsOnSaleExtendVOList) {
            allStoreGoodsIdList.add(storeGoodsOnSaleExtendVO.getId());
            storeGoodsOnSaleExtendVOMap.put(storeGoodsOnSaleExtendVO.getId(), storeGoodsOnSaleExtendVO);
        }
        // 查询商品排序数据
        List<SaleChannelGoodsSortPO> saleChannelGoodsSortPOList = saleChannelGoodsSortMapperService.lambdaQuery()
                .eq(SaleChannelGoodsSortPO::getStoreId, storeId)
                .eq(SaleChannelGoodsSortPO::getChannelId, channelId)
                .in(SaleChannelGoodsSortPO::getStoreGoodsId, allStoreGoodsIdList)
                .list();
        if (CollectionUtils.isEmpty(saleChannelGoodsSortPOList)) {
            // 如果都没有排序数据则按照创建时间倒序来进行排序
            storeGoodsOnSaleExtendVOList.sort(Comparator.comparing(StoreGoodsBaseVO::getCreatedAt).reversed());
        } else {
            // 先将商品排序数据分组，然后按照分组的key进行排序，分组的Key则是sort值，越小越靠前
            TreeMap<Integer, List<Integer>> sortedGroupTreeMap = saleChannelGoodsSortPOList.stream()
                    .collect(Collectors.groupingBy(SaleChannelGoodsSortPO::getSort,
                            () -> new TreeMap<>(Comparator.naturalOrder()), // TreeMap 会根据 key (即 sort) 自动排序
                            Collectors.mapping(SaleChannelGoodsSortPO::getStoreGoodsId, Collectors.toList())
                    ));
            List<T> sortedStoreGoodsOnSaleExtendVOList = new ArrayList<>();
            List<Integer> storeGoodsIdListForHaveSort = new ArrayList<>();
            for (Map.Entry<Integer, List<Integer>> item : sortedGroupTreeMap.entrySet()) {
                storeGoodsIdListForHaveSort.addAll(item.getValue());
                sortedStoreGoodsOnSaleExtendVOList.addAll(item.getValue().stream()
                        .map(storeGoodsOnSaleExtendVOMap::get)
                        .sorted(Comparator.comparing(StoreGoodsBaseVO::getCreatedAt).reversed())
                        .toList());
            }
            // 处理没有排序数据的商品
            allStoreGoodsIdList.removeAll(storeGoodsIdListForHaveSort);
            if (CollectionUtils.isNotEmpty(allStoreGoodsIdList)) {
                sortedStoreGoodsOnSaleExtendVOList.addAll(allStoreGoodsIdList.stream()
                        .map(storeGoodsOnSaleExtendVOMap::get)
                        .sorted(Comparator.comparing(StoreGoodsBaseVO::getCreatedAt).reversed())
                        .toList());
            }
            storeGoodsOnSaleExtendVOList = sortedStoreGoodsOnSaleExtendVOList;
        }
        return storeGoodsOnSaleExtendVOList;
    }

    /**
     * 根据传入的关键字模糊匹配商品的销售名称、商品条码、商品自编码、拼音简码
     *
     * @param storeGoodsBaseVOList                  待组装的在售商品列表
     * @param keywords                              关键字
     * @param barcodeMatchSuccessfulStoreGoodsIdSet 匹配成功的商品ID集合
     */
    private void filterStoreGoodsOnSaleExtendVOListByKeyword(List<StoreGoodsOnSaleExtendVO> storeGoodsBaseVOList,
                                                             String keywords,
                                                             Set<Integer> barcodeMatchSuccessfulStoreGoodsIdSet) {
        if (StringUtils.isBlank(keywords)) {
            return;
        }
        String finalKeywords = keywords.toLowerCase();
        Iterator<StoreGoodsOnSaleExtendVO> iterator = storeGoodsBaseVOList.iterator();
        while (iterator.hasNext()) {
            StoreGoodsOnSaleExtendVO storeGoodsBaseVO = iterator.next();
            // 单独判断一下商品条码，因为条码绑定商品变成了条码绑定SKU了
            if (barcodeMatchSuccessfulStoreGoodsIdSet.contains(storeGoodsBaseVO.getId())) {
                continue;
            }
            if (!filterStoreGoodsOnSaleExtendVOByKeyword(storeGoodsBaseVO, finalKeywords)) {
                iterator.remove();
            }
        }
    }

    /**
     * 根据传入的关键字模糊匹配商品的销售名称、商品条码、商品自编码、拼音简码
     *
     * @param storeGoodsBaseVO 待组装的在售商品
     * @param keywords         关键字
     * @return 是否匹配成功
     */
    private boolean filterStoreGoodsOnSaleExtendVOByKeyword(StoreGoodsOnSaleExtendVO storeGoodsBaseVO, String keywords) {
        // 根据商品售卖名称、条码、自编码、首字母中包含关键字筛选商品
        return storeGoodsBaseVO.getGoodsName().toLowerCase().contains(keywords) ||
                (StringUtils.isNotEmpty(storeGoodsBaseVO.getGoodsCustomCode()) && storeGoodsBaseVO.getGoodsCustomCode().toLowerCase().contains(keywords)) ||
                storeGoodsBaseVO.getPinyinCode().contains(keywords);
    }

    /**
     * 将在售商品信息的PO对象转为 VO对象
     * 再组装商品的销售价格、销售方案商品信息（包含商品销售名称、销售标签的ID、销售封面的ID，这三个需要根据生效的销售方案来定）、商品品牌扩展信息、销售分类扩展信息
     * 注意：后续还需根据销售标签ID和销售封面ID去组装其各自的扩展信息
     *
     * @param storeGoodsOnSaleExtendVOList          过滤后的店铺商品列表
     * @param customStoreProgramGoodsIdAndPriceMap  自定义方案的 店铺商品ID 和 时段价格的映射
     * @param defaultStoreProgramGoodsIdAndPriceMap 默认方案的 店铺商品ID 和 时段价格的映射
     * @param queryStoreGoodsBaseListForOnSaleBO    查询参数
     */
    private void convertAndAssembleStoreGoodsOnSaleInfo(List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList,
                                                        Map<Integer, BigDecimal> customStoreProgramGoodsIdAndPriceMap,
                                                        Map<Integer, BigDecimal> defaultStoreProgramGoodsIdAndPriceMap,
                                                        QueryStoreGoodsBaseListForOnSaleBO queryStoreGoodsBaseListForOnSaleBO) {
        // 定义店铺商品品牌ID列表、店铺商品ID列表、店铺商品单位ID列表
        Set<Integer> storeGoodsBrandIdSet = new HashSet<>();
        Set<Integer> storeGoodsIdSet = new HashSet<>();
        Set<Integer> storeGoodsUnitIdSet = new HashSet<>();
        Set<Integer> skuIdSet = new HashSet<>();
        for (StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO : storeGoodsOnSaleExtendVOList) {
            storeGoodsBrandIdSet.add(storeGoodsOnSaleExtendVO.getBrand());
            storeGoodsIdSet.add(storeGoodsOnSaleExtendVO.getId());
            storeGoodsUnitIdSet.add(storeGoodsOnSaleExtendVO.getGoodsUnit());
            if (CollectionUtils.isEmpty(storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList())) {
                continue;
            }
            skuIdSet.addAll(storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList().stream().map(StoreGoodsSpecAndDetailsVO::getSkuId).toList());
        }
        List<StoreGoodsExpand> storeGoodsExpandList = queryStoreGoodsBaseListForOnSaleBO.getStoreGoodsExpandList();
        boolean needQueryBrand = storeGoodsExpandList.contains(StoreGoodsExpand.BRAND);
        boolean needQuerySaleCategory = storeGoodsExpandList.contains(StoreGoodsExpand.SALE_CATEGORY);
        boolean needQueryUnit = storeGoodsExpandList.contains(StoreGoodsExpand.UNIT);
        // 组装店铺商品品牌ID和商品品牌信息的映射，用于后续的在售商品信息的组装
        Map<Integer, GoodsBrandVO> goodsBrandVOIdAndInfoMap = new HashMap<>();
        if (needQueryBrand) {
            goodsBrandVOIdAndInfoMap.putAll(assembleGoodsBrandVOIdAndInfoMap(storeGoodsBrandIdSet));
        }
        // 组装店铺商品ID和销售分组信息的映射，用于后续的在售商品信息的组装
        Map<Integer, List<StoreGoodsOnSaleExtendVO.SaleCategoryVO>> storeGoodsIdAndSaleCategoryVOListMap = new HashMap<>();
        if (needQuerySaleCategory) {
            storeGoodsIdAndSaleCategoryVOListMap.putAll(assembleStoreGoodsIdAndSaleCategoryVOListMap(storeGoodsIdSet, queryStoreGoodsBaseListForOnSaleBO.getChannelId()));
        }
        // 组装商品单位ID和商品单位名称的映射，用于后续的在售商品信息的组装
        Map<Integer, String> storeGoodsUnitIdAndNameMap = new HashMap<>();
        if (needQueryUnit) {
            storeGoodsUnitIdAndNameMap.putAll(assembleStoreGoodsUnitIdAndNameMap(storeGoodsUnitIdSet));
        }
        boolean hasMultipleSpec = storeGoodsOnSaleExtendVOList.stream().anyMatch(storeGoodsOnSaleExtendVO -> storeGoodsOnSaleExtendVO.getGoodsSpec() == 2);
        Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap = new HashMap<>();
        Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap = new HashMap<>();
        Map<Integer, SpecPO> specIdAndSpecInfoMap = null;
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap = null;
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = new HashMap<>();
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = new HashMap<>();
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = new HashMap<>();
        if (hasMultipleSpec) {
            // 查询门店明细
            specIdAndSpecInfoMap = specMapperService.findMapByStoreId(queryStoreGoodsBaseListForOnSaleBO.getStoreId());
            // 查询明细数据
            specDetailIdAndInfoMap = specDetailMapperService.findBySpecIdIn(specIdAndSpecInfoMap.keySet());
            List<SkuSpecDetailsPO> skuSpecDetailsPOList = skuSpecDetailsMapperService.lambdaQuery()
                    .select(SkuSpecDetailsPO::getGoodsSpecDetailId, SkuSpecDetailsPO::getSkuId)
                    .in(SkuSpecDetailsPO::getSkuId, skuIdSet).list();

            List<Integer> goodsSpecDetailIdList = new ArrayList<>();
            skuSpecDetailsPOList.forEach(skuSpecDetailsPO -> {
                goodsSpecDetailIdList.add(skuSpecDetailsPO.getGoodsSpecDetailId());
                skuIdAndGoodsSpecDetailIdListMap.computeIfAbsent(skuSpecDetailsPO.getSkuId(), k -> new ArrayList<>()).add(skuSpecDetailsPO.getGoodsSpecDetailId());
            });
            List<Integer> goodsSpecIdList = new ArrayList<>();

            List<GoodsSpecDetailPO> goodsSpecDetailPOList = goodsSpecDetailMapperService.lambdaQuery().in(GoodsSpecDetailPO::getId, goodsSpecDetailIdList).list();
            goodsSpecDetailPOList.forEach(goodsSpecDetailPO -> {
                goodsSpecDetailIdAndInfoMap.put(goodsSpecDetailPO.getId(), goodsSpecDetailPO);
                goodsSpecIdList.add(goodsSpecDetailPO.getGoodsSpecId());
                goodsSpecIdAndGoodsSpecDetailInfoMap.computeIfAbsent(goodsSpecDetailPO.getGoodsSpecId(), k -> new ArrayList<>()).add(goodsSpecDetailPO);
            });
            List<GoodsSpecPO> goodsSpecPOList = goodsSpecMapperService.lambdaQuery()
                    .in(GoodsSpecPO::getId, goodsSpecIdList)
                    .list();

            goodsSpecPOList.forEach(goodsSpecPO -> {
                storeGoodsIdAndGoodsSpecPOListMap.computeIfAbsent(goodsSpecPO.getStoreGoodsId(), k -> new ArrayList<>()).add(goodsSpecPO);
                goodsSpecIdAndInfoMap.put(goodsSpecPO.getId(), goodsSpecPO);
            });
        }
        AssembleForListStoreGoodsSpecDetailListBO assembleForListStoreGoodsSpecDetailListBO = AssembleForListStoreGoodsSpecDetailListBO.builder()
                .customStoreProgramGoodsIdAndPriceMap(customStoreProgramGoodsIdAndPriceMap)
                .goodsSpecDetailIdAndInfoMap(goodsSpecDetailIdAndInfoMap)
                .skuIdAndGoodsSpecDetailIdListMap(skuIdAndGoodsSpecDetailIdListMap)
                .goodsSpecIdAndInfoMap(goodsSpecIdAndInfoMap)
                .storeGoodsUnitIdAndNameMap(storeGoodsUnitIdAndNameMap)
                .specDetailIdAndInfoMap(specDetailIdAndInfoMap)
                .specIdAndSpecInfoMap(specIdAndSpecInfoMap)
                .defaultStoreProgramGoodsIdAndPriceMap(defaultStoreProgramGoodsIdAndPriceMap)
                .build();
        for (StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO : storeGoodsOnSaleExtendVOList) {
            assembleStoreGoodsSpecList(storeGoodsOnSaleExtendVO, storeGoodsIdAndGoodsSpecPOListMap, goodsSpecIdAndGoodsSpecDetailInfoMap,
                    specIdAndSpecInfoMap, specDetailIdAndInfoMap);
            // 开始组装销售价格 和 商品真实存在的商品规格
            assembleStoreGoodsSellingPrice(storeGoodsOnSaleExtendVO, assembleForListStoreGoodsSpecDetailListBO);
            if (needQueryBrand) {
                // 开始组装商品品牌扩展信息
                storeGoodsOnSaleExtendVO.setBrandInfo(goodsBrandVOIdAndInfoMap.get(storeGoodsOnSaleExtendVO.getBrand()));
            }
            if (needQuerySaleCategory) {
                // 开始组装销售分组扩展信息
                storeGoodsOnSaleExtendVO.setCategoryList(storeGoodsIdAndSaleCategoryVOListMap.get(storeGoodsOnSaleExtendVO.getId()));
            }
        }
    }

    /**
     * 组装商品的全量规格信息
     *
     * @param storeGoodsOnSaleExtendVO             在售商品信息
     * @param storeGoodsIdAndGoodsSpecPOListMap    店铺商品ID和商品规格信息的映射
     * @param goodsSpecIdAndGoodsSpecDetailInfoMap 商品规格ID和商品规格详情信息的映射
     * @param specIdAndSpecInfoMap                 商品规格ID和商品规格信息的映射
     * @param specDetailIdAndInfoMap               商品规格详情ID和商品规格详情信息的映射
     */
    @Override
    public void assembleStoreGoodsSpecList(StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                           Map<Integer, List<GoodsSpecPO>> storeGoodsIdAndGoodsSpecPOListMap,
                                           Map<Integer, List<GoodsSpecDetailPO>> goodsSpecIdAndGoodsSpecDetailInfoMap,
                                           Map<Integer, SpecPO> specIdAndSpecInfoMap,
                                           Map<Integer, SpecDetailPO> specDetailIdAndInfoMap) {
        if (!storeGoodsIdAndGoodsSpecPOListMap.containsKey(storeGoodsOnSaleExtendVO.getId()) || storeGoodsOnSaleExtendVO.getGoodsSpec() == 1) {
            return;
        }
        List<StoreGoodsSpecVO> storeGoodsSpecList = new ArrayList<>();
        Set<Integer> imgIdSet = new HashSet<>();
        for (GoodsSpecPO goodsSpecPO : storeGoodsIdAndGoodsSpecPOListMap.get(storeGoodsOnSaleExtendVO.getId())) {
            StoreGoodsSpecVO storeGoodsSpecVO = new StoreGoodsSpecVO();
            SpecPO specPO = specIdAndSpecInfoMap.get(goodsSpecPO.getSpecId());
            if (Objects.isNull(specPO)) {
                continue;
            }
            storeGoodsSpecVO.setSpecName(specPO.getName());
            storeGoodsSpecVO.setId(goodsSpecPO.getId());
            storeGoodsSpecVO.setSpecSort(goodsSpecPO.getSort());
            List<StoreGoodsSpecDetailVO> storeGoodsSpecDetailList = new ArrayList<>();
            for (GoodsSpecDetailPO goodsSpecDetailPO : goodsSpecIdAndGoodsSpecDetailInfoMap.get(goodsSpecPO.getId())) {
                StoreGoodsSpecDetailVO storeGoodsSpecDetailVO = new StoreGoodsSpecDetailVO();
                SpecDetailPO specDetailPO = specDetailIdAndInfoMap.get(goodsSpecDetailPO.getSpecDetailId());
                storeGoodsSpecDetailVO.setSpecDetailName(specDetailPO.getName());
                storeGoodsSpecDetailVO.setSpecDetailSort(goodsSpecDetailPO.getSort());
                storeGoodsSpecDetailVO.setId(goodsSpecDetailPO.getId());
                storeGoodsSpecDetailVO.setImgId(goodsSpecDetailPO.getImgId());
                imgIdSet.add(goodsSpecDetailPO.getImgId());
                storeGoodsSpecDetailList.add(storeGoodsSpecDetailVO);
            }
            storeGoodsSpecVO.setStoreGoodsSpecDetailList(storeGoodsSpecDetailList.stream()
                    .sorted(Comparator.comparingInt(StoreGoodsSpecDetailVO::getSpecDetailSort)
                            .thenComparing(StoreGoodsSpecDetailVO::getId, Comparator.reverseOrder()))
                    .toList());
            storeGoodsSpecList.add(storeGoodsSpecVO);
        }
        storeGoodsOnSaleExtendVO.setStoreGoodsSpecList(storeGoodsSpecList.stream()
                .sorted(Comparator.comparingInt(StoreGoodsSpecVO::getSpecSort)
                        .thenComparing(StoreGoodsSpecVO::getId, Comparator.reverseOrder()))
                .toList());

        if (CollectionUtils.isEmpty(imgIdSet)) {
            return;
        }
        Map<Integer, GoodsPictureVO> integerGoodsPictureVOMap = assembleCoverIdAndPictureVOMap(imgIdSet);
        storeGoodsSpecList.forEach(storeGoodsSpecVO ->
                storeGoodsSpecVO.getStoreGoodsSpecDetailList().forEach(storeGoodsSpecDetailVO ->
                        storeGoodsSpecDetailVO.setImage(integerGoodsPictureVOMap.get(storeGoodsSpecDetailVO.getImgId()))));
    }

    /**
     * 组装店铺商品单位ID和商品单位名称的映射
     *
     * @param storeGoodsUnitIdSet 店铺商品单位ID列表
     * @return 商品单位ID和商品单位名称的映射
     */
    private Map<Integer, String> assembleStoreGoodsUnitIdAndNameMap(Set<Integer> storeGoodsUnitIdSet) {
        return saleUnitMeasurementMapperService.listByIds(storeGoodsUnitIdSet)
                .stream()
                .collect(Collectors.toMap(SaleUnitMeasurementPO::getId, SaleUnitMeasurementPO::getUnitName));
    }

    /**
     * 组装商品品牌ID和商品品牌信息的映射
     *
     * @param storeGoodsBrandIdList 过滤后的店铺商品品牌列表
     * @return 商品品牌ID和商品品牌信息的映射
     */
    private Map<Integer, GoodsBrandVO> assembleGoodsBrandVOIdAndInfoMap(Collection<Integer> storeGoodsBrandIdList) {
        return goodsBrandConverter.toGoodsBrandVOList(
                        goodsBrandMapperService.queryGoodsBrandExtendListByIdList(
                                storeGoodsBrandIdList))
                .stream()
                .collect(Collectors.toMap(GoodsBrandVO::getId, Function.identity()));
    }

    /**
     * 组装销售方案商品信息（包含商品销售名称、销售标签的ID、销售封面的ID，这三个需要根据生效的销售方案来定）
     *
     * @param customStoreGoodsIdAndInfoListMap  自定义方案的 店铺商品ID 和 自定义销售方案信息的 映射
     * @param defaultStoreGoodsIdAndInfoListMap 默认方案的 店铺商品ID 和 默认销售方案信息的 映射
     * @param storeGoodsOnSaleExtendVO          在售商品信息
     * @param skuIdAndInfoMap                   商品SKUId和商品SKU信息的映射
     */
    private static void assembleStoreGoods(Map<Integer, List<StoreSalesProgramGoodsPO>> customStoreGoodsIdAndInfoListMap,
                                           Map<Integer, List<StoreSalesProgramGoodsPO>> defaultStoreGoodsIdAndInfoListMap,
                                           StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                           Map<Integer, GoodsPackageSkuPO> skuIdAndInfoMap) {
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList;
        if (customStoreGoodsIdAndInfoListMap.containsKey(storeGoodsOnSaleExtendVO.getId())) {
            // 如果自定义方案中存在当前店铺商品ID，则使用自定义方案的商品信息，因为自定义方案的商品信息优先级更高
            storeSalesProgramGoodsPOList = customStoreGoodsIdAndInfoListMap.get(storeGoodsOnSaleExtendVO.getId());
        } else {
            // 如果自定义方案中不存在当前店铺商品ID，则使用默认方案的商品信息
            storeSalesProgramGoodsPOList = defaultStoreGoodsIdAndInfoListMap.get(storeGoodsOnSaleExtendVO.getId());
        }
        // 所有的SKU的销售信息（主商品相关的都一样，所以只用取第一个SKU的销售信息即可）
        StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramGoodsPOList.get(0);
        storeGoodsOnSaleExtendVO.setStoreSalesProgramId(storeSalesProgramGoodsPO.getStoreSalesProgramId());
        // 设置生效的销售方案的商品名称
        storeGoodsOnSaleExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
        // 设置生效的销售方案的商品标签的ID，用于后续组装标签的扩展信息
        storeGoodsOnSaleExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
        // 设置生效的销售方案的商品封面的ID，用于后续组装封面的扩展信息
        storeGoodsOnSaleExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
        List<StoreGoodsSpecAndDetailsVO> storeGoodsSpecDetailsList = new ArrayList<>();
        // 组装商品的规格和详情信息
        for (StoreSalesProgramGoodsPO storeSalesProgramGoodsSpecPO : storeSalesProgramGoodsPOList) {
            StoreGoodsSpecAndDetailsVO storeGoodsSpecAndDetailsVO = new StoreGoodsSpecAndDetailsVO();
            GoodsPackageSkuPO goodsPackageSkuPO = skuIdAndInfoMap.get(storeSalesProgramGoodsSpecPO.getGoodsPackageSkuId());
            storeGoodsSpecAndDetailsVO.setBarcode(goodsPackageSkuPO.getBarcode());
            storeGoodsSpecAndDetailsVO.setGoodsUnit(goodsPackageSkuPO.getGoodsUnit());
            storeGoodsSpecAndDetailsVO.setCosts(Objects.isNull(goodsPackageSkuPO.getCosts()) ? null : goodsPackageSkuPO.getCosts().toString());
            storeGoodsSpecAndDetailsVO.setSkuCode(goodsPackageSkuPO.getSkuCode());
            storeGoodsSpecAndDetailsVO.setSkuId(goodsPackageSkuPO.getId());
            storeGoodsSpecAndDetailsVO.setStoreSalesProgramGoodsId(storeSalesProgramGoodsSpecPO.getId());
            storeGoodsSpecDetailsList.add(storeGoodsSpecAndDetailsVO);
        }
        storeGoodsOnSaleExtendVO.setStoreGoodsSpecDetailsList(storeGoodsSpecDetailsList);
    }

    /**
     * 组装商品的销售价格
     *
     * @param storeGoodsOnSaleExtendVO                  店铺商品信息
     * @param assembleForListStoreGoodsSpecDetailListBO 组装后的商品规格详情列表信息
     */
    private void assembleStoreGoodsSellingPrice(StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO,
                                                AssembleForListStoreGoodsSpecDetailListBO assembleForListStoreGoodsSpecDetailListBO) {
        Map<Integer, BigDecimal> customStoreProgramGoodsIdAndPriceMap = assembleForListStoreGoodsSpecDetailListBO.getCustomStoreProgramGoodsIdAndPriceMap();
        Map<Integer, BigDecimal> defaultStoreProgramGoodsIdAndPriceMap = assembleForListStoreGoodsSpecDetailListBO.getDefaultStoreProgramGoodsIdAndPriceMap();
        Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap = assembleForListStoreGoodsSpecDetailListBO.getSkuIdAndGoodsSpecDetailIdListMap();
        Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap = assembleForListStoreGoodsSpecDetailListBO.getGoodsSpecDetailIdAndInfoMap();
        Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap = assembleForListStoreGoodsSpecDetailListBO.getGoodsSpecIdAndInfoMap();
        Map<Integer, String> storeGoodsUnitIdAndNameMap = assembleForListStoreGoodsSpecDetailListBO.getStoreGoodsUnitIdAndNameMap();
        Map<Integer, SpecPO> specIdAndSpecInfoMap = assembleForListStoreGoodsSpecDetailListBO.getSpecIdAndSpecInfoMap();
        Map<Integer, SpecDetailPO> specDetailIdAndInfoMap = assembleForListStoreGoodsSpecDetailListBO.getSpecDetailIdAndInfoMap();
        for (StoreGoodsSpecAndDetailsVO storeGoodsSpecAndDetailsVO : storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList()) {
            // 组装销售价格
            if (customStoreProgramGoodsIdAndPriceMap.containsKey(storeGoodsSpecAndDetailsVO.getStoreSalesProgramGoodsId())) {
                storeGoodsSpecAndDetailsVO.setGoodsSpecSellingPrice(customStoreProgramGoodsIdAndPriceMap.get(storeGoodsSpecAndDetailsVO.getStoreSalesProgramGoodsId())
                        .setScale(2, RoundingMode.HALF_UP).toString());
            } else {
                storeGoodsSpecAndDetailsVO.setGoodsSpecSellingPrice(defaultStoreProgramGoodsIdAndPriceMap.get(storeGoodsSpecAndDetailsVO.getStoreSalesProgramGoodsId())
                        .setScale(2, RoundingMode.HALF_UP).toString());
            }
            storeGoodsSpecAndDetailsVO.setGoodsUnitName(storeGoodsUnitIdAndNameMap.get(storeGoodsSpecAndDetailsVO.getGoodsUnit()));
            // 开始组装商品规格和详情
            if (storeGoodsOnSaleExtendVO.getGoodsSpec() == 1) {
                continue;
            }
            List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList = new ArrayList<>();
            skuIdAndGoodsSpecDetailIdListMap.get(storeGoodsSpecAndDetailsVO.getSkuId()).forEach(goodsSpecDetailId -> {
                StoreGoodsSpecRelationVO storeGoodsSpecRelationVO = new StoreGoodsSpecRelationVO();
                GoodsSpecDetailPO goodsSpecDetailPO = goodsSpecDetailIdAndInfoMap.get(goodsSpecDetailId);
                storeGoodsSpecRelationVO.setGoodsSpecDetailId(goodsSpecDetailPO.getId());
                storeGoodsSpecRelationVO.setGoodsSpecDetailSort(goodsSpecDetailPO.getSort());
                storeGoodsSpecRelationVO.setGoodsSpecDetailName(specDetailIdAndInfoMap.get(goodsSpecDetailPO.getSpecDetailId()).getName());
                GoodsSpecPO goodsSpecPO = goodsSpecIdAndInfoMap.get(goodsSpecDetailPO.getGoodsSpecId());
                storeGoodsSpecRelationVO.setGoodsSpecId(goodsSpecPO.getId());
                storeGoodsSpecRelationVO.setGoodsSpecSort(goodsSpecPO.getSort());
                storeGoodsSpecRelationVO.setGoodsSpecName(specIdAndSpecInfoMap.get(goodsSpecPO.getSpecId()).getName());
                storeGoodsSpecRelationList.add(storeGoodsSpecRelationVO);
            });
            storeGoodsSpecAndDetailsVO.setStoreGoodsSpecRelationList(storeGoodsSpecRelationList.stream()
                    .sorted(Comparator.comparingInt(StoreGoodsSpecRelationVO::getGoodsSpecSort)
                            .thenComparing(StoreGoodsSpecRelationVO::getGoodsSpecId, Comparator.reverseOrder()))
                    .toList());
        }
        if (storeGoodsOnSaleExtendVO.getGoodsSpec() == 2) {
            sortStoreGoodsSpecAndDetailsList(storeGoodsOnSaleExtendVO.getStoreGoodsSpecDetailsList());
        }
    }

    /**
     * 根据 StoreGoodsSpecRelationVO 排序
     * 排序规则：
     * 1. 比较 goodsSpecSort，升序排序；
     * 2. 如果 goodsSpecSort 相等，则比较 goodsSpecId，降序排序；
     *
     * @param specDetailList 待排序的列表，类型为 StoreGoodsSpecRelationVO 或其子类
     */
    @Override
    public <T extends StoreGoodsSpecRelationVO> void sortStoreGoodsSpecList(List<T> specDetailList) {
        // 对 specDetailList 进行排序
        // 排序规则：首先按照 goodsSpecDetailSort 升序排序，
        // 如果相等，则按照 goodsSpecDetailId 降序排序。
        specDetailList.sort((o1, o2) -> {
            // 比较 goodsSpecDetailSort（升序排序）
            int sortCompare = Integer.compare(o1.getGoodsSpecSort(), o2.getGoodsSpecSort());
            if (sortCompare != 0) {
                return sortCompare;
            }
            // 如果 goodsSpecDetailSort 相等，则比较 goodsSpecDetailId（降序排序，即倒序）
            return -Integer.compare(o1.getGoodsSpecId(), o2.getGoodsSpecId());
        });
    }

    /**
     * 根据 StoreGoodsSpecAndDetailsVO 内部的 storeGoodsSpecRelationList 排序
     * 排序规则：
     * 1. 依次比较 storeGoodsSpecRelationList 中每个元素的 goodsSpecDetailSort，升序排序；
     * 2. 如果 goodsSpecDetailSort 相等，则比较 goodsSpecDetailId，降序排序；
     * 3. 如果当前比较的元素都相等，则继续比较下一个元素；
     * 4. 如果其中一个列表比较完毕，则列表长度较短的排前面（可根据业务需求调整）。
     *
     * @param specList 待排序的列表，类型为 StoreGoodsSpecAndDetailsVO 或其子类
     */
    @Override
    public <T extends StoreGoodsSpecAndDetailsVO> void sortStoreGoodsSpecAndDetailsList(List<T> specList) {
        specList.sort((Comparator<StoreGoodsSpecAndDetailsVO>) (o1, o2) -> {
            List<StoreGoodsSpecRelationVO> list1 = o1.getStoreGoodsSpecRelationList();
            List<StoreGoodsSpecRelationVO> list2 = o2.getStoreGoodsSpecRelationList();

            // 计算两个列表的最小长度
            int minSize = Math.min(list1.size(), list2.size());
            for (int i = 0; i < minSize; i++) {
                StoreGoodsSpecRelationVO r1 = list1.get(i);
                StoreGoodsSpecRelationVO r2 = list2.get(i);

                // 首先按照 goodsSpecDetailSort 正序排序
                int sortCompare = Integer.compare(r1.getGoodsSpecDetailSort(), r2.getGoodsSpecDetailSort());
                if (sortCompare != 0) {
                    return sortCompare;
                }

                // 如果 goodsSpecDetailSort 相等，则按照 goodsSpecDetailId 倒序排序
                int idCompare = -Integer.compare(r1.getGoodsSpecDetailId(), r2.getGoodsSpecDetailId());
                if (idCompare != 0) {
                    return idCompare;
                }
                // 如果两个字段都相等，则继续比较下一组数据
            }

            // 如果前 minSize 个元素均相同，则根据列表长度排序，较短的排在前面
            return Integer.compare(list1.size(), list2.size());
        });
    }

    /**
     * 组装商品的扩展信息（包含：商品封面、商品标签等映射关系）
     *
     * @param storeGoodsOnSaleExtendVOList 在售商品信息列表
     * @param storeGoodsExpandList         该字段用于指定店铺商品需要额外查询哪些扩展信息，如：销售分组、商品品牌等。
     */
    private void assembleStoreGoodsLabelAndCoverMap(List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList,
                                                    List<StoreGoodsExpand> storeGoodsExpandList) {
        // 判断是否需要查询标签和封面
        boolean needQueryLabel = storeGoodsExpandList.contains(StoreGoodsExpand.LABEL);
        boolean needQueryCover = storeGoodsExpandList.contains(StoreGoodsExpand.COVER);
        if (!needQueryLabel && !needQueryCover) {
            return;
        }
        Map<Integer, GoodsLabelVO> storeGoodsLabelVOIdAndInfoMap = new HashMap<>();
        Map<Integer, List<GoodsPictureVO>> coverIdAndPictureVOListMap = new HashMap<>();
        // 组装标签和封面的关系
        assembleLabelAndCoverRelation(storeGoodsOnSaleExtendVOList, needQueryLabel,
                storeGoodsLabelVOIdAndInfoMap, needQueryCover, coverIdAndPictureVOListMap);
        for (StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO : storeGoodsOnSaleExtendVOList) {
            if (needQueryLabel && !Objects.isNull(storeGoodsOnSaleExtendVO.getGoodsLabel())) {
                storeGoodsOnSaleExtendVO.setLabelInfo(storeGoodsLabelVOIdAndInfoMap.get(storeGoodsOnSaleExtendVO.getGoodsLabel()));
                storeGoodsOnSaleExtendVO.setTagName(
                        storeGoodsLabelVOIdAndInfoMap.get(storeGoodsOnSaleExtendVO.getGoodsLabel()) == null ?
                                null : storeGoodsLabelVOIdAndInfoMap.get(storeGoodsOnSaleExtendVO.getGoodsLabel()).getName());
            }
            if (needQueryCover && !Objects.isNull(storeGoodsOnSaleExtendVO.getCover())) {
                storeGoodsOnSaleExtendVO.setPicture(coverIdAndPictureVOListMap.get(storeGoodsOnSaleExtendVO.getCover()));
            }
        }
    }

    /**
     * 组装标签和封面的关系
     *
     * @param storeGoodsOnSaleExtendVOList  在售商品信息列表
     * @param needQueryLabel                是否需要查询标签
     * @param storeGoodsLabelVOIdAndInfoMap 商品标签ID和商品标签信息的映射
     * @param needQueryCover                是否需要查询封面
     * @param coverIdAndPictureVOListMap    商品封面ID和商品封面信息的映射
     */
    private void assembleLabelAndCoverRelation(List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList,
                                               boolean needQueryLabel,
                                               Map<Integer, GoodsLabelVO> storeGoodsLabelVOIdAndInfoMap,
                                               boolean needQueryCover,
                                               Map<Integer, List<GoodsPictureVO>> coverIdAndPictureVOListMap) {
        Set<Integer> storeGoodsLabelIdSet = new HashSet<>();
        Set<Integer> storeGoodsCoverIdSet = new HashSet<>();
        for (StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO : storeGoodsOnSaleExtendVOList) {
            // 因为这个销售标签在销售方案中是非必填的，如果没有填写则返回null，所以这里需要判断一下
            if (!Objects.isNull(storeGoodsOnSaleExtendVO.getGoodsLabel())) {
                storeGoodsLabelIdSet.add(storeGoodsOnSaleExtendVO.getGoodsLabel());
            }
            // 同理，封面也是如此
            if (!Objects.isNull(storeGoodsOnSaleExtendVO.getCover())) {
                storeGoodsCoverIdSet.add(storeGoodsOnSaleExtendVO.getCover());
            }
        }
        if (needQueryLabel && !storeGoodsLabelIdSet.isEmpty()) {
            // 开始组装商品标签的映射关系
            storeGoodsLabelVOIdAndInfoMap.putAll(assembleStoreGoodsIdAndGoodsLabelVOMap(storeGoodsLabelIdSet));
        }
        if (needQueryCover && !storeGoodsCoverIdSet.isEmpty()) {
            // 开始组装商品封面的映射关系
            coverIdAndPictureVOListMap.putAll(assembleCoverIdAndPictureVOListMap(storeGoodsCoverIdSet));
        }
    }

    /**
     * 组装店铺商品封面ID和商品封面信息的映射
     *
     * @param storeGoodsCoverIdSet 店铺商品封面ID集合
     * @return 商品封面ID和商品封面信息的映射
     */
    private Map<Integer, List<GoodsPictureVO>> assembleCoverIdAndPictureVOListMap(Set<Integer> storeGoodsCoverIdSet) {
        Map<Integer, List<GoodsPictureVO>> coverIdAndPictureVOListMap;
        List<UploadFilesPO> uploadFilesPOList = uploadFilesMapperService.listByIds(storeGoodsCoverIdSet);
        coverIdAndPictureVOListMap = new HashMap<>();
        for (UploadFilesPO uploadFilesPO : uploadFilesPOList) {
            List<GoodsPictureVO> goodsPictureVOList = new ArrayList<>();
            goodsPictureVOList.add(goodsPictureConverter.toGoodsPictureVO(uploadFilesPO));
            coverIdAndPictureVOListMap.put(uploadFilesPO.getId(), goodsPictureVOList);
        }
        return coverIdAndPictureVOListMap;
    }

    /**
     * 组装店铺商品封面ID和商品封面信息的映射
     *
     * @param storeGoodsCoverIdSet 店铺商品封面ID集合
     * @return 商品封面ID和商品封面信息的映射
     */
    @Override
    public Map<Integer, GoodsPictureVO> assembleCoverIdAndPictureVOMap(Set<Integer> storeGoodsCoverIdSet) {
        return uploadFilesMapperService.listByIds(storeGoodsCoverIdSet).stream()
                .map(goodsPictureConverter::toGoodsPictureVO)
                .collect(Collectors.toMap(GoodsPictureVO::getId, Function.identity()));
    }

    /**
     * 组装店铺商品ID和销售分组信息的映射
     *
     * @param storeGoodsIdList 过滤后的店铺商品ID列表
     * @param channelId        渠道ID
     * @return storeGoodsIdAndSaleCategoryVOListMap 店铺商品ID和销售分组信息的映射
     */
    private Map<Integer, List<StoreGoodsOnSaleExtendVO.SaleCategoryVO>> assembleStoreGoodsIdAndSaleCategoryVOListMap(
            Collection<Integer> storeGoodsIdList, Integer channelId) {
        Map<Integer, List<StoreGoodsOnSaleExtendVO.SaleCategoryVO>> storeGoodsIdAndSaleCategoryVOListMap = new HashMap<>();
        // 根据店铺商品ID列表查询出店铺商品ID和销售分组ID的关联列表
        List<SaleCategoryAndStoreGoodsPO> saleCategoryAndStoreGoodsPOList = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, SaleCategoryAndStoreGoodsPO::getSaleCategoryId)
                .in(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, storeGoodsIdList)
                .list();
        List<Integer> saleCategoryIdList = saleCategoryAndStoreGoodsPOList.stream()
                .map(SaleCategoryAndStoreGoodsPO::getSaleCategoryId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(saleCategoryIdList)) {
            return storeGoodsIdAndSaleCategoryVOListMap;
        }
        // 根据销售分组ID列表查询出销售分组信息并转换为VO的Map
        Map<Integer, StoreGoodsOnSaleExtendVO.SaleCategoryVO> saleCategoryIdAndInfoMap =
                saleCategoryConverter.toStoreGoodsOnSaleExtendVOSaleCategoryVOList(saleCategoryMapperService.lambdaQuery()
                                .eq(SaleCategoryPO::getChannelId, channelId)
                                .in(SaleCategoryPO::getId, saleCategoryIdList).list())
                        .stream()
                        .collect(Collectors.toMap(StoreGoodsOnSaleExtendVO.SaleCategoryVO::getId, Function.identity()));
        // 组装店铺商品ID和销售分组信息的映射
        saleCategoryAndStoreGoodsPOList.stream()
                .filter(saleCategoryAndStoreGoodsPO -> saleCategoryIdAndInfoMap.containsKey(saleCategoryAndStoreGoodsPO.getSaleCategoryId()))
                .collect(Collectors.groupingBy(SaleCategoryAndStoreGoodsPO::getStoreGoodsId))
                .forEach((key, value) -> storeGoodsIdAndSaleCategoryVOListMap.put(key,
                        value.stream()
                                .map(saleCategoryAndStoreGoodsPO -> saleCategoryIdAndInfoMap.get(saleCategoryAndStoreGoodsPO.getSaleCategoryId()))
                                .toList()));
        return storeGoodsIdAndSaleCategoryVOListMap;
    }

    /**
     * 组装店铺商品ID和商品标签信息的映射
     *
     * @param storeGoodsLabelIdList 商品标签ID列表
     * @return 商品标签ID和商品标签信息的映射
     */
    private Map<Integer, GoodsLabelVO> assembleStoreGoodsIdAndGoodsLabelVOMap(Collection<Integer> storeGoodsLabelIdList) {
        return goodsLabelConverter.toGoodsLabelVOList(
                        goodsLabelMapperService.listByIds(storeGoodsLabelIdList))
                .stream().collect(Collectors.toMap(GoodsLabelVO::getId, Function.identity()));
    }

    /**
     * 根据关键字筛选商品名称、条码、自编码、首字母
     * 根据销售分组ID筛选商品
     *
     * @param storeGoodsPOList   店铺商品列表
     * @param saleCategoryIdList 销售分组ID
     */
    private void filterStoreGoodsPOListBySaleCategoryId(List<StoreGoodsPO> storeGoodsPOList,
                                                        List<Integer> saleCategoryIdList) {
        if (CollectionUtils.isEmpty(saleCategoryIdList) || CollectionUtils.isEmpty(storeGoodsPOList)) {
            return;
        }
        // 需要查询连带查询销售分组的子级分组关联的商品
        List<Integer> filterStoreGoodsIdList = queryStoreGoodsIdListBySaleCategoryIdListAndChild(saleCategoryIdList);
        storeGoodsPOList.removeIf(storeGoodsPO -> !filterStoreGoodsIdList.contains(storeGoodsPO.getId()));
    }


    /**
     * 组装默认销售方案 以及 自定义销售方案的ID和当前时间段对应的销售方案的时间段序号的映射
     *
     * @param storeSalesProgramGoodsPricePOList 销售方案商品价格信息列表
     * @param defaultStoreSalesProgramPO        默认销售方案
     * @param defaultStoreGoodsIdAndPriceMap    默认方案的 店铺商品ID 和 时段价格的映射
     * @param customStoreGoodsIdAndPriceMap     自定义方案的 店铺商品ID 和 时段价格的映射
     */
    private static void assembleSalesProgramIdAndStoreSalesProgramGoodsPriceMap(
            List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList,
            StoreSalesProgramPO defaultStoreSalesProgramPO,
            Map<Integer, BigDecimal> defaultStoreGoodsIdAndPriceMap,
            Map<Integer, BigDecimal> customStoreGoodsIdAndPriceMap) {
        for (StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO : storeSalesProgramGoodsPricePOList) {
            // 组装默认方案的 店铺商品ID 和 时段价格的映射
            if (storeSalesProgramGoodsPricePO.getStoreSalesProgramId().equals(defaultStoreSalesProgramPO.getId())) {
                defaultStoreGoodsIdAndPriceMap.put(storeSalesProgramGoodsPricePO.getStoreSalesProgramGoodsId(),
                        storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute());
            } else {
                // 组装自定义方案的 店铺商品ID 和 时段价格的映射
                customStoreGoodsIdAndPriceMap.put(storeSalesProgramGoodsPricePO.getStoreSalesProgramGoodsId(),
                        storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute());
            }
        }
    }

    /**
     * 组装销售方案的ID和销售方案商品信息的映射，用于后续的在售商品信息的组装 （默认和自定义一并组装）
     *
     * @param storeSalesProgramGoodsPOList      销售方案商品信息列表
     * @param defaultStoreSalesProgramPO        默认销售方案
     * @param defaultStoreGoodsIdAndInfoListMap 默认方案的 店铺商品ID 和 默认销售方案信息的 映射
     * @param customStoreGoodsIdAndInfoListMap  自定义方案的 店铺商品ID 和 自定义销售方案信息的 映射
     */
    private static void assembleSalesProgramIdAndStoreSalesProgramGoodsInfoMap(
            List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList,
            StoreSalesProgramPO defaultStoreSalesProgramPO,
            Map<Integer, List<StoreSalesProgramGoodsPO>> defaultStoreGoodsIdAndInfoListMap,
            Map<Integer, List<StoreSalesProgramGoodsPO>> customStoreGoodsIdAndInfoListMap) {
        for (StoreSalesProgramGoodsPO storeSalesProgramGoodsPO : storeSalesProgramGoodsPOList) {
            if (storeSalesProgramGoodsPO.getStoreSalesProgramId().equals(defaultStoreSalesProgramPO.getId())) {
                // 组装默认方案的 店铺商品ID 和 默认销售方案信息的 映射
                defaultStoreGoodsIdAndInfoListMap
                        .computeIfAbsent(storeSalesProgramGoodsPO.getGoodsId(), k -> new ArrayList<>())
                        .add(storeSalesProgramGoodsPO);
            } else {
                // 组装自定义方案的 店铺商品ID 和 自定义销售方案信息的 映射
                customStoreGoodsIdAndInfoListMap
                        .computeIfAbsent(storeSalesProgramGoodsPO.getGoodsId(), k -> new ArrayList<>())
                        .add(storeSalesProgramGoodsPO);
            }
        }
    }

    /**
     * 根据自定义销售方案的ID和当前时间段对应的销售方案的时间段序号的映射，查询出所有的销售方案商品价格信息
     *
     * @param customStoreSalesProgramIdAndTimeSerialNumberPair 自定义销售方案和当前时间段对应的销售方案的时间段序号的映射
     * @param defaultStoreSalesProgramPO                       默认销售方案
     * @param storeGoodsIdList                                 店铺商品ID列表
     * @return 销售方案商品价格信息列表
     */
    private List<StoreSalesProgramGoodsPricePO> getStoreSalesProgramGoodsPricePOByStoreSalesProgramIdListAndSerialNumber(
            Pair<StoreSalesProgramPO, Integer> customStoreSalesProgramIdAndTimeSerialNumberPair,
            StoreSalesProgramPO defaultStoreSalesProgramPO, List<Integer> storeGoodsIdList) {
        List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList;
        if (customStoreSalesProgramIdAndTimeSerialNumberPair == null) {
            // 没有自定义方案，则返回默认方案的时段价格信息
            storeSalesProgramGoodsPricePOList = storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                    .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, defaultStoreSalesProgramPO.getId())
                    .in(StoreSalesProgramGoodsPricePO::getGoodsId, storeGoodsIdList)
                    .eq(StoreSalesProgramGoodsPricePO::getSerialNumber,
                            defaultStoreSalesProgramPO.getTimeSection().get(0).getSerialNumber())
                    .list();
        } else {
            // 有自定义方案，则返回默认方案的时段价格信息和自定义方案的时段价格信息
            storeSalesProgramGoodsPricePOList = storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                    .and(wrapper -> wrapper
                            .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, defaultStoreSalesProgramPO.getId())
                            .eq(StoreSalesProgramGoodsPricePO::getSerialNumber,
                                    defaultStoreSalesProgramPO.getTimeSection().get(0).getSerialNumber()))
                    .or(wrapper -> wrapper
                            .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, customStoreSalesProgramIdAndTimeSerialNumberPair.getKey().getId())
                            .eq(StoreSalesProgramGoodsPricePO::getSerialNumber, customStoreSalesProgramIdAndTimeSerialNumberPair.getValue()))
                    .list();
        }
        return storeSalesProgramGoodsPricePOList;
    }

    /**
     * 根据销售方案的ID列表查询出所有的销售方案商品信息
     *
     * @param defaultStoreSalesProgramPO                       默认销售方案
     * @param customStoreSalesProgramIdAndTimeSerialNumberPair 自定义销售方案和当前时间段对应的销售方案的时间段序号的映射
     * @param storeGoodsIdList                                 店铺商品ID列表
     * @return 销售方案商品信息列表
     */
    private List<StoreSalesProgramGoodsPO> getStoreSalesProgramGoodsPOListBySalesProgramIdList(
            StoreSalesProgramPO defaultStoreSalesProgramPO,
            Pair<StoreSalesProgramPO, Integer> customStoreSalesProgramIdAndTimeSerialNumberPair,
            List<Integer> storeGoodsIdList) {
        // 获取当前时刻生效的销售方案的Id（包含默认方案和自定义方案）
        List<Integer> storeSalesProgramOnSaleIdList = new ArrayList<>();
        storeSalesProgramOnSaleIdList.add(defaultStoreSalesProgramPO.getId());
        if (customStoreSalesProgramIdAndTimeSerialNumberPair != null) {
            storeSalesProgramOnSaleIdList.add(customStoreSalesProgramIdAndTimeSerialNumberPair.getKey().getId());
        }
        return storeSalesProgramGoodsMapperService.lambdaQuery()
                .in(StoreSalesProgramGoodsPO::getStoreSalesProgramId, storeSalesProgramOnSaleIdList)
                .in(CollectionUtils.isNotEmpty(storeGoodsIdList), StoreSalesProgramGoodsPO::getGoodsId, storeGoodsIdList)
                .list();
    }

    /**
     * 根据店铺ID和渠道ID查询出默认销售方案 以及  自定义销售方案的ID和当前时间段对应的销售方案的时间段序号的映射关系Pair
     * 结构如下：
     * Pair(defaultStoreSalesProgramPO, Pair(customStoreSalesProgramId, customStoreSalesProgramTimeSerialNumber))
     *
     * @param storeId   门店ID
     * @param channelId 渠道ID
     * @return 默认销售方案 以及  自定义销售方案的ID和当前时间段对应的销售方案的时间段序号的映射关系Pair
     */
    @Override
    public Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> queryDefaultSalesProgramAndCustomSalesProgramAndTimeSerialNumberPair(
            Integer storeId, Integer channelId) {
        // 获取当前日期的所有店铺销售方案(启用且在当前日期内,包含了过期、进行中、未开始的方案)
        List<StoreSalesProgramPO> allStoreSalesProgramPOList = storeSalesProgramMapperService.lambdaQuery()
                .eq(Objects.nonNull(storeId), StoreSalesProgramPO::getStoreId, storeId)
                .eq(StoreSalesProgramPO::getChannelId, channelId)
                .eq(StoreSalesProgramPO::getIsEnable, StoreSalesProgramStatus.ENABLE.isValue())
                // 暂时只考虑方案的时间段类型是每天的
                .eq(StoreSalesProgramPO::getCycleType, SalesProgramPeriodType.DAILY.getValue())
                .list();
        if (CollectionUtils.isEmpty(allStoreSalesProgramPOList)) {
            log.error("门店->{}, 通道->{}, 没有可用的门店销售方案", storeId, channelId);
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, NO_SALE_PROGRAM);
        }
        StoreSalesProgramPO defaultStoreSalesProgramPO =
                allStoreSalesProgramPOList.stream().filter(StoreSalesProgramPO::getIsDefault).findFirst().orElse(null);
        if (defaultStoreSalesProgramPO == null) {
            log.error("门店->{}, 通道->{}, 没有默认的门店销售方案", storeId, channelId);
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "没有默认的门店销售方案");
        }
        List<StoreSalesProgramPO> customStoreSalesProgramPOList = allStoreSalesProgramPOList.stream()
                .filter(storeSalesProgramPO -> !storeSalesProgramPO.getIsDefault())
                .toList();
        // 获取当前时间
        LocalTime currentLocalTime = DateUtils.getCurrentLocalTimeWithoutSeconds();
        // 返回的结构体：
        // Pair(defaultStoreSalesProgramPO, Pair(customStoreSalesProgramId, customStoreSalesProgramTimeSerialNumber))
        Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair = Pair.of(defaultStoreSalesProgramPO, null);
        if (CollectionUtils.isNotEmpty(customStoreSalesProgramPOList)) {
            for (StoreSalesProgramPO customStoreSalesProgramPO : customStoreSalesProgramPOList) {
                if (CollectionUtils.isEmpty(customStoreSalesProgramPO.getTimeSection())) {
                    continue;
                }
                // 判断是否在当前时间段内
                // 在时间段内就返回该方案的ID和当前时间段对应的序号 否则返回空
                Pair<StoreSalesProgramPO, Integer> storeSalesProgramPOIntegerPair = filterAvailableDailySalesPrograms(customStoreSalesProgramPO, currentLocalTime);
                if (storeSalesProgramPOIntegerPair != null) {
                    return Pair.of(defaultStoreSalesProgramPO, storeSalesProgramPOIntegerPair);
                }
            }
        }
        return resultPair;
    }

    @Override
    public List<StoreGoodsPO> queryStoreGoodsForAvailableForSaleWeighList(
            QueryStoreGoodsForAvailableForSaleWeighListBO queryStoreGoodsForAvailableForSaleWeighListBO) {
        return storeGoodsMapperService.queryStoreGoodsForAvailableForSaleWeighList(
                queryStoreGoodsForAvailableForSaleWeighListBO.getStoreId(),
                queryStoreGoodsForAvailableForSaleWeighListBO.getChannelId(),
                queryStoreGoodsForAvailableForSaleWeighListBO.getBarcode(),
                queryStoreGoodsForAvailableForSaleWeighListBO.getStoreGoodsIdList(),
                List.of(GoodsValuationMethod.COUNTING.getId(), GoodsValuationMethod.WEIGHING.getId()));
    }

    @Override
    public void assembleGoodsNameAndSalePriceAndPictureForWeighList(
            List<StoreGoodsExtendVO> storeGoodsExtendVOList, Integer storeId, Integer channelId) {
        List<StoreSalesProgramPO> allStoreSalesProgramPOList = storeSalesProgramMapperService.lambdaQuery()
                .eq(StoreSalesProgramPO::getStoreId, storeId)
                .eq(StoreSalesProgramPO::getChannelId, channelId)
                .eq(StoreSalesProgramPO::getIsEnable, StoreSalesProgramStatus.ENABLE.isValue())
                .list();
        if (CollectionUtils.isEmpty(allStoreSalesProgramPOList)) {
            log.error("门店->{}, 通道->{}, 没有可用的门店默认销售方案", storeId, channelId);
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "没有可用的门店默认销售方案");
        }
        if (allStoreSalesProgramPOList.size() == 1) {
            // 只有一个默认销售方案的情况
            assembleStoreGoodsSaleInfoForOnlyDefaultSalesProgram(storeGoodsExtendVOList, allStoreSalesProgramPOList);
        } else {
            // 即存在默认销售方案又存在自定义销售方案的情况
            assembleStoreGoodsSaleInfoForMultipleSalesProgram(storeGoodsExtendVOList, allStoreSalesProgramPOList);
        }
    }

    @Override
    public void assembleGoodsNameAndSalePriceAndPictureForWeigh(StoreGoodsExtendVO storeGoodsExtendVO,
                                                                Integer storeId, Integer channelId) {
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeSalesProgramGoodsMapperService.lambdaQuery()
                .eq(StoreSalesProgramGoodsPO::getGoodsId, storeGoodsExtendVO.getId()).list();
        if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
            return;
        }
        Map<Integer, StoreSalesProgramGoodsPO> storeSalesProgramIdAndInfoMap = storeSalesProgramGoodsPOList.stream()
                .collect(Collectors.toMap(StoreSalesProgramGoodsPO::getStoreSalesProgramId, Function.identity()));
        List<StoreSalesProgramPO> allStoreSalesProgramPOList = storeSalesProgramMapperService.listByIds(storeSalesProgramIdAndInfoMap.keySet());
        StoreSalesProgramPO defaultStoreSalesProgramPO = allStoreSalesProgramPOList.stream()
                .filter(StoreSalesProgramPO::getIsDefault)
                .findFirst().orElse(null);
        List<StoreSalesProgramPO> customStoreSalesProgramPOList = allStoreSalesProgramPOList.stream()
                .filter(storeSalesProgramPO -> !storeSalesProgramPO.getIsDefault())
                .toList();
        if (CollectionUtils.isEmpty(customStoreSalesProgramPOList) && defaultStoreSalesProgramPO != null) {
            // 当没有自定义方案时，即只有默认方案
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramIdAndInfoMap.get(defaultStoreSalesProgramPO.getId());
            StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO = storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                    .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, defaultStoreSalesProgramPO.getId())
                    .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, storeSalesProgramGoodsPO.getId())
                    .eq(StoreSalesProgramGoodsPricePO::getSerialNumber,
                            defaultStoreSalesProgramPO.getTimeSection().get(0).getSerialNumber()).one();
            storeGoodsExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
            storeGoodsExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
            storeGoodsExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
            storeGoodsExtendVO.setSellingPrice(storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute()
                    .setScale(2, RoundingMode.HALF_UP).toString());
        } else {
            // 当存在自定义方案时，即存在默认方案和自定义方案
            // 获取当前生效的时段信息（包含销售方案ID、时段序号、时段开始时间、时段结束时间），用于后续给商品组装销售价格使用
            List<StoreSalesProgramSectionBO> concurrentStoreSalesProgramSectionBOList = new ArrayList<>();
            // 获取当前时间之前的时段信息（包含销售方案ID、时段序号、时段开始时间、时段结束时间），用于后续给商品组装销售价格使用
            List<StoreSalesProgramSectionBO> beforeStoreSalesProgramSectionBOList = new ArrayList<>();
            // 获取当前时间之后的时段信息（包含销售方案ID、时段序号、时段开始时间、时段结束时间），用于后续给商品组装销售价格使用
            List<StoreSalesProgramSectionBO> afterStoreSalesProgramSectionBOList = new ArrayList<>();
            // 对时段信息进行分类，当前时间段、之前时段、之后时段
            classifyTimeSections(customStoreSalesProgramPOList, concurrentStoreSalesProgramSectionBOList,
                    beforeStoreSalesProgramSectionBOList, afterStoreSalesProgramSectionBOList);
            StoreSalesProgramSectionBO storeSalesProgramSectionBO;
            // 判断取哪个方案是需要按照顺序： 当前生效的自定义方案 > 默认方案 > 之后生效的自定义方案 > 之前生效的自定义方案
            if (CollectionUtils.isNotEmpty(concurrentStoreSalesProgramSectionBOList)) {
                // 首先需要判断在售方案，如果在售方案则需要先看在售方案中是否有当前商品，如果有则获取该商品的售卖信息，如果没有则往下执行
                storeSalesProgramSectionBO = concurrentStoreSalesProgramSectionBOList.get(0);
            } else if (defaultStoreSalesProgramPO != null) {
                StoreSalesProgramPO.Section section = defaultStoreSalesProgramPO.getTimeSection().get(0);
                storeSalesProgramSectionBO = StoreSalesProgramSectionBO.builder()
                        .storeSalesProgramId(defaultStoreSalesProgramPO.getId())
                        .start(section.getStart())
                        .end(section.getEnd())
                        .serialNumber(section.getSerialNumber())
                        .build();
            } else if (CollectionUtils.isNotEmpty(afterStoreSalesProgramSectionBOList)) {
                // 首先需要判断当前时间之后的自定义方案，如果当前时间之后的自定义方案则需要先看当前时间之后的自定义方案中是否有当前商品，如果有则获取该商品的售卖信息，如果没有则往下执行
                storeSalesProgramSectionBO = afterStoreSalesProgramSectionBOList.get(0);
            } else if (CollectionUtils.isNotEmpty(beforeStoreSalesProgramSectionBOList)) {
                // 首先需要判断当前时间之前的自定义方案，如果当前时间之前的自定义方案则需要先看当前时间之前的自定义方案中是否有当前商品，如果有则获取该商品的售卖信息
                storeSalesProgramSectionBO = afterStoreSalesProgramSectionBOList.get(afterStoreSalesProgramSectionBOList.size() - 1);
            } else {
                throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, NO_SALE_PROGRAM);
            }
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramIdAndInfoMap.get(storeSalesProgramSectionBO.getStoreSalesProgramId());
            StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO = storeSalesProgramGoodsPriceMapperService.lambdaQuery()
                    .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, storeSalesProgramSectionBO.getStoreSalesProgramId())
                    .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramGoodsId, storeSalesProgramGoodsPO.getId())
                    .eq(StoreSalesProgramGoodsPricePO::getSerialNumber, storeSalesProgramSectionBO.getSerialNumber()).one();
            storeGoodsExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
            storeGoodsExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
            storeGoodsExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
            storeGoodsExtendVO.setSellingPrice(storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute()
                    .setScale(2, RoundingMode.HALF_UP).toString());
        }

    }

    @Override
    public void assembleGoodsExpandInfo(List<StoreGoodsExtendVO> storeGoodsExtendVOList,
                                        List<StoreGoodsExpand> storeGoodsExpandList, Integer storeId, Integer channelId,
                                        Integer scaleType) {
        StoreGoodsAnalysisContextImpl storeGoodsAnalysisContext = StoreGoodsAnalysisContextImpl.builder()
                .setStoreGoodsExpandQueryType(StoreGoodsExpandQueryType.STORE_GOODS_SINGLE_TIMESLOT_SALES_SCHEME)
                .setStoreGoodsExtendVOList(storeGoodsExtendVOList)
                .setStoreGoodsExpandList(storeGoodsExpandList)
                .setStoreId(storeId)
                .setChannelId(channelId)
                .setScaleType(scaleType)
                .setGoodsLabelConverter(goodsLabelConverter)
                .setSaleCategoryConverter(saleCategoryConverter)
                .setSaleCategoryAndStoreGoodsMapperService(saleCategoryAndStoreGoodsMapperService)
                .setGoodsBrandMapperService(goodsBrandMapperService)
                .setGoodsLabelMapperService(goodsLabelMapperService)
                .setUploadFilesMapperService(uploadFilesMapperService)
                .setSaleCategoryMapperService(saleCategoryMapperService)
                .setGoodsBrandConverter(goodsBrandConverter)
                .setGoodsPictureConverter(goodsPictureConverter)
                .setGoodsScaleCodeConverter(goodsScaleCodeConverter)
                .setStoreSalesProgramConverter(storeSalesProgramConverter)
                .setSaleUnitMeasurementMapperService(saleUnitMeasurementMapperService)
                .setGoodsScaleCodeMapperService(goodsScaleCodeMapperService)
                .setStoreSalesProgramMapperService(storeSalesProgramMapperService)
                .setStoreSalesProgramGoodsMapperService(storeSalesProgramGoodsMapperService)
                .setStoreSalesProgramGoodsPriceMapperService(storeSalesProgramGoodsPriceMapperService)
                .build();
        storeGoodsExtendVOList.forEach(storeGoodsExtendVO -> assembleGoodsExpandInfo(storeGoodsExtendVO, storeGoodsAnalysisContext));
    }

    @Override
    public void assembleGoodsExpandInfoForPrintLabel(List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList, List<StoreGoodsExpand> storeGoodsExpandList, Integer storeId, Integer channelId, Integer scaleType) {
        StoreGoodsAnalysisContextImpl storeGoodsAnalysisContext = StoreGoodsAnalysisContextImpl.builder()
                .setStoreGoodsExpandQueryType(StoreGoodsExpandQueryType.GROUP_BY_STORE_GOODS_MULTI_PERIOD)
                .setStoreGoodsPrintLabelExtendVOList(storeGoodsPrintLabelExtendVOList)
                .setStoreGoodsExpandList(storeGoodsExpandList)
                .setStoreId(storeId)
                .setChannelId(channelId)
                .setScaleType(scaleType)
                .setGoodsLabelConverter(goodsLabelConverter)
                .setSaleCategoryConverter(saleCategoryConverter)
                .setSaleCategoryAndStoreGoodsMapperService(saleCategoryAndStoreGoodsMapperService)
                .setGoodsBrandMapperService(goodsBrandMapperService)
                .setGoodsLabelMapperService(goodsLabelMapperService)
                .setUploadFilesMapperService(uploadFilesMapperService)
                .setSaleCategoryMapperService(saleCategoryMapperService)
                .setGoodsBrandConverter(goodsBrandConverter)
                .setGoodsPictureConverter(goodsPictureConverter)
                .setGoodsScaleCodeConverter(goodsScaleCodeConverter)
                .setStoreSalesProgramConverter(storeSalesProgramConverter)
                .setSaleUnitMeasurementMapperService(saleUnitMeasurementMapperService)
                .setGoodsScaleCodeMapperService(goodsScaleCodeMapperService)
                .setStoreSalesProgramMapperService(storeSalesProgramMapperService)
                .setStoreSalesProgramGoodsMapperService(storeSalesProgramGoodsMapperService)
                .setStoreSalesProgramGoodsPriceMapperService(storeSalesProgramGoodsPriceMapperService)
                .build();
        storeGoodsPrintLabelExtendVOList.forEach(storeGoodsExtendVO ->
                assembleGoodsPrintLabelExpandInfo(storeGoodsExtendVO, storeGoodsAnalysisContext));
    }

    @Override
    public void filterStoreGoodsExtendVOListByKeywordsForSaleNameAndPinyinAndCustomCode(
            List<StoreGoodsExtendVO> storeGoodsExtendVOList, String keywords) {
        if (StringUtils.isBlank(keywords)) {
            return;
        }
        String finalKeywords = keywords.toLowerCase();
        // 条形码过滤
        List<Integer> barcodeByKeywords = goodsPackageSkuService.filterBarcodeByKeywords(storeGoodsExtendVOList, keywords);
        storeGoodsExtendVOList.removeIf(storeGoodsExtendVO ->
                !(storeGoodsExtendVO.getGoodsName().toLowerCase().contains(finalKeywords)
                        || filterScaleCustomCode(storeGoodsExtendVO.getGoodsScaleCodeList(), finalKeywords)
                        || storeGoodsExtendVO.getPinyinCode().contains(finalKeywords)
                        || (!barcodeByKeywords.isEmpty() && barcodeByKeywords.contains(storeGoodsExtendVO.getId()))));
    }

    /**
     * 过滤秤内自编码
     *
     * @param goodsScaleCodeList 秤内码信息列表
     * @param keywords           关键字
     * @return 是否匹配成功
     */
    private boolean filterScaleCustomCode(List<StoreGoodsBaseExtendVO.GoodsScaleCodeVO> goodsScaleCodeList, String keywords) {
        if (CollUtil.isNotEmpty(goodsScaleCodeList)) {
            for (StoreGoodsBaseExtendVO.GoodsScaleCodeVO goodsScaleCodeVO : goodsScaleCodeList) {
                if (StringUtils.isNotEmpty(goodsScaleCodeVO.getScaleCustomCode()) &&
                        goodsScaleCodeVO.getScaleCustomCode().contains(keywords)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void filterStoreGoodsListBySaleCategoryIdListOneself(List<StoreGoodsPO> storeGoodsPOList, List<Integer> saleCategoryIdList) {
        if (CollectionUtils.isEmpty(saleCategoryIdList) || CollectionUtils.isEmpty(storeGoodsPOList)) {
            return;
        }
        List<Integer> storeGoodsIdList = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                .in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, saleCategoryIdList)
                .list().stream().map(SaleCategoryAndStoreGoodsPO::getStoreGoodsId).distinct().toList();
        storeGoodsPOList.removeIf(storeGoodsPO -> !storeGoodsIdList.contains(storeGoodsPO.getId()));
    }

    @Override
    public String queryOrderRefundInboundNumberInfo(List<String> orderRefundNumberList) {
        String url = inventoryServiceIntranetHost + UrlPathConstants.SELECT_GOODS_RECEIPT_NOTE_LIST;
        HttpHeaders headers = new HttpHeaders();
        headers.set(COMPANY_ID_HEADER_KEY, DataSourceContextHolder.getTenant().split(DATASOURCE_PREFIX)[1]);
        HttpEntity<Map<String, List<String>>> requestEntity = new HttpEntity<>(Map.of(REQUEST_PARAM_REFUND_NUMBER, orderRefundNumberList), headers);
        log.info("查询退款单对应的入库单信息, URL：{}, requestEntity：{}", url, requestEntity);
        ResponseEntity<String> postForEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("查询退款单对应的入库单信息：{}", postForEntity);
        return postForEntity.getBody();
    }

    @Override
    public Integer querySaleChannelPOSId() {
        PlatformSalesChannelPO pos = platformSalesChannelMapperService.lambdaQuery()
                .select(PlatformSalesChannelPO::getId)
                .eq(PlatformSalesChannelPO::getIsEnable, StoreSalesProgramStatus.ENABLE.isValue())
                .eq(PlatformSalesChannelPO::getChannelName, "POS")
                .one();
        return pos == null ? null : pos.getId();
    }

    @Override
    public List<StoreGoodsExtendVO> queryStorePosGoodsAvailableForSaleList(QueryStorePosGoodsAvailableForSaleBO queryBO,
                                                                           StoreSalesProgramPO defaultStoreSalesProgramPO,
                                                                           Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndSalesProgramGoodsListMap) {
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList =
                storeSalesProgramGoodsMapperService.lambdaQuery()
                        .eq(StoreSalesProgramGoodsPO::getStoreSalesProgramId, defaultStoreSalesProgramPO.getId()).list();
        if (CollectionUtils.isEmpty(storeSalesProgramGoodsPOList)) {
            return Collections.emptyList();
        }
        List<Integer> storeGoodsIdList = new ArrayList<>();
        storeSalesProgramGoodsPOList.forEach(storeSalesProgramGoodsPO -> {
            storeGoodsIdList.add(storeSalesProgramGoodsPO.getGoodsId());
            storeGoodsIdAndSalesProgramGoodsListMap.computeIfAbsent(storeSalesProgramGoodsPO.getGoodsId(),
                    k -> new ArrayList<>()).add(storeSalesProgramGoodsPO);
        });
        List<StoreGoodsExtendVO> storeGoodsExtendVOList = storeGoodsConverter.toStoreGoodsExtendVOList(storeGoodsMapperService.lambdaQuery()
                .in(StoreGoodsPO::getId, storeGoodsIdList)
                .eq(StoreGoodsPO::getStatus, StoreGoodsStatus.ENABLE.getValue())
                .list());
        if (CollectionUtils.isEmpty(storeGoodsExtendVOList)) {
            return Collections.emptyList();
        }
        // 组装售卖信息
        storeGoodsExtendVOList.forEach(storeGoodsExtendVO -> {
            if (!storeGoodsIdAndSalesProgramGoodsListMap.containsKey(storeGoodsExtendVO.getId())) {
                return;
            }
            // 目前同一商品的所有SKU的售卖名称、标签、封面都是一样的，即取第一条记录即可
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeGoodsIdAndSalesProgramGoodsListMap.get(storeGoodsExtendVO.getId()).get(0);
            storeGoodsExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
            storeGoodsExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
            storeGoodsExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
        });
        filterStoreGoodsExtendVOForPosStore(storeGoodsExtendVOList, queryBO, storeGoodsIdList);
        if (CollectionUtils.isEmpty(storeGoodsExtendVOList)) {
            return Collections.emptyList();
        }
        return storeGoodsExtendVOList;
    }

    /**
     * 根据关键字、销售分组ID、商品计价方式、商品来源、品牌以及标签等过滤POS渠道的可售商品列表
     *
     * @param storeGoodsExtendVOList 商品列表
     * @param queryBO                查询条件
     * @param queryBO                查询条件
     * @param allStoreGoodsIdList    商品ID列表
     */
    private void filterStoreGoodsExtendVOForPosStore(List<StoreGoodsExtendVO> storeGoodsExtendVOList,
                                                     QueryStorePosGoodsAvailableForSaleBO queryBO,
                                                     List<Integer> allStoreGoodsIdList) {
        if (StringUtils.isNotEmpty(queryBO.getKeywords())) {
            filterStoreGoodsExtendVOForKeywords(storeGoodsExtendVOList, queryBO.getKeywords(), allStoreGoodsIdList);
        }
        if (queryBO.getSaleCategoryId() != null) {
            // 查询指定渠道的ID及其所有子级的ID
            List<Integer> saleCategoryIdList = saleCategoryMapperService.lambdaQuery().eq(SaleCategoryPO::getChannelId, queryBO.getChannelId()).and(
                    wrapper -> wrapper.eq(SaleCategoryPO::getId, queryBO.getSaleCategoryId()).or().eq(SaleCategoryPO::getParentId, queryBO.getSaleCategoryId())
            ).list().stream().map(SaleCategoryPO::getId).distinct().toList();
            List<Integer> storeGoodsIdList = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                    .select(SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                    .in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, saleCategoryIdList)
                    .list().stream()
                    .map(SaleCategoryAndStoreGoodsPO::getStoreGoodsId).toList();
            storeGoodsExtendVOList.removeIf(storeGoodsExtendVO -> !storeGoodsIdList.contains(storeGoodsExtendVO.getId()));
        }
        if (queryBO.getStoreSource() != null) {
            if (queryBO.getStoreSource() == StoreSource.STORE_GOODS) {
                storeGoodsExtendVOList.removeIf(storeGoodsExtendVO -> !Objects.isNull(storeGoodsExtendVO.getGoodsId()));
            } else {
                storeGoodsExtendVOList.removeIf(storeGoodsExtendVO -> Objects.isNull(storeGoodsExtendVO.getGoodsId()));
            }
        }
        if (CollectionUtils.isNotEmpty(queryBO.getGoodsValuationMethodList())) {
            List<Integer> goodsValuationMethodIdList = queryBO.getGoodsValuationMethodList().stream()
                    .map(GoodsValuationMethod::getId).distinct().toList();
            // 如果传入的是全部的，则不需要过滤
            if (goodsValuationMethodIdList.size() != GoodsValuationMethod.values().length) {
                storeGoodsExtendVOList.removeIf(storeGoodsExtendVO -> !goodsValuationMethodIdList.contains(storeGoodsExtendVO.getValuationMethod()));
            }
        }
        if (CollectionUtils.isNotEmpty(queryBO.getBrandIdList())) {
            storeGoodsExtendVOList.removeIf(storeGoodsExtendVO -> !queryBO.getBrandIdList().contains(storeGoodsExtendVO.getBrand()));
        }
        if (CollectionUtils.isNotEmpty(queryBO.getCoverIdList())) {
            storeGoodsExtendVOList.removeIf(storeGoodsExtendVO -> !queryBO.getCoverIdList().contains(storeGoodsExtendVO.getCover()));
        }
        if (Objects.nonNull(queryBO.getGoodsSpec())) {
            storeGoodsExtendVOList.removeIf(storeGoodsExtendVO -> !Objects.equals(queryBO.getGoodsSpec(), storeGoodsExtendVO.getGoodsSpec()));
        }
    }

    /**
     * 根据关键字过滤商品的名称、条码、SPU编码以及拼音码
     *
     * @param storeGoodsExtendVOList 商品列表
     * @param keywords               关键字
     * @param keywords               关键字
     * @param allStoreGoodsIdList   商品ID列表
     */
    private void filterStoreGoodsExtendVOForKeywords(List<StoreGoodsExtendVO> storeGoodsExtendVOList, String keywords, List<Integer> allStoreGoodsIdList) {
        if (StringUtils.isEmpty(keywords)) {
            return;
        }
        String finalKeywords = keywords.toLowerCase();
        List<Integer> barcodeMatchSuccessfulStoreGoodsIdList = goodsPackageSkuMapperService.lambdaQuery()
                .select(GoodsPackageSkuPO::getStoreGoodsId)
                .in(GoodsPackageSkuPO::getStoreGoodsId, allStoreGoodsIdList)
                .like(GoodsPackageSkuPO::getBarcode, CommonUtils.assembleSqlLikeString(finalKeywords))
                .list().stream().map(GoodsPackageSkuPO::getStoreGoodsId).toList();
        Iterator<StoreGoodsExtendVO> iterator = storeGoodsExtendVOList.iterator();
        while (iterator.hasNext()) {
            StoreGoodsExtendVO storeGoodsExtendVO = iterator.next();
            if (barcodeMatchSuccessfulStoreGoodsIdList.contains(storeGoodsExtendVO.getId())) {
                continue;
            }
            if (!filterStoreGoodsExtendVOForKeywords(storeGoodsExtendVO, finalKeywords)) {
                iterator.remove();
            }
        }
    }

    /**
     * 根据关键字过滤商品的名称、条码、SPU编码以及拼音码
     *
     * @param storeGoodsExtendVO 商品
     * @param keywords           关键字
     * @return 是否过滤成功
     */
    private boolean filterStoreGoodsExtendVOForKeywords(StoreGoodsExtendVO storeGoodsExtendVO, String keywords) {
        return storeGoodsExtendVO.getGoodsName().toLowerCase().contains(keywords) ||
                storeGoodsExtendVO.getSpuCode().toLowerCase().contains(keywords) ||
                storeGoodsExtendVO.getPinyinCode().toLowerCase().contains(keywords);
    }

    /**
     * 组装商品扩展基础信息 （多时段）
     *
     * @param storeGoodsPrintLabelExtendVO 商品扩展信息
     * @param storeGoodsAnalysisContext    商品分析上下文
     */
    private static void assembleGoodsPrintLabelExpandInfo(StoreGoodsPrintLabelExtendVO storeGoodsPrintLabelExtendVO,
                                                          StoreGoodsAnalysisContextImpl storeGoodsAnalysisContext) {
        // 组装商品扩展基础信息
        assembleGoodsExpandBaseInfo(storeGoodsPrintLabelExtendVO, storeGoodsAnalysisContext);
        // 销售商品策略数据信息
        List<StoreGoodsPrintLabelExtendVO.StoreSalesProgramGoodVO> programGoodList = storeGoodsPrintLabelExtendVO.getStoreSalesProgramGoodList();
        if (Objects.nonNull(programGoodList)) {
            if (storeGoodsAnalysisContext.getNeedQueryCoverFlag()) {
                for (StoreGoodsPrintLabelExtendVO.StoreSalesProgramGoodVO storeSalesProgramGoodVO : programGoodList) {
                    storeSalesProgramGoodVO.setPicture(getPicture(storeGoodsAnalysisContext, storeSalesProgramGoodVO.getCoverPicture()));
                }
            }
            if (storeGoodsAnalysisContext.getNeedQueryLabelFlag()) {
                for (StoreGoodsPrintLabelExtendVO.StoreSalesProgramGoodVO storeSalesProgramGoodVO : programGoodList) {
                    storeSalesProgramGoodVO.setLabelInfo(storeGoodsAnalysisContext.getStoreLabelIdAndInfoMap().get(storeSalesProgramGoodVO.getLabel()));
                }
            }
        } else {
            if (storeGoodsAnalysisContext.getNeedQueryCoverFlag()) {
                storeGoodsPrintLabelExtendVO.setPicture(getPicture(storeGoodsAnalysisContext, storeGoodsPrintLabelExtendVO.getCoverPicture()));
            }
            if (storeGoodsAnalysisContext.getNeedQueryLabelFlag()) {
                storeGoodsPrintLabelExtendVO.setLabelInfo(storeGoodsAnalysisContext.getStoreLabelIdAndInfoMap().get(storeGoodsPrintLabelExtendVO.getLabel()));
            }
        }
    }

    /**
     * 获取图片信息
     *
     * @param storeGoodsAnalysisContext 店铺商品分组组装上下文实现类
     * @param coverPicture              图片id
     * @return 查询结果
     */
    private static List<GoodsPictureVO> getPicture(StoreGoodsAnalysisContextImpl storeGoodsAnalysisContext, Integer coverPicture) {
        GoodsPictureVO goodsPictureVO = storeGoodsAnalysisContext.getStoreCoverIdAndInfoMap().get(coverPicture);
        return Objects.isNull(goodsPictureVO) ? List.of() : List.of(goodsPictureVO);
    }

    /**
     * 组装商品扩展基础信息 （单时段）
     *
     * @param storeGoodsExtendVO        商品扩展信息
     * @param storeGoodsAnalysisContext 商品分析上下文
     */
    private static void assembleGoodsExpandInfo(StoreGoodsExtendVO storeGoodsExtendVO, StoreGoodsAnalysisContextImpl storeGoodsAnalysisContext) {
        assembleGoodsExpandBaseInfo(storeGoodsExtendVO, storeGoodsAnalysisContext);
        if (storeGoodsAnalysisContext.getNeedQueryCoverFlag()) {
            storeGoodsExtendVO.setPicture(
                    storeGoodsAnalysisContext.getStoreCoverIdAndInfoMap().get(storeGoodsExtendVO.getCover()) == null ?
                            List.of() : List.of(storeGoodsAnalysisContext.getStoreCoverIdAndInfoMap().get(storeGoodsExtendVO.getCover())));
        }
        if (storeGoodsAnalysisContext.getNeedQueryLabelFlag()) {
            storeGoodsExtendVO.setLabelInfo(storeGoodsAnalysisContext.getStoreLabelIdAndInfoMap().get(storeGoodsExtendVO.getGoodsLabel()));
        }
        if (storeGoodsAnalysisContext.getNeedQueryTimeSectionFlag()) {
            storeGoodsExtendVO.setSectionList(
                    storeGoodsAnalysisContext.getStoreSalesProgramGoodsIdAndTimeSectionInfoMap().get(storeGoodsExtendVO.getStoreSalesProgramGoodsId()));
        }
    }

    /**
     * 组装商品扩展基础信息
     *
     * @param storeGoodsExtendVO        商品扩展信息
     * @param storeGoodsAnalysisContext 商品分析上下文
     */
    private static void assembleGoodsExpandBaseInfo(StoreGoodsBaseExtendVO storeGoodsExtendVO, StoreGoodsAnalysisContextImpl storeGoodsAnalysisContext) {
        if (storeGoodsAnalysisContext.getNeedQueryBrandFlag()) {
            storeGoodsExtendVO.setBrandInfo(storeGoodsAnalysisContext.getStoreBrandIdAndInfoMap().get(storeGoodsExtendVO.getBrand()));
        }
        if (storeGoodsAnalysisContext.getNeedQueryUnitFlag()) {
            storeGoodsExtendVO.setGoodsUnitName(storeGoodsAnalysisContext.getStoreUnitIdAndNameMap().get(storeGoodsExtendVO.getGoodsUnit()));
        }
        if (storeGoodsAnalysisContext.getNeedQuerySaleCategoryFlag()) {
            storeGoodsExtendVO.setCategoryList(storeGoodsAnalysisContext.getStoreSaleCategoryIdAndInfoMap().get(storeGoodsExtendVO.getId()));
        }
        if (storeGoodsAnalysisContext.getNeedQueryGoodsScaleCodeFlag()) {
            storeGoodsExtendVO.setGoodsScaleCodeList(storeGoodsAnalysisContext.getStoreGoodsIdAndGoodsScaleCodeVOInfoMap().get(storeGoodsExtendVO.getId()));
        }
    }

    /**
     * 当即有默认销售方案又有自定义销售方案时，组装商品销售信息(传秤)
     *
     * @param storeGoodsExtendVOList     商品列表
     * @param allStoreSalesProgramPOList 所有的销售方案
     */
    private void assembleStoreGoodsSaleInfoForMultipleSalesProgram(List<StoreGoodsExtendVO> storeGoodsExtendVOList,
                                                                   List<StoreSalesProgramPO> allStoreSalesProgramPOList) {
        StoreSalesProgramPO defaultStoreSalesProgramPO = allStoreSalesProgramPOList.stream()
                .filter(StoreSalesProgramPO::getIsDefault)
                .findFirst()
                .orElseThrow(() -> new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "没有默认的门店销售方案"));
        List<StoreSalesProgramPO> customStoreSalesProgramPOList = allStoreSalesProgramPOList.stream()
                .filter(storeSalesProgramPO -> !storeSalesProgramPO.getIsDefault())
                .toList();
        // 获取当前生效的时段信息（包含销售方案ID、时段序号、时段开始时间、时段结束时间），用于后续给商品组装销售价格使用
        List<StoreSalesProgramSectionBO> concurrentStoreSalesProgramSectionBOList = new ArrayList<>();
        // 获取当前时间之前的时段信息（包含销售方案ID、时段序号、时段开始时间、时段结束时间），用于后续给商品组装销售价格使用
        List<StoreSalesProgramSectionBO> beforeStoreSalesProgramSectionBOList = new ArrayList<>();
        // 获取当前时间之后的时段信息（包含销售方案ID、时段序号、时段开始时间、时段结束时间），用于后续给商品组装销售价格使用
        List<StoreSalesProgramSectionBO> afterStoreSalesProgramSectionBOList = new ArrayList<>();
        // 对时段信息进行分类，当前时间段、之前时段、之后时段
        classifyTimeSections(customStoreSalesProgramPOList, concurrentStoreSalesProgramSectionBOList,
                beforeStoreSalesProgramSectionBOList, afterStoreSalesProgramSectionBOList);
        List<Integer> allStoreSalesProgramIdList = allStoreSalesProgramPOList.stream().map(StoreSalesProgramPO::getId).toList();
        List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList = storeSalesProgramGoodsMapperService.lambdaQuery()
                .in(StoreSalesProgramGoodsPO::getStoreSalesProgramId, allStoreSalesProgramIdList)
                .list();
        // 组装商品ID和方案商品信息的映射
        Map<Integer, Map<Integer, StoreSalesProgramGoodsPO>> storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap = new HashMap<>();
        // 组装商品ID和销售方案ID集合的映射
        Map<Integer, List<Integer>> storeGoodsIdAndStoreSalesProgramIdListMap = new HashMap<>();
        for (StoreSalesProgramGoodsPO storeSalesProgramGoodsPO : storeSalesProgramGoodsPOList) {
            storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap.putIfAbsent(storeSalesProgramGoodsPO.getGoodsId(), new HashMap<>());
            storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap.get(storeSalesProgramGoodsPO.getGoodsId()).put(
                    storeSalesProgramGoodsPO.getStoreSalesProgramId(), storeSalesProgramGoodsPO);
            storeGoodsIdAndStoreSalesProgramIdListMap.putIfAbsent(storeSalesProgramGoodsPO.getGoodsId(), new ArrayList<>());
            storeGoodsIdAndStoreSalesProgramIdListMap.get(
                    storeSalesProgramGoodsPO.getGoodsId()).add(storeSalesProgramGoodsPO.getStoreSalesProgramId());
        }
        // 获取店铺商品ID 和 销售方案ID和价格集合的映射
        // 先根据goodsId分组，再根据storeSalesProgramId分组
        Map<Integer, Map<Integer, List<StoreSalesProgramGoodsPricePO>>> goodsIdAndStoreSalesProgramIdAndInfoListMap =
                storeSalesProgramGoodsPriceMapperService.lambdaQuery().select(StoreSalesProgramGoodsPricePO::getGoodsId,
                                StoreSalesProgramGoodsPricePO::getStoreSalesProgramId,
                                StoreSalesProgramGoodsPricePO::getSerialNumber,
                                StoreSalesProgramGoodsPricePO::getTimeSectionPriceCompute)
                        .in(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, allStoreSalesProgramIdList)
                        .list().stream()
                        .collect(Collectors.groupingBy(StoreSalesProgramGoodsPricePO::getGoodsId,
                                Collectors.groupingBy(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId)));
        boolean concurrentStoreSalesProgramSectionExist = CollectionUtils.isNotEmpty(concurrentStoreSalesProgramSectionBOList);
        boolean beforeStoreSalesProgramSectionExist = CollectionUtils.isNotEmpty(beforeStoreSalesProgramSectionBOList);
        boolean afterStoreSalesProgramSectionExist = CollectionUtils.isNotEmpty(afterStoreSalesProgramSectionBOList);
        for (StoreGoodsExtendVO storeGoodsExtendVO : storeGoodsExtendVOList) {
            List<Integer> storeSalesProgramIdList = storeGoodsIdAndStoreSalesProgramIdListMap.get(storeGoodsExtendVO.getId());
            if (CollectionUtils.isEmpty(storeSalesProgramIdList)) {
                continue;
            }
            // 判断取哪个方案是需要按照顺序： 当前生效的自定义方案 > 默认方案 > 之后生效的自定义方案 > 之前生效的自定义方案
            if (concurrentStoreSalesProgramSectionExist &&
                    assignedSaleProgramAndAssembleStoreGoodsSaleInfo(storeGoodsExtendVO, concurrentStoreSalesProgramSectionBOList,
                            storeSalesProgramIdList, storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap, goodsIdAndStoreSalesProgramIdAndInfoListMap)) {
                // 首先需要判断在售方案，如果在售方案则需要先看在售方案中是否有当前商品，如果有则获取该商品的售卖信息，如果没有则往下执行
            } else if (storeSalesProgramIdList.contains(defaultStoreSalesProgramPO.getId())) {
                // 首先需要判断默认方案，如果默认方案则需要先看默认方案中是否有当前商品，如果有则获取该商品的售卖信息，如果没有则往下执行
                assembleStoreGoodsSaleInfo(storeGoodsExtendVO, defaultStoreSalesProgramPO.getId(), defaultStoreSalesProgramPO.getStoreSalesProgramName(),
                        defaultStoreSalesProgramPO.getTimeSection().get(0).getSerialNumber(), storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap,
                        goodsIdAndStoreSalesProgramIdAndInfoListMap);
            } else if (afterStoreSalesProgramSectionExist &&
                    assignedSaleProgramAndAssembleStoreGoodsSaleInfo(storeGoodsExtendVO, afterStoreSalesProgramSectionBOList,
                            storeSalesProgramIdList, storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap, goodsIdAndStoreSalesProgramIdAndInfoListMap)) {
                // 首先需要判断当前时间之后的自定义方案，如果当前时间之后的自定义方案则需要先看当前时间之后的自定义方案中是否有当前商品，如果有则获取该商品的售卖信息，如果没有则往下执行
            } else if (beforeStoreSalesProgramSectionExist &&
                    assignedSaleProgramAndAssembleStoreGoodsSaleInfoForBefore(storeGoodsExtendVO, beforeStoreSalesProgramSectionBOList,
                            storeSalesProgramIdList, storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap, goodsIdAndStoreSalesProgramIdAndInfoListMap)) {
                // 这儿需要注意，因为这个数组是时间的正序排列，所以往前数 则需要从最后一个开始往前遍历
                // 首先需要判断当前时间之前的自定义方案，如果当前时间之前的自定义方案则需要先看当前时间之前的自定义方案中是否有当前商品，如果有则获取该商品的售卖信息
            } else {
                throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, NO_SALE_PROGRAM);
            }
        }
    }

    /**
     * 当只有一个默认销售方案时，组装商品销售信息(传秤)
     *
     * @param storeGoodsExtendVOList     商品信息
     * @param allStoreSalesProgramPOList 所有店铺销售方案
     */
    private void assembleStoreGoodsSaleInfoForOnlyDefaultSalesProgram(List<StoreGoodsExtendVO> storeGoodsExtendVOList,
                                                                      List<StoreSalesProgramPO> allStoreSalesProgramPOList) {
        StoreSalesProgramPO defaultStoreSalesProgramPO = allStoreSalesProgramPOList.get(0);
        Map<Integer, StoreSalesProgramGoodsPO> storeGoodsIdAndInfoMap;
        // 查询销售时段下所有商品
        List<StoreSalesProgramGoodsPO> goodsPOS = storeSalesProgramGoodsMapperService.lambdaQuery()
                .eq(StoreSalesProgramGoodsPO::getStoreSalesProgramId, defaultStoreSalesProgramPO.getId())
                .list();
        // 商品只需要计数和计重
        if(!goodsPOS.isEmpty()){
            List<Integer> goodIds = goodsPOS.stream().map(StoreSalesProgramGoodsPO::getGoodsId).toList();
            List<StoreGoodsPO> goodsPOList = storeGoodsMapperService.lambdaQuery()
                    .in(StoreGoodsPO::getId, goodIds)
                    .in(StoreGoodsPO::getValuationMethod, List.of(GoodsValuationMethod.WEIGHING.getId(),
                            GoodsValuationMethod.COUNTING.getId()))
                    .list();
            // 过滤数据
            if(!goodsPOList.isEmpty()){
                List<Integer> filterGoodIds = goodsPOList.stream().map(StoreGoodsPO::getId).toList();
                goodsPOS.removeIf(item -> !filterGoodIds.contains(item.getGoodsId()));
            }
            storeGoodsIdAndInfoMap = goodsPOS.stream()
                    .collect(Collectors.toMap(StoreSalesProgramGoodsPO::getGoodsId, Function.identity()));
        } else {
            storeGoodsIdAndInfoMap = Map.of();
        }
        Map<Integer, List<StoreSalesProgramGoodsPricePO>> goodsIdAndInfoListMap =
                storeSalesProgramGoodsPriceMapperService.lambdaQuery().select(StoreSalesProgramGoodsPricePO::getGoodsId,
                                StoreSalesProgramGoodsPricePO::getStoreSalesProgramId,
                                StoreSalesProgramGoodsPricePO::getSerialNumber,
                                StoreSalesProgramGoodsPricePO::getTimeSectionPriceCompute)
                        .eq(StoreSalesProgramGoodsPricePO::getStoreSalesProgramId, defaultStoreSalesProgramPO.getId())
                        .list().stream()
                        .collect(Collectors.groupingBy(StoreSalesProgramGoodsPricePO::getGoodsId));
        for (StoreGoodsExtendVO storeGoodsExtendVO : storeGoodsExtendVOList) {
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeGoodsIdAndInfoMap.get(storeGoodsExtendVO.getId());
            if (storeSalesProgramGoodsPO == null) {
                continue;
            }
            storeGoodsExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
            storeGoodsExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
            storeGoodsExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
            List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList = goodsIdAndInfoListMap.get(storeGoodsExtendVO.getId());
            if (CollectionUtils.isNotEmpty(storeSalesProgramGoodsPricePOList)) {
                StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO = storeSalesProgramGoodsPricePOList.get(0);
                storeGoodsExtendVO.setSellingPrice(
                        storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute().setScale(2, RoundingMode.HALF_UP).toString());
            }
        }
    }

    /**
     * 指定需要展示的销售方案，再根据展示的销售方案组装商品销售信息
     *
     * @param storeGoodsExtendVO                                  商品信息
     * @param storeSalesProgramSectionBOList                      展示的销售方案列表
     * @param storeSalesProgramIdList                             销售方案ID列表
     * @param storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap 商品ID和销售方案ID和销售方案商品信息的映射
     * @param goodsIdAndStoreSalesProgramIdAndInfoListMap         商品ID和销售方案ID和价格集合的映射
     * @return 是否找到了指定销售方案
     */
    private boolean assignedSaleProgramAndAssembleStoreGoodsSaleInfo(StoreGoodsExtendVO storeGoodsExtendVO,
                                                                     List<StoreSalesProgramSectionBO> storeSalesProgramSectionBOList,
                                                                     List<Integer> storeSalesProgramIdList,
                                                                     Map<Integer, Map<Integer, StoreSalesProgramGoodsPO>> storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap,
                                                                     Map<Integer, Map<Integer, List<StoreSalesProgramGoodsPricePO>>> goodsIdAndStoreSalesProgramIdAndInfoListMap) {
        for (StoreSalesProgramSectionBO storeSalesProgramSectionBO : storeSalesProgramSectionBOList) {
            if (storeSalesProgramIdList.contains(storeSalesProgramSectionBO.getStoreSalesProgramId())) {
                assembleStoreGoodsSaleInfo(storeGoodsExtendVO, storeSalesProgramSectionBO.getStoreSalesProgramId(),
                        storeSalesProgramSectionBO.getStoreSalesProgramName(),
                        storeSalesProgramSectionBO.getSerialNumber(), storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap,
                        goodsIdAndStoreSalesProgramIdAndInfoListMap);
                return true;
            }
        }
        return false;
    }

    /**
     * 指定需要展示的销售方案，再根据展示的销售方案组装商品销售信息
     *
     * @param storeGoodsExtendVO                                  商品信息
     * @param storeSalesProgramSectionBOList                      展示的销售方案列表
     * @param storeSalesProgramIdList                             销售方案ID列表
     * @param storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap 商品ID和销售方案ID和销售方案商品信息的映射
     * @param goodsIdAndStoreSalesProgramIdAndInfoListMap         商品ID和销售方案ID和价格集合的映射
     * @return 是否找到了指定销售方案
     */
    private boolean assignedSaleProgramAndAssembleStoreGoodsSaleInfoForBefore(StoreGoodsExtendVO storeGoodsExtendVO,
                                                                              List<StoreSalesProgramSectionBO> storeSalesProgramSectionBOList,
                                                                              List<Integer> storeSalesProgramIdList,
                                                                              Map<Integer, Map<Integer, StoreSalesProgramGoodsPO>> storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap,
                                                                              Map<Integer, Map<Integer, List<StoreSalesProgramGoodsPricePO>>> goodsIdAndStoreSalesProgramIdAndInfoListMap) {
        for (int i = storeSalesProgramSectionBOList.size() - 1; i >= 0; i--) {
            StoreSalesProgramSectionBO storeSalesProgramSectionBO = storeSalesProgramSectionBOList.get(i);
            if (storeSalesProgramIdList.contains(storeSalesProgramSectionBO.getStoreSalesProgramId())) {
                assembleStoreGoodsSaleInfo(storeGoodsExtendVO, storeSalesProgramSectionBO.getStoreSalesProgramId(),
                        storeSalesProgramSectionBO.getStoreSalesProgramName(),
                        storeSalesProgramSectionBO.getSerialNumber(), storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap,
                        goodsIdAndStoreSalesProgramIdAndInfoListMap);
                return true;
            }
        }
        return false;
    }

    /**
     * 组装销售价格信息(传秤)
     *
     * @param storeGoodsExtendVO                                  商品信息
     * @param storeSalesProgramId                                 指定的销售方案ID
     * @param storeSalesProgramName                               指定的销售方案名称
     * @param serialNumber                                        指定的时段序号
     * @param storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap 商品ID和销售方案ID和销售方案商品信息的映射
     * @param goodsIdAndStoreSalesProgramIdAndInfoListMap         商品ID和销售方案ID和价格集合的映射
     */
    private void assembleStoreGoodsSaleInfo(
            StoreGoodsExtendVO storeGoodsExtendVO, Integer storeSalesProgramId, String storeSalesProgramName, Integer serialNumber,
            Map<Integer, Map<Integer, StoreSalesProgramGoodsPO>> storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap,
            Map<Integer, Map<Integer, List<StoreSalesProgramGoodsPricePO>>> goodsIdAndStoreSalesProgramIdAndInfoListMap) {
        StoreSalesProgramGoodsPO storeSalesProgramGoodsPO =
                storeSalesProgramGoodsIdAndSalesProgramIdAndInfoMap.get(storeGoodsExtendVO.getId())
                        .get(storeSalesProgramId);
        if (storeSalesProgramGoodsPO == null) {
            return;
        }
        storeGoodsExtendVO.setStoreSalesProgramId(storeSalesProgramId);
        storeGoodsExtendVO.setStoreSalesProgramName(storeSalesProgramName);
        storeGoodsExtendVO.setStoreSalesProgramGoodsId(storeSalesProgramGoodsPO.getId());
        storeGoodsExtendVO.setGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
        storeGoodsExtendVO.setCover(storeSalesProgramGoodsPO.getCoverPicture());
        storeGoodsExtendVO.setGoodsLabel(storeSalesProgramGoodsPO.getTag());
        assembleSellingPriceForWeigh(storeGoodsExtendVO, goodsIdAndStoreSalesProgramIdAndInfoListMap,
                storeSalesProgramId, serialNumber);
    }

    /**
     * 组装商品价格信息(传秤)
     *
     * @param storeGoodsExtendVO                          商品信息
     * @param goodsIdAndStoreSalesProgramIdAndInfoListMap 商品ID和销售方案ID和价格集合的映射
     * @param storeSalesProgramId                         指定的销售方案ID
     * @param serialNumber                                指定的时段序号
     */
    private void assembleSellingPriceForWeigh(
            StoreGoodsExtendVO storeGoodsExtendVO,
            Map<Integer, Map<Integer, List<StoreSalesProgramGoodsPricePO>>> goodsIdAndStoreSalesProgramIdAndInfoListMap,
            Integer storeSalesProgramId, Integer serialNumber) {
        Map<Integer, List<StoreSalesProgramGoodsPricePO>> storeSalesProgramIdAndInfoListMap = goodsIdAndStoreSalesProgramIdAndInfoListMap
                .get(storeGoodsExtendVO.getId());
        if (storeSalesProgramIdAndInfoListMap == null) {
            return;
        }
        // 获取当前商品当前销售方案的每个时段价格
        List<StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOList = storeSalesProgramIdAndInfoListMap.get(storeSalesProgramId);
        for (StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO : storeSalesProgramGoodsPricePOList) {
            if (storeSalesProgramGoodsPricePO.getSerialNumber().equals(serialNumber)) {
                storeGoodsExtendVO.setSellingPrice(
                        storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute().setScale(2, RoundingMode.HALF_UP).toString());
                return;
            }
        }
    }

    /**
     * 对时段信息进行分类
     * 1. 当前时段：当前时间在该时段内
     * 2. 之前时段：当前时间在该时段之前
     * 3. 之后时段：当前时间在该时段之后
     *
     * @param storeSalesProgramPOList                  店铺销售方案列表
     * @param concurrentStoreSalesProgramSectionBOList 当前时段列表
     * @param beforeStoreSalesProgramSectionBOList     之前时段列表
     * @param afterStoreSalesProgramSectionBOList      之后时段列表
     */
    private void classifyTimeSections(List<StoreSalesProgramPO> storeSalesProgramPOList,
                                      List<StoreSalesProgramSectionBO> concurrentStoreSalesProgramSectionBOList,
                                      List<StoreSalesProgramSectionBO> beforeStoreSalesProgramSectionBOList,
                                      List<StoreSalesProgramSectionBO> afterStoreSalesProgramSectionBOList) {
        List<StoreSalesProgramSectionBO> storeSalesProgramSectionBOList = new ArrayList<>();
        // 组装时段信息
        assembleStoreSalesProgramSectionBOList(storeSalesProgramPOList, storeSalesProgramSectionBOList);
        storeSalesProgramSectionBOList.sort(Comparator.comparing(StoreSalesProgramSectionBO::getStart));
        LocalTime currentLocalTime = DateUtils.getCurrentLocalTimeWithoutSeconds();
        // 遍历时段列表，根据时间段划分
        for (StoreSalesProgramSectionBO section : storeSalesProgramSectionBOList) {
            if (isCurrentTimeInSection(currentLocalTime, section)) {
                // 当前时段
                concurrentStoreSalesProgramSectionBOList.add(section);
            } else if (currentLocalTime.isBefore(section.getStart())) {
                // 当前时刻在时段开始之前
                afterStoreSalesProgramSectionBOList.add(section);
            } else {
                // 当前时刻在时段结束之后
                beforeStoreSalesProgramSectionBOList.add(section);
            }
        }
    }

    /**
     * 组装时段信息
     *
     * @param storeSalesProgramPOList        店铺销售方案列表
     * @param storeSalesProgramSectionBOList 时段信息列表
     */
    private void assembleStoreSalesProgramSectionBOList(List<StoreSalesProgramPO> storeSalesProgramPOList,
                                                        List<StoreSalesProgramSectionBO> storeSalesProgramSectionBOList) {
        for (StoreSalesProgramPO storeSalesProgramPO : storeSalesProgramPOList) {
            for (StoreSalesProgramPO.Section section : storeSalesProgramPO.getTimeSection()) {
                storeSalesProgramSectionBOList.add(
                        StoreSalesProgramSectionBO.builder()
                                .storeSalesProgramId(storeSalesProgramPO.getId())
                                .storeSalesProgramName(storeSalesProgramPO.getStoreSalesProgramName())
                                .start(section.getStart())
                                .end(section.getEnd())
                                .serialNumber(section.getSerialNumber())
                                .build());
            }
        }
    }

    /**
     * 判断当前时间是否在给定时段内（包含边界）
     *
     * @param currentLocalTime 当前时间
     * @param section          时段信息
     * @return 是否在时段内
     */
    private boolean isCurrentTimeInSection(LocalTime currentLocalTime, StoreSalesProgramSectionBO section) {
        return (currentLocalTime.equals(section.getStart()) || currentLocalTime.equals(section.getEnd())) ||
                (currentLocalTime.isAfter(section.getStart()) && currentLocalTime.isBefore(section.getEnd()));
    }

    /**
     * 针对类型是DAILY的情况做可售的销售方案过滤
     *
     * @param storeSalesProgramPO 店铺销售方案
     * @param currentLocalTime    当前时间
     * @return 店铺销售方案Id和时段序号的映射
     */
    private Pair<StoreSalesProgramPO, Integer> filterAvailableDailySalesPrograms(StoreSalesProgramPO storeSalesProgramPO,
                                                                                 LocalTime currentLocalTime) {
        for (StoreSalesProgramPO.Section section : storeSalesProgramPO.getTimeSection()) {
            // 当前时间在开始时间和结束时间之间 或者 等于开始时间、结束时间
            if ((currentLocalTime.isAfter(section.getStart()) && currentLocalTime.isBefore(section.getEnd())) ||
                    currentLocalTime.equals(section.getStart()) ||
                    currentLocalTime.equals(section.getEnd())) {
                return Pair.of(storeSalesProgramPO, section.getSerialNumber());
            }
        }
        return null;
    }
}

package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.model.dto.QueryStoreSalesProgramGoodsCategoryTreeDTO;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.service.StoreSalesProgramGoodsCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * Description: 门店营销方案 ServiceImpl
 * Author: 向超
 * Date: 2024/11/14 15:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StoreSalesProgramGoodsCategoryServiceImpl implements StoreSalesProgramGoodsCategoryService {

    @Override
    public PageRespVO<Void> getTestList(QueryStoreSalesProgramGoodsCategoryTreeDTO queryStoreSalesProgramGoodsCategoryTreeDTO) {
        return null;
    }
}

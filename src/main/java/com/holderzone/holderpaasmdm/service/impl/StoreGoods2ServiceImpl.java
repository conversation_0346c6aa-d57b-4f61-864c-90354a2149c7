package com.holderzone.holderpaasmdm.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.holderzone.holderpaasmdm.common.constant.SeparatorConstants;
import com.holderzone.holderpaasmdm.common.exception.BaseException;
import com.holderzone.holderpaasmdm.common.utils.CommonUtils;
import com.holderzone.holderpaasmdm.converter.StoreGoodsConverter;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.enumeraton.StoreSalesProgramStatus;
import com.holderzone.holderpaasmdm.mapper.service.*;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleListDTO;
import com.holderzone.holderpaasmdm.model.dto.SqlQueryGoodsByTagListDTO;
import com.holderzone.holderpaasmdm.model.dto.SqlQuerySkuSpecInfoDTO;
import com.holderzone.holderpaasmdm.model.po.*;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.model.vo.StoreGoodsPrintLabelExtendVO;
import com.holderzone.holderpaasmdm.service.CommonService;
import com.holderzone.holderpaasmdm.service.StoreGoods2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * 门店营销方案2 ServiceImpl
 *
 * <AUTHOR>
 * @date 2025/4/15 15:39
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class StoreGoods2ServiceImpl implements StoreGoods2Service {

    private final CommonService commonService;
    private final StoreGoodsMapperService storeGoodsMapperService;
    private final SaleCategoryAndStoreGoodsMapperService saleCategoryAndStoreGoodsMapperService;
    private final StoreSalesProgramMapperService storeSalesProgramMapperService;
    private final StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService;
    private final StoreSalesProgramGoodsPriceMapperService storeSalesProgramGoodsPriceMapperService;
    private final GoodsPackageSkuMapperService goodsPackageSkuMapperService;
    private final SkuSpecDetailsMapperService skuSpecDetailsMapperService;
    private final StoreGoodsConverter storeGoodsConverter;

    @Override
    public PageRespVO<StoreGoodsPrintLabelExtendVO> queryStoreGoodsAvailableForSaleTimeSectionListPage(
            QueryStoreGoodsAvailableForSaleListDTO queryDTO) {
        Map<Integer, StoreSalesProgramPO> salesProgramIdAndInfoMap = queryStoreSalesProgramIdAndInfoMap(queryDTO);
        // 开始查询销售方案对应的所有的销售方案商品
        List<StoreSalesProgramGoodsPO> allStoreSalesProgramGoodsPOList = storeSalesProgramGoodsMapperService.lambdaQuery()
                .in(StoreSalesProgramGoodsPO::getStoreSalesProgramId, salesProgramIdAndInfoMap.keySet())
                .in(CollectionUtils.isNotEmpty(queryDTO.getStoreSalesProgramGoodsIdList()),
                        StoreSalesProgramGoodsPO::getId, queryDTO.getStoreSalesProgramGoodsIdList())
                .list();
        if (CollectionUtils.isEmpty(allStoreSalesProgramGoodsPOList)) {
            return new PageRespVO<>(queryDTO);
        }
        // 根据销售分组ID集合过滤商品集合
        filterStoreSalesProgramGoodsBySaleCategoryIdList(queryDTO, allStoreSalesProgramGoodsPOList);
        if (CollectionUtils.isEmpty(allStoreSalesProgramGoodsPOList)) {
            return new PageRespVO<>(queryDTO);
        }
        Set<Integer> allStoreSalesProgramGoodsIds = new HashSet<>();
        // 组装商品ID和销售方案ID和销售方案商品信息 的映射
        Map<Integer, Map<Integer, StoreSalesProgramGoodsPO>> goodsIdAndSalesProgramIdAndInfoMap = new HashMap<>();
        for (StoreSalesProgramGoodsPO storeSalesProgramGoodsPO : allStoreSalesProgramGoodsPOList) {
            allStoreSalesProgramGoodsIds.add(storeSalesProgramGoodsPO.getId());
            goodsIdAndSalesProgramIdAndInfoMap.computeIfAbsent(storeSalesProgramGoodsPO.getGoodsId(), k -> new HashMap<>())
                    .put(storeSalesProgramGoodsPO.getStoreSalesProgramId(), storeSalesProgramGoodsPO);
        }
        // 查询所有商品数据
        List<SqlQueryGoodsByTagListDTO> dtoList = storeGoodsMapperService.queryAllSkuGoodsByTag(allStoreSalesProgramGoodsIds,
                salesProgramIdAndInfoMap.keySet());
        // 根据关键字过滤商品和销售方案商品
        filterStoreGoodsAndSalesProgramGoodsByKeywords(queryDTO, dtoList);
        // 排序
        sortProgramList(dtoList, queryDTO.getStoreSalesProgramGoodsIdList());
        // 先分页，避免后续查询出无效的数据
        PageRespVO<SqlQueryGoodsByTagListDTO> pageRespVO = new PageRespVO<>(dtoList, queryDTO.getLimit(), queryDTO.getPage());
        dtoList = pageRespVO.getList();
        if (CollectionUtils.isEmpty(dtoList)) {
            return new PageRespVO<>(queryDTO);
        }
        // 对象转换
        List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList = storeGoodsConverter.toStoreGoodsPrintLabelExtendSkuVOList(dtoList);
        // 再次转换,这个插件有部分值不会转换，因为继承关系，法克
        toStoreGoodsPrintLabelExtendVO(storeGoodsPrintLabelExtendVOList, dtoList);
        // 组装sku的各种信息
        assembleStoreGoodsPrintLabelExtendVOSalesInfo(storeGoodsPrintLabelExtendVOList, salesProgramIdAndInfoMap);
        // 查询商品扩展信息
        commonService.assembleGoodsExpandInfoForPrintLabel(storeGoodsPrintLabelExtendVOList, queryDTO.getStoreGoodsExpandList(),
                queryDTO.getStoreId(), queryDTO.getChannelId(),
                queryDTO.getScaleType());
        return new PageRespVO<>(storeGoodsPrintLabelExtendVOList, pageRespVO.getTotal(), pageRespVO.getPageSize(),
                pageRespVO.getPageNum(), pageRespVO.getTotalPage());
    }

    /**
     * 组装商品扩展信息
     *
     * @param storeGoodsPrintLabelExtendVOList 商品扩展信息集合
     * @param dtoList                          sql查询结果
     */
    private void toStoreGoodsPrintLabelExtendVO(List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList,
                                                List<SqlQueryGoodsByTagListDTO> dtoList) {
        for (int i = 0; i < storeGoodsPrintLabelExtendVOList.size(); i++) {
            StoreGoodsPrintLabelExtendVO storeGoodsPrintLabelExtendVO = storeGoodsPrintLabelExtendVOList.get(i);
            SqlQueryGoodsByTagListDTO sqlQueryGoodsByTagListDTO = dtoList.get(i);
            storeGoodsPrintLabelExtendVO.setStoreSalesProgramGoodsId(sqlQueryGoodsByTagListDTO.getStoreSalesProgramGoodsId());
            storeGoodsPrintLabelExtendVO.setStoreSalesProgramGoodsName(sqlQueryGoodsByTagListDTO.getStoreSalesProgramGoodsName());
            storeGoodsPrintLabelExtendVO.setStoreSalesProgramId(sqlQueryGoodsByTagListDTO.getStoreSalesProgramId());
            storeGoodsPrintLabelExtendVO.setStoreSalesProgramName(sqlQueryGoodsByTagListDTO.getStoreSalesProgramGoodsName());
            storeGoodsPrintLabelExtendVO.setSalesProgramIsDefault(sqlQueryGoodsByTagListDTO.getStoreSalesProgramIsDefault());
            storeGoodsPrintLabelExtendVO.setGoodsPackageSkuId(sqlQueryGoodsByTagListDTO.getGoodsPackageSkuId());
            storeGoodsPrintLabelExtendVO.setStoreSalesProgramGoodsPriceId(sqlQueryGoodsByTagListDTO.getStoreSalesProgramGoodsPriceId());
        }
    }

    /**
     * 查询店铺启用的销售方案ID和方案信息的映射
     *
     * @param queryDTO 查询条件
     * @return 店铺启用的销售方案ID和方案信息的映射
     */
    private Map<Integer, StoreSalesProgramPO> queryStoreSalesProgramIdAndInfoMap(QueryStoreGoodsAvailableForSaleListDTO queryDTO) {
        List<StoreSalesProgramPO> allStoreSalesProgramPOList = storeSalesProgramMapperService.lambdaQuery()
                .eq(StoreSalesProgramPO::getStoreId, queryDTO.getStoreId())
                .eq(StoreSalesProgramPO::getChannelId, queryDTO.getChannelId())
                .in(CollectionUtils.isNotEmpty(queryDTO.getSalesProgramIdList()), StoreSalesProgramPO::getId,
                        queryDTO.getSalesProgramIdList())
                .eq(StoreSalesProgramPO::getIsEnable, StoreSalesProgramStatus.ENABLE.isValue())
                .list();
        if (CollectionUtils.isEmpty(allStoreSalesProgramPOList)) {
            log.error("门店->{}, 通道->{}, 没有可用的门店默认销售方案", queryDTO.getStoreId(), queryDTO.getChannelId());
            throw new BaseException(HttpStatus.BAD_REQUEST, ResponseCode.COMMON_INTERNAL_SERVER_ERROR, "没有可用的门店默认销售方案");
        }
        // 组装销售方案ID和销售方案信息 的映射
        return allStoreSalesProgramPOList.stream().collect(toMap(StoreSalesProgramPO::getId, Function.identity()));
    }

    /**
     * 根据销售分组ID集合过滤店铺销售方案商品
     *
     * @param queryDTO                        查询条件
     * @param allStoreSalesProgramGoodsPOList 所有店铺销售方案商品
     */
    private void filterStoreSalesProgramGoodsBySaleCategoryIdList(QueryStoreGoodsAvailableForSaleListDTO queryDTO,
                                                                  List<StoreSalesProgramGoodsPO> allStoreSalesProgramGoodsPOList) {
        if (CollectionUtils.isNotEmpty(queryDTO.getSaleCategoryIdList())) {
            Set<Integer> needFilterGoodsIdSet = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                    .select(SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                    .in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, queryDTO.getSaleCategoryIdList())
                    .list().stream().map(SaleCategoryAndStoreGoodsPO::getStoreGoodsId).collect(Collectors.toSet());
            allStoreSalesProgramGoodsPOList.removeIf(storeSalesProgramGoodsPO ->
                    !needFilterGoodsIdSet.contains(storeSalesProgramGoodsPO.getGoodsId()));
        }
    }

    /**
     * 根据关键字过滤商品和销售方案商品
     *
     * @param queryDTO 查询条件
     * @param dtoList  店铺商品sku列表
     */
    private void filterStoreGoodsAndSalesProgramGoodsByKeywords(QueryStoreGoodsAvailableForSaleListDTO queryDTO,
                                                                List<SqlQueryGoodsByTagListDTO> dtoList) {
        if (StringUtils.isEmpty(queryDTO.getKeywords())) {
            return;
        }
        String lowerCaseKeywords = queryDTO.getKeywords().toLowerCase();
        // 移除不匹配的店铺商品
        dtoList.removeIf(storeGoodsPO ->
                !(storeGoodsPO.getGoodsName().toLowerCase().contains(lowerCaseKeywords)
                        || (storeGoodsPO.getStoreSalesProgramGoodsName().toLowerCase().contains(lowerCaseKeywords)
                        || CommonUtils.filterLikeBarcodeListByKeywords(storeGoodsPO.getGoodsPackageSkuBarCode(), lowerCaseKeywords)
                        || storeGoodsPO.getPinyinCode().contains(lowerCaseKeywords))));
    }

    /**
     * 组装打印标签商品的售卖信息（售卖标签、售卖价格、时段、售卖封面图片）
     * 组装的维度是以商品为维度，即该商品的所在的所有方案的所有时段的售卖信息都要组装到一起
     *
     * @param storeGoodsPrintLabelExtendVOList 打印标签商品列表
     * @param salesProgramIdAndInfoMap         销售方案ID和销售方案信息 的映射
     */
    private void assembleStoreGoodsPrintLabelExtendVOSalesInfo(List<StoreGoodsPrintLabelExtendVO> storeGoodsPrintLabelExtendVOList,
                                                               Map<Integer, StoreSalesProgramPO> salesProgramIdAndInfoMap) {
        // 组装查询的id集合
        Set<Integer> allStoreSalesProgramGoodsIdList = new HashSet<>();
        Set<Integer> allStoreSalesProgramGoodsPriceIdList = new HashSet<>();
        Set<Integer> allGoodsPackageSkuIdList = new HashSet<>();
        for (StoreGoodsPrintLabelExtendVO skuVO : storeGoodsPrintLabelExtendVOList) {
            allStoreSalesProgramGoodsIdList.add(skuVO.getStoreSalesProgramGoodsId());
            allStoreSalesProgramGoodsPriceIdList.add(skuVO.getStoreSalesProgramGoodsPriceId());
            allGoodsPackageSkuIdList.add(skuVO.getGoodsPackageSkuId());
        }
        // 查询数据信息
        Map<Integer, StoreSalesProgramGoodsPO> storeSalesProgramGoodsIdAndInfoMap =
                storeSalesProgramGoodsMapperService.findMapByIdIn(allStoreSalesProgramGoodsIdList);
        Map<Integer, StoreSalesProgramGoodsPricePO> storeSalesProgramGoodsPricePOMap =
                storeSalesProgramGoodsPriceMapperService.findMapByIdIn(allStoreSalesProgramGoodsPriceIdList);
        Map<Integer, GoodsPackageSkuPO> goodsPackageSkuPOMap = goodsPackageSkuMapperService.findMapByIdIn(allGoodsPackageSkuIdList);
        // 获取规格数据
        Map<Integer, String> skuSpecInfoMap = findSkuSpecInfoMap(allGoodsPackageSkuIdList);

        for (StoreGoodsPrintLabelExtendVO skuVO : storeGoodsPrintLabelExtendVOList) {
            // 策略价格信息
            StoreSalesProgramGoodsPricePO storeSalesProgramGoodsPricePO =
                    storeSalesProgramGoodsPricePOMap.get(skuVO.getStoreSalesProgramGoodsPriceId());
            skuVO.setSellingPrice(storeSalesProgramGoodsPricePO.getTimeSectionPriceCompute()
                    .setScale(2, RoundingMode.HALF_UP).toString());
            skuVO.setSerialNumber(storeSalesProgramGoodsPricePO.getSerialNumber());
            // 策略信息
            StoreSalesProgramPO storeSalesProgramPO = salesProgramIdAndInfoMap.get(skuVO.getStoreSalesProgramId());
            skuVO.setStoreSalesProgramName(storeSalesProgramPO.getStoreSalesProgramName());
            skuVO.setSalesProgramIsDefault(storeSalesProgramPO.getIsDefault());
            // 销售方案时间段对应
            for (StoreSalesProgramPO.Section section : storeSalesProgramPO.getTimeSection()) {
                if (Objects.equals(section.getSerialNumber(), skuVO.getSerialNumber())) {
                    skuVO.setStart(section.getStart());
                    skuVO.setEnd(section.getEnd());
                }
            }
            // 销售方案商品信息
            StoreSalesProgramGoodsPO storeSalesProgramGoodsPO = storeSalesProgramGoodsIdAndInfoMap.get(
                    skuVO.getStoreSalesProgramGoodsId());
            skuVO.setStoreSalesProgramGoodsName(storeSalesProgramGoodsPO.getGoodsSaleName());
            skuVO.setLabel(storeSalesProgramGoodsPO.getTag());
            // sku信息
            GoodsPackageSkuPO goodsPackageSkuPO = goodsPackageSkuPOMap.get(skuVO.getGoodsPackageSkuId());
            // 条形码
            if (StringUtils.isNotEmpty(goodsPackageSkuPO.getBarcode())) {
                skuVO.setBarcode(goodsPackageSkuPO.getBarcode().split(SeparatorConstants.COMMA_EN)[0]);
            }
            // 封面
            if (Objects.nonNull(goodsPackageSkuPO.getCover())) {
                // 优先获取规格封面
                skuVO.setCoverPicture(goodsPackageSkuPO.getCover());
            } else {
                // 如果没有，获取销售封面
                skuVO.setCoverPicture(storeSalesProgramGoodsPO.getCoverPicture());
            }
            // 规格数据
            skuVO.setSpecName(skuSpecInfoMap.get(skuVO.getGoodsPackageSkuId()));
        }
    }

    /**
     * 查询规格数据信息
     *
     * @param allGoodsPackageSkuIdList skuId集合
     * @return 查询结果
     */
    private Map<Integer, String> findSkuSpecInfoMap(Set<Integer> allGoodsPackageSkuIdList) {
        // 查询数据
        List<SqlQuerySkuSpecInfoDTO> sqlQuerySkuSpecInfoDTOList = skuSpecDetailsMapperService.querySpecInfoBySkuIdIn(
                allGoodsPackageSkuIdList);
        if (sqlQuerySkuSpecInfoDTOList.isEmpty()) {
            return Map.of();
        }
        // 将数据根据skuId分组
        Map<Integer, List<SqlQuerySkuSpecInfoDTO>> skuMap = sqlQuerySkuSpecInfoDTOList.stream()
                .collect(Collectors.groupingBy(SqlQuerySkuSpecInfoDTO::getSkuId));
        Map<Integer, String> skuSpecInfoMap = new HashMap<>();
        for (Map.Entry<Integer, List<SqlQuerySkuSpecInfoDTO>> entry : skuMap.entrySet()) {
            skuSpecInfoMap.put(entry.getKey(), String.join(",",
                    entry.getValue().stream().map(SqlQuerySkuSpecInfoDTO::getSpecDetailName).toList()));
        }
        return skuSpecInfoMap;
    }

    /**
     * 根据时段排序
     *
     * @param dtoList                      排序的时段列表
     * @param storeSalesProgramGoodsIdList 策略商品排序集合
     */
    private static void sortProgramList(List<SqlQueryGoodsByTagListDTO> dtoList, List<Integer> storeSalesProgramGoodsIdList) {
        if (CollectionUtils.isNotEmpty(storeSalesProgramGoodsIdList)) {
            // 创建映射
            Map<Integer, Integer> orderMap = new HashMap<>();
            for (int i = 0; i < storeSalesProgramGoodsIdList.size(); i++) {
                orderMap.put(storeSalesProgramGoodsIdList.get(i), i);
            }
            dtoList.sort(Comparator.comparingInt(
                    x -> orderMap.getOrDefault(x.getStoreSalesProgramGoodsId(), Integer.MAX_VALUE)));
        } else {
            for (SqlQueryGoodsByTagListDTO dto : dtoList) {
                // 设置策略的开始时间
                if (StrUtil.isNotBlank(dto.getStoreSalesProgramTimeSection())) {
                    List<StoreSalesProgramPO.Section> sections = JSONUtil.toList(dto.getStoreSalesProgramTimeSection(),
                            StoreSalesProgramPO.Section.class);
                    dto.setStart(sections.get(0).getStart());
                }
            }
            dtoList.sort(Comparator.comparing(SqlQueryGoodsByTagListDTO::getId)
                    .thenComparing(SqlQueryGoodsByTagListDTO::getGoodsPackageSkuId)
                    .thenComparing((SqlQueryGoodsByTagListDTO goods) -> !goods.getStoreSalesProgramIsDefault())
                    .thenComparing(SqlQueryGoodsByTagListDTO::getStart));
        }
    }
}

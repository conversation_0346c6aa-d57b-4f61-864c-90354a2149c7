package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.common.utils.DateUtils;
import com.holderzone.holderpaasmdm.converter.StoreSalesProgramConverter;
import com.holderzone.holderpaasmdm.enumeraton.StoreSalesProgramStatus;
import com.holderzone.holderpaasmdm.mapper.service.StoreSalesProgramMapperService;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreSalesProgramListDTO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramPO;
import com.holderzone.holderpaasmdm.model.vo.StoreSalesProgramExtendVO;
import com.holderzone.holderpaasmdm.model.vo.StoreSalesProgramVO;
import com.holderzone.holderpaasmdm.service.StoreSalesProgramService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.Comparator;
import java.util.List;

/**
 * Description: 店铺销售方案 ServiceImpl
 * Author: 向超
 * Date: 2024/11/14 15:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StoreSalesProgramServiceImpl implements StoreSalesProgramService {
    private final StoreSalesProgramMapperService storeSalesProgramMapperService;
    private final StoreSalesProgramConverter storeSalesProgramConverter;

    @Override
    public List<StoreSalesProgramExtendVO> queryStoreSalesProgramList(QueryStoreSalesProgramListDTO queryStoreSalesProgramListDTO) {
        List<StoreSalesProgramExtendVO> storeSalesProgramExtendVOList =
                storeSalesProgramConverter.toStoreSalesProgramExtendVOList(
                        storeSalesProgramMapperService.lambdaQuery()
                                .eq(StoreSalesProgramPO::getStoreId, queryStoreSalesProgramListDTO.getStoreId())
                                .eq(StoreSalesProgramPO::getChannelId, queryStoreSalesProgramListDTO.getChannelId())
                                .eq(StoreSalesProgramPO::getIsEnable, StoreSalesProgramStatus.ENABLE.isValue())
                                .list());
        // 获取当前时间
        LocalTime currentLocalTime = DateUtils.getCurrentLocalTimeWithoutSeconds();
        for (StoreSalesProgramExtendVO storeSalesProgramExtendVO : storeSalesProgramExtendVOList) {
            if (Boolean.TRUE.equals(storeSalesProgramExtendVO.getIsDefault())) {
                // 默认销售方案全天生效
                storeSalesProgramExtendVO.setIsActive(true);
            } else {
                // 标记自定义销售方案是否生效
                signCustomStoreSalesProgramIsActive(storeSalesProgramExtendVO, currentLocalTime);
            }
        }
        // 构造排序规则
        // 1. IsDefault为true的排在第一位
        // 2. isActive为true的排在前面
        // 3. 根据createAt字段排序
        Comparator<StoreSalesProgramExtendVO> comparator = Comparator
                .comparing((StoreSalesProgramExtendVO vo) -> !vo.getIsDefault()) // IsDefault为true的排在第一位
                .thenComparing((StoreSalesProgramExtendVO vo) -> !vo.getIsActive()) // isActive为true的排在前面
                .thenComparing(StoreSalesProgramExtendVO::getCreatedAt); // 根据createAt字段排序

        return storeSalesProgramExtendVOList.stream().sorted(comparator).toList();
    }

    /**
     * 标记自定义销售方案是否生效
     *
     * @param storeSalesProgramExtendVO 自定义销售方案
     * @param currentLocalTime 当前时间
     */
    private void signCustomStoreSalesProgramIsActive(StoreSalesProgramExtendVO storeSalesProgramExtendVO, LocalTime currentLocalTime) {
        for (StoreSalesProgramVO.SectionVO section : storeSalesProgramExtendVO.getSectionList()) {
            // 当前时间在开始时间和结束时间之间 或者 等于开始时间、结束时间
            if ((currentLocalTime.isAfter(section.getStart()) && currentLocalTime.isBefore(section.getEnd())) ||
                    currentLocalTime.equals(section.getStart()) ||
                    currentLocalTime.equals(section.getEnd())) {
                storeSalesProgramExtendVO.setIsActive(true);
            }
        }
    }
}

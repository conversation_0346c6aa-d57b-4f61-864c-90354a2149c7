package com.holderzone.holderpaasmdm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.holderpaasmdm.model.dto.QueryInboundListDTO;
import com.holderzone.holderpaasmdm.model.vo.InboundBaseVO;
import com.holderzone.holderpaasmdm.service.CommonService;
import com.holderzone.holderpaasmdm.service.StockService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.holderzone.holderpaasmdm.common.constant.RequestConstants.RESPONSE_BASE_CODE;
import static com.holderzone.holderpaasmdm.common.constant.RequestConstants.RESPONSE_BASE_DATA;


/**
 * Description: 库存 ServiceImpl
 * Author: 向超
 * Date: 2025/01/16 15:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StockServiceImpl implements StockService {

    private final CommonService commonService;
    private final RetryTemplate retryTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<InboundBaseVO> queryInboundsList(QueryInboundListDTO queryInboundListDTO) {
        return queryOrderRefundInboundNumberInfo(queryInboundListDTO.getRefundNumberList());
    }

    /**
     * 查询退款单对应的入库单信息
     *
     * @param orderRefundNumberList 订单退款号列表
     * @return 退款单对应的入库单信息
     */
    private List<InboundBaseVO> queryOrderRefundInboundNumberInfo(List<String> orderRefundNumberList) {
        return retryTemplate.execute(context -> parseJsonToInboundInfoMap(
                commonService.queryOrderRefundInboundNumberInfo(orderRefundNumberList)), context -> {
            // 兜底逻辑
            log.error("查询退款单对应的入库单信息过程中有报错，错误信息：", context.getLastThrowable());
            return Collections.emptyList();
        });
    }

    /**
     * 将 JSON 字符串解析为 入库信息Map
     *
     * @param jsonString JSON 字符串
     * @return Map
     */
    @SneakyThrows
    public List<InboundBaseVO> parseJsonToInboundInfoMap(String jsonString) {
        // 解析 JSON 字符串为 Map
        Map<String, Object> responseMap = objectMapper.readValue(jsonString, new TypeReference<>() {});
        if (responseMap == null || (!Objects.equals(responseMap.get(RESPONSE_BASE_CODE), 0) ||
                responseMap.get(RESPONSE_BASE_DATA) == null)) {
            return Collections.emptyList();
        }
        // 解析 JSON 字符串为 List<InboundBaseVO>
        return objectMapper.readValue(
                objectMapper.writeValueAsString(responseMap.get(RESPONSE_BASE_DATA)), new TypeReference<>() {}
        );
    }
}

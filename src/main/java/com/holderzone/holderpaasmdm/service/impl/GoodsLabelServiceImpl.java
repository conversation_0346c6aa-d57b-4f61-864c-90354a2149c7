package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.converter.GoodsLabelConverter;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.mapper.GoodsLabelMapper;
import com.holderzone.holderpaasmdm.mapper.service.GoodsLabelMapperService;
import com.holderzone.holderpaasmdm.model.po.GoodsLabelPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsLabelVO;
import com.holderzone.holderpaasmdm.service.GoodsLabelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 商品标签service实现类
 *
 * <AUTHOR>
 * @date 2025/6/17 10:50
 **/
@Service
@RequiredArgsConstructor
public class GoodsLabelServiceImpl implements GoodsLabelService {

    private final GoodsLabelMapperService goodsLabelMapperService;
    private final GoodsLabelConverter goodsLabelConverter;

    @Override
    public List<GoodsLabelVO> findAll() {
        // 查询所有启用的数据
        List<GoodsLabelPO> labelPOList = goodsLabelMapperService.lambdaQuery()
                .eq(GoodsLabelPO::getIsEnable, Boolean.TRUE)
                .list();
        return goodsLabelConverter.toGoodsLabelVOList(labelPOList);
    }

    @Override
    public GoodsLabelVO findById(Integer id) {
        GoodsLabelPO labelPO = goodsLabelMapperService.getById(id);
        if (Objects.isNull(labelPO) || !Boolean.TRUE.equals(labelPO.getIsEnable())) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "标签不存在或未开启");
        }
        return goodsLabelConverter.toGoodsLabelVO(labelPO);
    }
}

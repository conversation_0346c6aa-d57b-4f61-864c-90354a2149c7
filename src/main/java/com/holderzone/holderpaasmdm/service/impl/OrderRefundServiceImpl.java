package com.holderzone.holderpaasmdm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.holderpaasmdm.common.utils.CommonUtils;
import com.holderzone.holderpaasmdm.common.utils.DateUtils;
import com.holderzone.holderpaasmdm.converter.OrderRefundConverter;
import com.holderzone.holderpaasmdm.converter.OrderRefundDiscountConverter;
import com.holderzone.holderpaasmdm.converter.OrderRefundItemConverter;
import com.holderzone.holderpaasmdm.converter.OrderRefundPaymentConverter;
import com.holderzone.holderpaasmdm.enumeraton.OrderRefundExpand;
import com.holderzone.holderpaasmdm.mapper.service.*;
import com.holderzone.holderpaasmdm.model.bo.QueryOrderRefundListBO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundDetailsDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundExceptionNumberDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundListDTO;
import com.holderzone.holderpaasmdm.model.po.*;
import com.holderzone.holderpaasmdm.model.vo.*;
import com.holderzone.holderpaasmdm.service.CommonService;
import com.holderzone.holderpaasmdm.service.OrderRefundService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.holderpaasmdm.common.constant.RequestConstants.*;
import static java.util.stream.Collectors.toMap;


/**
 * Description: 订单退款 ServiceImpl
 * Author: 向超
 * Date: 2025/01/14 15:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderRefundServiceImpl implements OrderRefundService {
    private final OrderRefundItemMapperService orderRefundItemMapperService;
    private final OrderItemMapperService orderItemMapperService;
    private final OrderRefundDiscountMapperService orderRefundDiscountMapperService;
    private final OrderRefundMapperService orderRefundMapperService;
    private final OrderRefundPaymentMapperService orderRefundPaymentMapperService;
    private final OrdersMapperService ordersMapperService;
    private final OrderRefundConverter orderRefundConverter;
    private final OrderRefundItemConverter orderRefundItemConverter;
    private final OrderRefundPaymentConverter orderRefundPaymentConverter;
    private final OrderRefundDiscountConverter orderRefundDiscountConverter;
    private final RetryTemplate retryTemplate;
    private final CommonService commonService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public PageRespBaseVO<OrderRefundExtendVO> queryOrderRefundList(QueryOrderRefundListDTO queryOrderRefundListDTO) {
        if (queryOrderRefundListDTO.getTimeSearch() != null) {
            Map<String, Date> startAndEndDateTimeByTimeShortcut = DateUtils.getStartAndEndDateTimeByTimeShortcut(
                    queryOrderRefundListDTO.getTimeSearch());
            queryOrderRefundListDTO.setStartTime(startAndEndDateTimeByTimeShortcut.get("startTime"));
            queryOrderRefundListDTO.setEndTime(startAndEndDateTimeByTimeShortcut.get("endTime"));
        } else {
            if (Objects.isNull(queryOrderRefundListDTO.getStartTime())) {
                queryOrderRefundListDTO.setStartTime(DateUtils.getTwoYearsAgoDate());
            }
        }
        QueryOrderRefundListBO queryOrderRefundListBO = orderRefundConverter.toQueryOrderRefundListBO(queryOrderRefundListDTO);
        // 需要先根据keywords搜索匹配的商品名称或者条码对应的退单号
        if (StringUtils.isNotEmpty(queryOrderRefundListDTO.getKeywords())) {
            queryOrderRefundListBO.setOrderRefundNumberList(
                    orderRefundMapperService.queryOrderRefundNumberListByKeywordsForNameOrBarcode(CommonUtils.assembleSqlLikeString(queryOrderRefundListDTO.getKeywords())));
        }
        Integer totalCount = orderRefundMapperService.queryOrderRefundListCount(queryOrderRefundListBO);
        if (totalCount == 0) {
            return new PageRespBaseVO<>(queryOrderRefundListDTO);
        }
        List<OrderRefundPO> orderRefundPOList = orderRefundMapperService.queryOrderRefundList(queryOrderRefundListBO);
        List<OrderRefundExtendVO> orderRefundExtendVOList = orderRefundConverter.toOrderRefundExtendVOList(orderRefundPOList);
        if (CollectionUtils.isEmpty(orderRefundExtendVOList)) {
            return new PageRespBaseVO<>(queryOrderRefundListDTO);
        }
        Map<String, OrdersPO> ordersPOMap = getOrdersPOMap(orderRefundExtendVOList);
        assembleOrderInfo(orderRefundExtendVOList, ordersPOMap);
        if (CollectionUtils.isEmpty(queryOrderRefundListDTO.getRefundExpandList())) {
            return new PageRespBaseVO<>(orderRefundExtendVOList, totalCount, queryOrderRefundListDTO.getLimit(), queryOrderRefundListDTO.getPage());
        }
        List<String> orderRefundNumberList = orderRefundExtendVOList.stream().map(OrderRefundExtendVO::getRefundNumber).toList();
        // 封装订单退款客户信息
        assembleOrderRefundMemberInfo(orderRefundExtendVOList, ordersPOMap);
        // 封装订单退款商品信息
        assembleOrderRefundItemInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundListDTO.getRefundExpandList());
        // 封装订单退款支付信息
        assembleOrderRefundPaymentInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundListDTO.getRefundExpandList());
        // 封装订单退款优惠信息
        assembleOrderRefundDiscountInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundListDTO.getRefundExpandList());
        // 封装订单入库单号信息
        assembleOrderRefundInboundNumberInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundListDTO.getRefundExpandList());
        return new PageRespBaseVO<>(orderRefundExtendVOList, totalCount, queryOrderRefundListDTO.getLimit(), queryOrderRefundListDTO.getPage());
    }

    /**
     * 获取订单信息
     *
     * @param orderRefundExtendVOList 订单退款信息
     * @return 订单信息
     */
    private Map<String, OrdersPO> getOrdersPOMap(List<OrderRefundExtendVO> orderRefundExtendVOList) {
        return ordersMapperService.lambdaQuery()
                .in(OrdersPO::getOrderNumber, orderRefundExtendVOList.stream().map(OrderRefundExtendVO::getOrderNumber).collect(Collectors.toSet()))
                .list().stream()
                .collect(toMap(OrdersPO::getOrderNumber, Function.identity()));
    }

    /**
     * 封装订单信息
     *
     * @param orderRefundExtendVOList 订单退款信息
     * @param ordersPOMap             订单信息
     */
    private static void assembleOrderInfo(List<OrderRefundExtendVO> orderRefundExtendVOList, Map<String, OrdersPO> ordersPOMap) {
        for (OrderRefundExtendVO orderRefundExtendVO : orderRefundExtendVOList) {
            OrdersPO ordersPO = ordersPOMap.get(orderRefundExtendVO.getOrderNumber());
            if (Objects.nonNull(ordersPO)) {
                orderRefundExtendVO.setOrderSource(ordersPO.getOrderSource());
            }
        }
    }

    /**
     * 封装订单退款客户信息
     *
     * @param orderRefundExtendVOList 订单退款信息
     */
    private void assembleOrderRefundMemberInfo(List<OrderRefundExtendVO> orderRefundExtendVOList, Map<String, OrdersPO> ordersPOMap) {
        for (OrderRefundExtendVO orderRefundExtendVO : orderRefundExtendVOList) {
            OrdersPO ordersPO = ordersPOMap.get(orderRefundExtendVO.getOrderNumber());
            if (Objects.nonNull(ordersPO)) {
                orderRefundExtendVO.setMemberName(ordersPO.getMemberName());
                orderRefundExtendVO.setMemberPhone(ordersPO.getMemberPhone());
            }
        }
    }

    @Override
    public Integer queryOrderRefundExceptionNumber(QueryOrderRefundExceptionNumberDTO queryDTO) {
        if (queryDTO.getTimeSearch() != null) {
            Map<String, Date> startAndEndDateTimeByTimeShortcut = DateUtils.getStartAndEndDateTimeByTimeShortcut(
                    queryDTO.getTimeSearch());
            queryDTO.setEndTime(startAndEndDateTimeByTimeShortcut.get("endTime"));
            queryDTO.setStartTime(startAndEndDateTimeByTimeShortcut.get("startTime"));
        } else {
            if (Objects.isNull(queryDTO.getStartTime())) {
                queryDTO.setStartTime(DateUtils.getTwoYearsAgoDate());
            }
        }
        return orderRefundMapperService.queryOrderRefundExceptionNumber(orderRefundConverter.toQueryOrderRefundListBO(queryDTO));
    }

    @Override
    public OrderRefundExtendVO queryOrderRefundDetails(QueryOrderRefundDetailsDTO queryOrderRefundDetailsDTO) {
        List<OrderRefundPO> orderRefundPOList = orderRefundMapperService.lambdaQuery()
                .eq(OrderRefundPO::getRefundNumber, queryOrderRefundDetailsDTO.getRefundNumber())
                .list();
        if (CollectionUtils.isEmpty(orderRefundPOList)) {
            return null;
        }
        OrderRefundExtendVO orderRefundExtendVO = orderRefundConverter.toOrderRefundExtendVO(orderRefundPOList.get(0));
        List<OrderRefundExtendVO> orderRefundExtendVOList = List.of(orderRefundExtendVO);
        List<String> orderRefundNumberList = List.of(queryOrderRefundDetailsDTO.getRefundNumber());
        // 封装订单退款客户信息
        Map<String, OrdersPO> ordersPOMap = getOrdersPOMap(orderRefundExtendVOList);
        assembleOrderRefundMemberInfo(orderRefundExtendVOList, ordersPOMap);
        // 封装订单退款商品信息
        assembleOrderRefundItemInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundDetailsDTO.getRefundExpandList());
        // 封装订单退款支付信息
        assembleOrderRefundPaymentInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundDetailsDTO.getRefundExpandList());
        // 封装订单退款优惠信息
        assembleOrderRefundDiscountInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundDetailsDTO.getRefundExpandList());
        // 封装订单入库单号信息
        assembleOrderRefundInboundNumberInfo(orderRefundExtendVOList, orderRefundNumberList, queryOrderRefundDetailsDTO.getRefundExpandList());
        return orderRefundExtendVO;
    }

    /**
     * 封装订单退款优惠信息
     *
     * @param orderRefundExtendVOList 订单退款信息
     * @param orderRefundNumberList   订单退款号列表
     * @param refundExpandList        订单退款展开信息
     */
    private void assembleOrderRefundInboundNumberInfo(List<OrderRefundExtendVO> orderRefundExtendVOList,
                                                      List<String> orderRefundNumberList, List<OrderRefundExpand> refundExpandList) {
        if (!refundExpandList.contains(OrderRefundExpand.INBOUND_NUMBER)) {
            return;
        }
        Map<String, List<OrderRefundExtendVO.InboundInfo>> refundNumberAndInboundInfoMap = queryOrderRefundInboundNumberInfo(orderRefundNumberList);
        orderRefundExtendVOList.forEach(orderRefundExtendVO ->
                orderRefundExtendVO.setInboundNumberList(refundNumberAndInboundInfoMap.get(orderRefundExtendVO.getRefundNumber())));
    }

    /**
     * 查询退款单对应的入库单信息
     *
     * @param orderRefundNumberList 订单退款号列表
     * @return 退款单对应的入库单信息
     */
    private Map<String, List<OrderRefundExtendVO.InboundInfo>> queryOrderRefundInboundNumberInfo(List<String> orderRefundNumberList) {
        return retryTemplate.execute(context -> parseJsonToInboundInfoMap(
                commonService.queryOrderRefundInboundNumberInfo(orderRefundNumberList)), context -> {
            // 兜底逻辑
            log.error("查询退款单对应的入库单信息过程中有报错，错误信息：", context.getLastThrowable());
            return Collections.emptyMap();
        });
    }

    /**
     * 将 JSON 字符串解析为 入库信息Map
     *
     * @param jsonString JSON 字符串
     * @return Map
     */
    @SneakyThrows
    public Map<String, List<OrderRefundExtendVO.InboundInfo>> parseJsonToInboundInfoMap(String jsonString) {
        // 解析 JSON 字符串为 Map
        Map<String, Object> responseMap = objectMapper.readValue(jsonString, new TypeReference<>() {
        });
        if (responseMap == null || (!Objects.equals(responseMap.get(RESPONSE_BASE_CODE), 0) ||
                responseMap.get(RESPONSE_BASE_DATA) == null)) {
            return Collections.emptyMap();
        }
        // 获取 data 列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) responseMap.get(RESPONSE_BASE_DATA);
        // 构建结果 Map
        Map<String, List<OrderRefundExtendVO.InboundInfo>> resultMap = new HashMap<>();
        for (Map<String, Object> data : dataList) {
            String refundNumber = (String) data.get(REQUEST_PARAM_REFUND_NUMBER);
            Integer id = (Integer) data.get(RESPONSE_BUSINESS_ID);
            String code = (String) data.get(RESPONSE_BUSINESS_CODE);
            OrderRefundExtendVO.InboundInfo inboundInfo = new OrderRefundExtendVO.InboundInfo();
            inboundInfo.setId(id);
            inboundInfo.setCode(code);
            resultMap.computeIfAbsent(refundNumber, k -> new ArrayList<>()).add(inboundInfo);
        }
        return resultMap;
    }

    /**
     * 封装订单退款优惠信息
     *
     * @param orderRefundExtendVOList 订单退款信息
     * @param orderRefundNumberList   订单退款号列表
     * @param refundExpandList        订单退款展开信息
     */
    private void assembleOrderRefundDiscountInfo(List<OrderRefundExtendVO> orderRefundExtendVOList,
                                                 List<String> orderRefundNumberList, List<OrderRefundExpand> refundExpandList) {
        if (!refundExpandList.contains(OrderRefundExpand.REFUND_DISCOUNT)) {
            return;
        }
        Map<String, List<OrderRefundDiscountVO>> refundNumberAndDiscountVOListMap =
                orderRefundDiscountConverter.toOrderRefundDiscountVOList(orderRefundDiscountMapperService.lambdaQuery()
                                .in(OrderRefundDiscountPO::getRefundNumber, orderRefundNumberList).list())
                        .stream().collect(Collectors.groupingBy(OrderRefundDiscountVO::getRefundNumber));
        orderRefundExtendVOList.forEach(orderRefundExtendVO -> orderRefundExtendVO.setRefundDiscountList(
                refundNumberAndDiscountVOListMap.get(orderRefundExtendVO.getRefundNumber())));
    }

    /**
     * 封装订单退款支付信息
     *
     * @param orderRefundExtendVOList 订单退款信息
     * @param orderRefundNumberList   订单退款号列表
     * @param refundExpandList        订单退款展开信息
     */
    private void assembleOrderRefundPaymentInfo(List<OrderRefundExtendVO> orderRefundExtendVOList,
                                                List<String> orderRefundNumberList, List<OrderRefundExpand> refundExpandList) {
        if (!refundExpandList.contains(OrderRefundExpand.REFUND_PAYMENT)) {
            return;
        }
        Map<String, List<OrderRefundPaymentVO>> refundNumberAndPaymentVOListMap =
                orderRefundPaymentConverter.toOrderRefundPaymentVOList(
                                orderRefundPaymentMapperService.lambdaQuery()
                                        .in(OrderRefundPaymentPO::getRefundNumber, orderRefundNumberList)
                                        .list())
                        .stream().collect(Collectors.groupingBy(OrderRefundPaymentVO::getRefundNumber));
        orderRefundExtendVOList.forEach(orderRefundExtendVO -> orderRefundExtendVO.setRefundPaymentList(
                refundNumberAndPaymentVOListMap.get(orderRefundExtendVO.getRefundNumber())));
    }

    /**
     * 封装订单退款商品信息
     *
     * @param orderRefundExtendVOList 订单退款信息
     * @param orderRefundNumberList   订单退款号列表
     * @param refundExpandList        订单退款展开信息
     */
    private void assembleOrderRefundItemInfo(List<OrderRefundExtendVO> orderRefundExtendVOList,
                                             List<String> orderRefundNumberList, List<OrderRefundExpand> refundExpandList) {
        if (!refundExpandList.contains(OrderRefundExpand.REFUND_GOODS)) {
            return;
        }
        List<OrderRefundItemVO> orderRefundItemVOList = orderRefundItemConverter.toOrderRefundItemVOList(
                orderRefundItemMapperService.lambdaQuery().in(OrderRefundItemPO::getRefundNumber, orderRefundNumberList).list());
        Set<Integer> orderItemIdSet = orderRefundItemVOList.stream().map(OrderRefundItemVO::getOrderItemId).collect(Collectors.toSet());
        Map<Integer, OrderItemPO> orderItemIdAndPictureUrlMap = orderItemMapperService.lambdaQuery()
                .select(OrderItemPO::getRecordId,
                        OrderItemPO::getPictureUrl,
                        OrderItemPO::getGoodsId,
                        OrderItemPO::getGoodsSaleName,
                        OrderItemPO::getSellingPrice,
                        OrderItemPO::getBarcode,
                        OrderItemPO::getGoodsUnitId,
                        OrderItemPO::getGoodsUnitName,
                        OrderItemPO::getPurchaseQuantity,
                        OrderItemPO::getActualReceivePrice)
                .in(OrderItemPO::getRecordId, orderItemIdSet).list()
                .stream().collect(toMap(OrderItemPO::getRecordId, Function.identity()));
        Map<String, List<OrderRefundItemVO>> refundNumberAndItemVOListMap = new HashMap<>();
        for (OrderRefundItemVO orderRefundItemVO : orderRefundItemVOList) {
            OrderItemPO orderItemPO = orderItemIdAndPictureUrlMap.get(orderRefundItemVO.getOrderItemId());
            orderRefundItemVO.setPictureUrl(orderItemPO.getPictureUrl());
            orderRefundItemVO.setGoodsId(orderItemPO.getGoodsId());
            orderRefundItemVO.setGoodsSaleName(orderItemPO.getGoodsSaleName());
            orderRefundItemVO.setSellingPrice(orderItemPO.getSellingPrice());
            orderRefundItemVO.setBarcode(orderItemPO.getBarcode());
            orderRefundItemVO.setGoodsUnitId(orderItemPO.getGoodsUnitId());
            orderRefundItemVO.setGoodsUnitName(orderItemPO.getGoodsUnitName());
            orderRefundItemVO.setPurchaseQuantity(orderItemPO.getPurchaseQuantity());
            orderRefundItemVO.setActualReceivePrice(orderItemPO.getActualReceivePrice());
            refundNumberAndItemVOListMap.computeIfAbsent(orderRefundItemVO.getRefundNumber(), k -> new ArrayList<>()).add(orderRefundItemVO);
        }
        orderRefundExtendVOList.forEach(
                orderRefundExtendVO -> orderRefundExtendVO.setRefundGoodsList(
                        refundNumberAndItemVOListMap.get(orderRefundExtendVO.getRefundNumber())));
    }
}

package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.converter.SaleCategoryConverter;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.mapper.service.*;
import com.holderzone.holderpaasmdm.model.bo.QueryStoreGoodsForAvailableForSaleWeighListBO;
import com.holderzone.holderpaasmdm.model.bo.QueryStorePosGoodsAvailableForSaleBO;
import com.holderzone.holderpaasmdm.model.dto.*;
import com.holderzone.holderpaasmdm.model.po.*;
import com.holderzone.holderpaasmdm.model.vo.*;
import com.holderzone.holderpaasmdm.service.CommonService;
import com.holderzone.holderpaasmdm.service.SaleCategoryService;
import com.holderzone.holderpaasmdm.service.UploadFilesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toSet;


/**
 * Description: 销售分组 ServiceImpl
 * Author: 向超
 * Date: 2024/11/28 15:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SaleCategoryServiceImpl implements SaleCategoryService {
    private final CommonService commonService;
    private final StoreSalesProgramGoodsMapperService storeSalesProgramGoodsMapperService;
    private final StoreGoodsMapperService storeGoodsMapperService;
    private final SaleCategoryAndStoreGoodsMapperService saleCategoryAndStoreGoodsMapperService;
    private final SaleCategoryMapperService saleCategoryMapperService;
    private final StoreSalesProgramMapperService storeSalesProgramMapperService;
    private final SaleCategoryConverter saleCategoryConverter;
    private final UploadFilesService uploadFilesService;

    @Override
    public List<SaleCategoryTreeVO> querySaleCategoryOnSaleTree(QuerySaleCategoryOnSaleTreeDTO querySaleCategoryOnSaleTreeDTO) {
        // 查询默认销售方案 以及 自定义销售方案和当前时间段对应的销售方案的时间段序号的映射
        Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair =
                commonService.queryDefaultSalesProgramAndCustomSalesProgramAndTimeSerialNumberPair(
                        querySaleCategoryOnSaleTreeDTO.getStoreId(), querySaleCategoryOnSaleTreeDTO.getChannelId());
        List<Integer> saleProgramIdList = getSaleProgramIdsByPair(resultPair);
        // 根据进行中的销售方案查询对应的销售方案商品IdList
        List<Integer> storeGoodsForSaleCategoryIdList = storeSalesProgramGoodsMapperService.queryGoodsIdsByProgramIdIn(saleProgramIdList);
        if (storeGoodsForSaleCategoryIdList.isEmpty()) {
            return Collections.emptyList();
        }
        // 根据商品IdList查询正常状态且已上架的商品IdList
        List<Integer> storeGoodsIdList = storeGoodsMapperService.queryOnSaleIdsByIdIn(storeGoodsForSaleCategoryIdList);
        if (storeGoodsIdList.isEmpty()) {
            return Collections.emptyList();
        }
        // 根据商品IdList查询对应的销售分组树
        List<Integer> categoryIdList = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getSaleCategoryId)
                .in(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, storeGoodsIdList)
                .list().stream().map(SaleCategoryAndStoreGoodsPO::getSaleCategoryId).distinct().toList();
        if (categoryIdList.isEmpty()) {
            log.error("根据商品IdList查询对应的销售分组树为空");
            throw new StoreBaseException(ResponseCode.COMMON_BAD_REQUEST, "销售分组树为空");
        }
        List<SaleCategoryTreeVO> saleCategoryTreeVOList =
                saleCategoryConverter.toSaleCategoryTreeVOList(
                        saleCategoryMapperService.queryGenerationsVOListByIdListAndChannelId(
                                categoryIdList, querySaleCategoryOnSaleTreeDTO.getChannelId()));
        // 递归处理
        List<SaleCategoryTreeVO> treeVOList = setCategoryLevel(saleCategoryTreeVOList);
        // 设置全部数据
        for (SaleCategoryTreeVO treeVO : treeVOList) {
            setTreeListAll(treeVO);
        }
        // 需要添加一个全部类型的节点
        SaleCategoryTreeVO allTypeSaleCategoryTreeVO = new SaleCategoryTreeVO();
        allTypeSaleCategoryTreeVO.setName("全部");
        allTypeSaleCategoryTreeVO.setCategoryId(Collections.emptyList());
        allTypeSaleCategoryTreeVO.setChildren(Collections.emptyList());
        treeVOList.add(0, allTypeSaleCategoryTreeVO);
        return treeVOList;
    }

    @Override
    public List<SaleCategoryTreeVO> querySaleCategoryTree(QuerySaleCategoryTreeDTO querySaleCategoryTreeDTO,
                                                          boolean hasAll) {
        // 查询数据
        List<SaleCategoryPO> categoryPOList = findByStoreAndChannel(querySaleCategoryTreeDTO.getChannelId(),
                querySaleCategoryTreeDTO.getStoreId());
        if (categoryPOList.isEmpty()) {
            return List.of();
        }
        // 查询销售分组树
        List<SaleCategoryTreeVO> saleCategoryTreeVOList = saleCategoryConverter.toSaleCategoryTreeVOList(categoryPOList);
        // 查询图片数据
        Map<Integer, GoodsPictureVO> pictureVOMap = findPictureInfo(categoryPOList, querySaleCategoryTreeDTO.getIsQueryPicture());
        saleCategoryTreeVOList.forEach(item -> item.setPicture(pictureVOMap.get(item.getCoverId())));
        // 遍历数据
        List<SaleCategoryTreeVO> parentSaleCategoryTreeVOList;
        // 树结构展示
        if (Boolean.TRUE.equals(querySaleCategoryTreeDTO.getIsTree())) {
            // 递归查询树结构
            parentSaleCategoryTreeVOList = setCategoryLevel(saleCategoryTreeVOList);
        } else {
            // 平铺展示
            parentSaleCategoryTreeVOList = saleCategoryTreeVOList;
        }
        if (hasAll) {
            // 需要添加一个全部类型的节点
            SaleCategoryTreeVO allTypeSaleCategoryTreeVO = new SaleCategoryTreeVO();
            allTypeSaleCategoryTreeVO.setName("全部");
            parentSaleCategoryTreeVOList.add(0, allTypeSaleCategoryTreeVO);
        }
        return parentSaleCategoryTreeVOList;
    }

    @Override
    public List<SaleCategoryTreeVO> querySaleCategoryAvailableForSaleTree(
            QuerySaleCategoryAvailableForSaleTreeDTO querySaleCategoryAvailableForSaleTreeDTO) {
        if (Objects.isNull(querySaleCategoryAvailableForSaleTreeDTO.getStoreSalesProgramId())) {
            StoreSalesProgramPO defaultStoreSalesProgramPO = storeSalesProgramMapperService.lambdaQuery()
                    .eq(StoreSalesProgramPO::getStoreId, querySaleCategoryAvailableForSaleTreeDTO.getStoreId())
                    .eq(StoreSalesProgramPO::getChannelId, querySaleCategoryAvailableForSaleTreeDTO.getChannelId())
                    .eq(StoreSalesProgramPO::getIsDefault, Boolean.TRUE)
                    .one();
            if (defaultStoreSalesProgramPO == null) {
                return Collections.emptyList();
            }
            querySaleCategoryAvailableForSaleTreeDTO.setStoreSalesProgramId(defaultStoreSalesProgramPO.getId());
        }
        // 默认查询POS渠道的默认销售方案

        List<Integer> saleCategoryIdList = saleCategoryMapperService.querySaleCategoryIdBySaleProgramIdAndChannelIdAndStoreId(
                querySaleCategoryAvailableForSaleTreeDTO.getStoreSalesProgramId(),
                querySaleCategoryAvailableForSaleTreeDTO.getChannelId(),
                querySaleCategoryAvailableForSaleTreeDTO.getStoreId());
        if (saleCategoryIdList.isEmpty()) {
            return Collections.emptyList();
        }
        // 查询销售分组的所有父节点(世袭)且转为树结构VO
        List<SaleCategoryTreeVO> saleCategoryTreeVOList = saleCategoryConverter.toSaleCategoryTreeVOList(
                saleCategoryMapperService.queryGenerationsVOListByIdListAndChannelId(saleCategoryIdList,
                        querySaleCategoryAvailableForSaleTreeDTO.getChannelId()));
        // 开始组装树结构，递归处理
        List<SaleCategoryTreeVO> allParentSaleCategoryTreeVOList = setCategoryLevel(saleCategoryTreeVOList);
        // 需要在第一层级添加一个全部类型的节点
        SaleCategoryTreeVO allTypeSaleCategoryTreeVO = new SaleCategoryTreeVO();
        allTypeSaleCategoryTreeVO.setName("全部分组");
        allParentSaleCategoryTreeVOList.add(0, allTypeSaleCategoryTreeVO);
        return allParentSaleCategoryTreeVOList;
    }

    @Override
    public List<SaleCategoryTreeVO> querySaleCategoryAvailableForSaleWeighTree(
            QuerySaleCategoryAvailableForSaleWeighTreeDTO querySaleCategoryAvailableForSaleWeighTreeDTO) {
        List<StoreGoodsPO> storeGoodsPOList = commonService.queryStoreGoodsForAvailableForSaleWeighList(
                QueryStoreGoodsForAvailableForSaleWeighListBO.builder()
                        .storeId(querySaleCategoryAvailableForSaleWeighTreeDTO.getStoreId())
                        .channelId(querySaleCategoryAvailableForSaleWeighTreeDTO.getChannelId()).build());
        if (CollectionUtils.isEmpty(storeGoodsPOList)) {
            return Collections.emptyList();
        }
        List<Integer> allStoreGoodsIdList = storeGoodsPOList.stream().map(StoreGoodsPO::getId).distinct().toList();
        List<Integer> allSaleCategoryIdList = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getSaleCategoryId)
                .in(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, allStoreGoodsIdList)
                .list().stream().map(SaleCategoryAndStoreGoodsPO::getSaleCategoryId).distinct().toList();
        List<SaleCategoryTreeVO> saleCategoryTreeVOList = saleCategoryConverter.toSaleCategoryTreeVOList(
                saleCategoryMapperService.queryGenerationsVOListByIdListAndChannelId(allSaleCategoryIdList,
                        querySaleCategoryAvailableForSaleWeighTreeDTO.getChannelId()));
        // 递归处理
        return setCategoryLevel(saleCategoryTreeVOList);
    }

    @Override
    public List<SaleCategoryTreeVO> querySaleCategoryPosGoodsAvailableForSaleTree(QuerySaleCategoryPosGoodsAvailableForSaleTreeDTO queryDTO) {
        List<SaleCategoryPO> categoryPOS = findByStoreAndChannel(queryDTO.getChannelId(), queryDTO.getStoreId());

        List<SaleCategoryTreeVO> saleCategoryTreeVOList = saleCategoryConverter.toSaleCategoryTreeVOList(categoryPOS);
        List<Integer> allSaleCategoryIdList = new ArrayList<>();
        List<SaleCategoryTreeVO> allParentSaleCategoryTreeVOList = new ArrayList<>();
        saleCategoryTreeVOList.sort(Comparator.comparing(SaleCategoryVO::getSort).reversed());
        saleCategoryTreeVOList.forEach(saleCategoryTreeVO -> {
            allSaleCategoryIdList.add(saleCategoryTreeVO.getId());
            if (saleCategoryTreeVO.getParentId() == 0) {
                allParentSaleCategoryTreeVOList.add(saleCategoryTreeVO);
            }
        });
        for (SaleCategoryTreeVO saleCategoryTreeVO : allParentSaleCategoryTreeVOList) {
            saleCategoryTreeVO.setChildren(saleCategoryTreeVOList.stream()
                    .filter(childSaleCategoryTreeVO -> childSaleCategoryTreeVO.getParentId().equals(saleCategoryTreeVO.getId()))
                    .collect(Collectors.toList()));
        }
        StoreSalesProgramPO defaultStoreSalesProgramPO = storeSalesProgramMapperService.lambdaQuery()
                .eq(StoreSalesProgramPO::getStoreId, queryDTO.getStoreId())
                .eq(StoreSalesProgramPO::getChannelId, queryDTO.getChannelId())
                .eq(StoreSalesProgramPO::getIsDefault, true)
                .one();
        if (defaultStoreSalesProgramPO == null) {
            throw new StoreBaseException(ResponseCode.COMMON_NOT_FOUND, "POS通道未设置默认销售方案");
        }
        Map<Integer, List<StoreSalesProgramGoodsPO>> storeGoodsIdAndInfoMap = new HashMap<>();
        QueryStorePosGoodsAvailableForSaleBO queryStorePosGoodsAvailableForSaleBO = QueryStorePosGoodsAvailableForSaleBO.builder()
                .keywords(queryDTO.getKeywords())
                .brandIdList(queryDTO.getBrandIdList())
                .saleCategoryId(null)
                .storeSource(queryDTO.getStoreSource())
                .coverIdList(queryDTO.getCoverIdList())
                .goodsValuationMethodList(queryDTO.getGoodsValuationMethodList())
                .goodsSpec(queryDTO.getGoodsSpec())
                .build();
        List<StoreGoodsExtendVO> storeGoodsExtendVOList =
                commonService.queryStorePosGoodsAvailableForSaleList(queryStorePosGoodsAvailableForSaleBO,
                        defaultStoreSalesProgramPO, storeGoodsIdAndInfoMap);
        if (CollectionUtils.isEmpty(storeGoodsExtendVOList)) {
            return Collections.emptyList();
        }
        // 查询图片数据
        Map<Integer, GoodsPictureVO> pictureInfo = findPictureInfo(categoryPOS, queryDTO.getIsQueryPicture());

        // 根据pos商品列表过滤销售分组树
        filterSaleCategoryByPosGoodsList(storeGoodsExtendVOList, allSaleCategoryIdList, allParentSaleCategoryTreeVOList, pictureInfo);

        return allParentSaleCategoryTreeVOList;
    }

    @Override
    public PageRespVO<SaleCategoryTreeVO> queryCategoryPage(QuerySaleCategoryPageDTO pageDTO) {
        List<SaleCategoryPO> categoryPOList = findByStoreAndChannel(pageDTO.getChannelId(), pageDTO.getStoreId());
        // 查询销售分类信息
        List<SaleCategoryTreeVO> saleCategoryTreeVOList = saleCategoryConverter.toSaleCategoryTreeVOList(categoryPOList);
        if (saleCategoryTreeVOList.isEmpty()) {
            return new PageRespVO<>();
        }
        // 获取所有1及分类
        List<SaleCategoryTreeVO> firstList = saleCategoryTreeVOList.stream()
                .filter(item -> Objects.equals(item.getLevel(), 1))
                .toList();

        // 手动分页
        PageRespVO<SaleCategoryTreeVO> pageRespVO = new PageRespVO<>(firstList, pageDTO.getLimit(), pageDTO.getPage());
        if (pageRespVO.getList().isEmpty()) {
            return pageRespVO;
        }

        // 查询默认销售方案 以及 自定义销售方案和当前时间段对应的销售方案的时间段序号的映射
        Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair =
                commonService.queryDefaultSalesProgramAndCustomSalesProgramAndTimeSerialNumberPair(null, pageDTO.getChannelId());
        List<Integer> saleProgramIdList = getSaleProgramIdsByPair(resultPair);
        // 根据进行中的销售方案查询对应的销售方案商品IdList
        List<Integer> storeGoodsForSaleCategoryIdList = storeSalesProgramGoodsMapperService.queryGoodsIdsByProgramIdIn(saleProgramIdList);
        if (storeGoodsForSaleCategoryIdList.isEmpty()) {
            return pageRespVO;
        }
        // 根据商品IdList查询正常状态且已上架的商品IdList
        List<Integer> storeGoodsIdList = storeGoodsMapperService.queryOnSaleIdsByIdIn(storeGoodsForSaleCategoryIdList);
        if (storeGoodsIdList.isEmpty()) {
            return pageRespVO;
        }

        // 查询分类关联商品信息
        List<Integer> saleCategoryIds = pageRespVO.getList().stream().map(SaleCategoryTreeVO::getId).toList();
        List<SaleCategoryAndStoreGoodsPO> goodsPOList =
                saleCategoryAndStoreGoodsMapperService.querySaleCategoryAndStoreGoodsIn(saleCategoryIds, storeGoodsIdList);
        if (!goodsPOList.isEmpty()) {
            for (SaleCategoryTreeVO treeVO : pageRespVO.getList()) {
                // 计算商品数量
                treeVO.setGoodsCount(getCategoryGoodsCount(goodsPOList, saleCategoryTreeVOList, treeVO.getId()));
            }
        }

        // 获取图片信息
        Map<Integer, GoodsPictureVO> pictureInfo = findPictureInfo(categoryPOList, pageDTO.getIsQueryPicture());
        for (SaleCategoryTreeVO treeVO : pageRespVO.getList()) {
            treeVO.setPicture(pictureInfo.get(treeVO.getCoverId()));
        }
        return pageRespVO;
    }

    @Override
    public SaleCategoryTreeVO queryCategoryDetails(QuerySaleCategoryTreeDTO querySaleCategoryTreeDTO) {
        // 查询数据
        List<SaleCategoryPO> categoryPOList = saleCategoryMapperService.queryByStoreAndChannel(
                querySaleCategoryTreeDTO.getChannelId(), querySaleCategoryTreeDTO.getStoreId());
        // 参数转换
        List<SaleCategoryTreeVO> saleCategoryTreeVOList = saleCategoryConverter.toSaleCategoryTreeVOList(categoryPOList);
        Optional<SaleCategoryTreeVO> voOptional = saleCategoryTreeVOList.stream()
                .filter(item -> Objects.equals(item.getId(), querySaleCategoryTreeDTO.getSaleCategoryId()))
                .findFirst();
        if (voOptional.isEmpty()) {
            throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "销售分组不存在");
        }
        // 获取图片信息
        Map<Integer, GoodsPictureVO> pictureInfo = findPictureInfo(categoryPOList, querySaleCategoryTreeDTO.getIsQueryPicture());
        for (SaleCategoryTreeVO treeVO : saleCategoryTreeVOList) {
            treeVO.setPicture(pictureInfo.get(treeVO.getCoverId()));
        }
        // 递归处理
        SaleCategoryTreeVO categoryTreeVO = voOptional.get();
        categoryTreeVO.setPicture(pictureInfo.get(querySaleCategoryTreeDTO.getSaleCategoryId()));
        setChildren(categoryTreeVO, saleCategoryTreeVOList, Comparator.comparing(SaleCategoryVO::getSort).reversed());
        return categoryTreeVO;
    }

    /**
     * 获取分类下的商品数量
     *
     * @param goodsPOList 关联商品数据
     * @param allList     所有分类数据
     * @param categoryId  分类id
     * @return 商品数量
     */
    private int getCategoryGoodsCount(List<SaleCategoryAndStoreGoodsPO> goodsPOList, List<SaleCategoryTreeVO> allList,
                                      Integer categoryId) {
        // 获取所有子类数据
        Set<Integer> allIds = new HashSet<>();
        allIds.add(categoryId);
        findAllChildrenCategoryIds(allIds, categoryId, allList);
        // 获取分类下的商品数量
        long count = goodsPOList.stream()
                .filter(item -> allIds.contains(item.getSaleCategoryId()))
                .distinct()
                .count();
        return (int) count;
    }

    /**
     * 查询所有分类 id集合
     *
     * @param allIds     所有id集合
     * @param categoryId 父级id
     * @param allList    所有分类数据
     */
    private void findAllChildrenCategoryIds(Set<Integer> allIds, Integer categoryId, List<SaleCategoryTreeVO> allList) {
        List<SaleCategoryTreeVO> children = allList.stream()
                .filter(saleCategoryTreeVO1 -> saleCategoryTreeVO1.getParentId().equals(categoryId))
                .toList();
        if (!children.isEmpty()) {
            allIds.addAll(children.stream().map(SaleCategoryTreeVO::getId).toList());
            for (SaleCategoryTreeVO treeVO : children) {
                findAllChildrenCategoryIds(allIds, treeVO.getId(), allList);
            }
        }
    }

    /**
     * pair中获取销售策略id集合
     *
     * @param resultPair pair
     * @return 销售策略id集合
     */
    private List<Integer> getSaleProgramIdsByPair(Pair<StoreSalesProgramPO, Pair<StoreSalesProgramPO, Integer>> resultPair) {
        List<Integer> saleProgramIdList = new ArrayList<>();
        saleProgramIdList.add(resultPair.getKey().getId());
        if (resultPair.getValue() != null) {
            saleProgramIdList.add(resultPair.getValue().getKey().getId());
        }
        return saleProgramIdList;
    }

    /**
     * 根据pos商品列表过滤销售分组树
     *
     * @param storeGoodsExtendVOList          pos商品列表
     * @param allSaleCategoryIdList           所有销售分组id列表
     * @param allParentSaleCategoryTreeVOList 所有父节点销售分组树VO列表
     * @param pictureInfo                     图片数据
     */
    private void filterSaleCategoryByPosGoodsList(List<StoreGoodsExtendVO> storeGoodsExtendVOList,
                                                  List<Integer> allSaleCategoryIdList,
                                                  List<SaleCategoryTreeVO> allParentSaleCategoryTreeVOList,
                                                  Map<Integer, GoodsPictureVO> pictureInfo) {
        List<Integer> storeGoodsIdList = storeGoodsExtendVOList.stream().map(StoreGoodsExtendVO::getId).distinct().toList();
        // 找到所有需要展示商品的对应的销售分组id和商品id的映射
        Map<Integer, Set<Integer>> saleCategoryIdAndStoreGoodsIdSetMap = saleCategoryAndStoreGoodsMapperService.lambdaQuery()
                .select(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, SaleCategoryAndStoreGoodsPO::getStoreGoodsId)
                .in(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, allSaleCategoryIdList)
                .list()
                .stream()
                .filter(saleCategoryAndStoreGoodsPO -> storeGoodsIdList.contains(saleCategoryAndStoreGoodsPO.getStoreGoodsId()))
                .collect(Collectors.groupingBy(SaleCategoryAndStoreGoodsPO::getSaleCategoryId, mapping(SaleCategoryAndStoreGoodsPO::getStoreGoodsId, toSet())));
        // 规则：如果分组下（包含子级分组）没有商品，则不需要展示该分组
        Iterator<SaleCategoryTreeVO> paraentIterator = allParentSaleCategoryTreeVOList.iterator();
        while (paraentIterator.hasNext()) {
            SaleCategoryTreeVO saleCategoryTreeVO = paraentIterator.next();
            // 设置图片
            saleCategoryTreeVO.setPicture(pictureInfo.get(saleCategoryTreeVO.getCoverId()));
            Set<Integer> storeGoodsIdSet;
            if (saleCategoryIdAndStoreGoodsIdSetMap.containsKey(saleCategoryTreeVO.getId())) {
                storeGoodsIdSet = new HashSet<>(saleCategoryIdAndStoreGoodsIdSetMap.get(saleCategoryTreeVO.getId()));
            } else {
                storeGoodsIdSet = new HashSet<>();
            }
            // 过滤子节点
            filterChildSaleCategoryByPosGoodsList(saleCategoryTreeVO, saleCategoryIdAndStoreGoodsIdSetMap, storeGoodsIdSet);
            if (storeGoodsIdSet.isEmpty()) {
                paraentIterator.remove();
            } else {
                saleCategoryTreeVO.setGoodsCount(storeGoodsIdSet.size());
            }
        }
    }

    /**
     * 根据pos商品列表过滤子节点销售分组树
     *
     * @param saleCategoryTreeVO                  父节点销售分组树VO
     * @param saleCategoryIdAndStoreGoodsIdSetMap 销售分组id和商品id的映射
     * @param storeGoodsIdSet                     商品id集合
     */
    private static void filterChildSaleCategoryByPosGoodsList(SaleCategoryTreeVO saleCategoryTreeVO,
                                                              Map<Integer, Set<Integer>> saleCategoryIdAndStoreGoodsIdSetMap,
                                                              Set<Integer> storeGoodsIdSet) {
        if (CollectionUtils.isEmpty(saleCategoryTreeVO.getChildren())) {
            return;
        }
        Iterator<SaleCategoryTreeVO> childIterator = saleCategoryTreeVO.getChildren().iterator();
        while (childIterator.hasNext()) {
            SaleCategoryTreeVO childSaleCategoryTreeVO = childIterator.next();
            Set<Integer> childStoreGoodsIdSet = saleCategoryIdAndStoreGoodsIdSetMap.get(childSaleCategoryTreeVO.getId());
            if (CollectionUtils.isNotEmpty(childStoreGoodsIdSet)) {
                childSaleCategoryTreeVO.setGoodsCount(childStoreGoodsIdSet.size());
                storeGoodsIdSet.addAll(childStoreGoodsIdSet);
            } else {
                childIterator.remove();
            }
        }
    }

    /**
     * 查询图片信息
     *
     * @param categoryPOList 分类集合
     * @param isQueryPicture 是否查询封面
     * @return 查询结果
     */
    private Map<Integer, GoodsPictureVO> findPictureInfo(List<SaleCategoryPO> categoryPOList,
                                                         Boolean isQueryPicture) {
        if (!Boolean.TRUE.equals(isQueryPicture)) {
            return new HashMap<>();
        }
        List<Integer> coverIds = categoryPOList.stream()
                .map(SaleCategoryPO::getCoverId)
                .filter(Objects::nonNull)
                .toList();
        return uploadFilesService.findFileByIdIn(coverIds);
    }

    /**
     * 渠道和门店id查询所有分类
     *
     * @param channel 渠道id
     * @param storeId 门店id
     * @return 查询结果
     */
    private List<SaleCategoryPO> findByStoreAndChannel(Integer channel, Integer storeId) {
        return saleCategoryMapperService.queryByStoreAndChannel(channel, storeId);
    }

    /**
     * 递归设置值，且新增全部属性
     *
     * @param saleCategoryTreeVO 销售分组对象
     */
    private void setTreeListAll(SaleCategoryTreeVO saleCategoryTreeVO) {
        saleCategoryTreeVO.setCategoryId(List.of(saleCategoryTreeVO.getId()));
        if (CollectionUtils.isNotEmpty(saleCategoryTreeVO.getChildren())) {
            for (SaleCategoryTreeVO child : saleCategoryTreeVO.getChildren()) {
                setTreeListAll(child);
            }
            SaleCategoryTreeVO allTypeSaleCategoryTreeVO = new SaleCategoryTreeVO();
            allTypeSaleCategoryTreeVO.setName("全部");
            allTypeSaleCategoryTreeVO.setCategoryId(saleCategoryTreeVO.getCategoryId());
            saleCategoryTreeVO.getChildren().add(0, allTypeSaleCategoryTreeVO);
        }
    }

    /**
     * 按照层级查询数据信息
     *
     * @param allCategoryTreeVOList 所有分类信息
     * @return 查询结果
     */
    private List<SaleCategoryTreeVO> setCategoryLevel(List<SaleCategoryTreeVO> allCategoryTreeVOList) {
        Comparator<SaleCategoryVO> comparator = Comparator.comparing(SaleCategoryVO::getSort).reversed();
        // 递归处理
        List<SaleCategoryTreeVO> parentSaleCategoryTreeVOList = allCategoryTreeVOList.stream()
                .filter(saleCategoryTreeVO -> saleCategoryTreeVO.getParentId() == 0)
                .sorted(comparator)
                .collect(Collectors.toList());
        for (SaleCategoryTreeVO treeVO : parentSaleCategoryTreeVOList) {
            setChildren(treeVO, allCategoryTreeVOList, comparator);
        }
        return parentSaleCategoryTreeVOList;
    }

    /**
     * 递归查询子集数据
     *
     * @param treeVO                当前数据
     * @param allCategoryTreeVOList 所有销售分类
     * @param comparator            排序
     */
    private void setChildren(SaleCategoryTreeVO treeVO, List<SaleCategoryTreeVO> allCategoryTreeVOList,
                             Comparator<SaleCategoryVO> comparator) {
        // 查询子集数据
        List<SaleCategoryTreeVO> children = allCategoryTreeVOList.stream()
                .filter(saleCategoryTreeVO1 -> saleCategoryTreeVO1.getParentId().equals(treeVO.getId()))
                .sorted(comparator)
                .collect(Collectors.toList());
        if (!children.isEmpty()) {
            for (SaleCategoryTreeVO child : children) {
                setChildren(child, allCategoryTreeVOList, comparator);
            }
            treeVO.setChildren(children);
        } else {
            treeVO.setChildren(new ArrayList<>());
        }
    }
}

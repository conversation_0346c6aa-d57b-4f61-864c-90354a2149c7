package com.holderzone.holderpaasmdm.service.impl;

import com.holderzone.holderpaasmdm.enumeraton.StoreSalesProgramStatus;
import com.holderzone.holderpaasmdm.mapper.service.PlatformSalesChannelMapperService;
import com.holderzone.holderpaasmdm.model.dto.PlatformSalesChannelDTO;
import com.holderzone.holderpaasmdm.model.po.PlatformSalesChannelPO;
import com.holderzone.holderpaasmdm.model.vo.PlatformSalesChannelVO;
import com.holderzone.holderpaasmdm.service.CommonService;
import com.holderzone.holderpaasmdm.service.PlatformSalesChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * Description: 销售渠道 ServiceImpl
 * Author: 向超
 * Date: 2024/12/05 15:36
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PlatformSalesChannelServiceImpl implements PlatformSalesChannelService {
    private final PlatformSalesChannelMapperService platformSalesChannelMapperService;
    private final CommonService commonService;

    @Override
    public Integer querySaleChannelPOSId() {
        return commonService.querySaleChannelPOSId();
    }

    @Override
    public List<PlatformSalesChannelVO> querySaleChannelId(PlatformSalesChannelDTO platformSalesChannelDTO) {
        List<PlatformSalesChannelPO> channelPOList = platformSalesChannelMapperService.<PlatformSalesChannelPO>lambdaQuery()
                .eq(PlatformSalesChannelPO::getIsEnable, StoreSalesProgramStatus.ENABLE.isValue())
                // 渠道名称
                .eq(PlatformSalesChannelPO::getChannelName, platformSalesChannelDTO.getChannelName())
                .list();

        return channelPOList.stream().map(channelPO -> {
            PlatformSalesChannelVO channelVO = new PlatformSalesChannelVO();
            BeanUtils.copyProperties(channelPO, channelVO);
            return channelVO;
        }).toList();
    }
}

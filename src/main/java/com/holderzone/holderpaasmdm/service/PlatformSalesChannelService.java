package com.holderzone.holderpaasmdm.service;


import com.holderzone.holderpaasmdm.model.dto.PlatformSalesChannelDTO;
import com.holderzone.holderpaasmdm.model.vo.PlatformSalesChannelVO;

import java.util.List;

/**
 * Description: 销售渠道 Service
 * Author: 向超
 * Date: 2024/12/05 15:36
 */
public interface PlatformSalesChannelService {
    /**
     * 查询销售渠道 POS ID
     *
     * @return POS ID
     */
    Integer querySaleChannelPOSId();

    /**
     * 根据渠道名称查询销售渠道 ID
     * @return 渠道信息
     */
    List<PlatformSalesChannelVO> querySaleChannelId(PlatformSalesChannelDTO platformSalesChannelDTO);
}

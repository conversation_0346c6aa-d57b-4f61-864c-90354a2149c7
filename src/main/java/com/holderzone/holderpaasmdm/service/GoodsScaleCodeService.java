package com.holderzone.holderpaasmdm.service;

import com.holderzone.holderpaasmdm.model.vo.StoreGoodsExtendVO;

import java.util.List;

/**
 * 门店商品称码 service
 *
 * <AUTHOR>
 * @date 2025/4/16 15:05
 **/
public interface GoodsScaleCodeService {

    /**
     * 封装城内吗数据信息
     *
     * @param storeGoodsExtendVOList 商品数据信息
     * @param storeId                门店id
     */
    void assembleGoodsScaleCode(List<StoreGoodsExtendVO> storeGoodsExtendVOList, Integer storeId);
}

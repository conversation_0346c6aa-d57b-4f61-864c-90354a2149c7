package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.GenerateBatchStoreGoodsPluCodeDTO;
import com.holderzone.holderpaasmdm.model.dto.GenerateStoreGoodsPluCodeDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleMemberListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSalePosListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleWeighDetailDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsAvailableForSaleWeighListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsOnSaleDetailDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsOnSaleFilterListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsOnSaleListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStorePosGoodsAvailableForSaleDetailDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStorePosGoodsAvailableForSaleListDTO;
import com.holderzone.holderpaasmdm.model.dto.StoreGoodsAvailableForSaleAdjustmentPriceDTO;
import com.holderzone.holderpaasmdm.model.dto.VerifyStoreGoodsAvailableForSaleScaleCodeDTO;
import com.holderzone.holderpaasmdm.model.dto.VerifyStoreGoodsOnSaleDTO;
import com.holderzone.holderpaasmdm.model.vo.*;
import com.holderzone.holderpaasmdm.service.StoreGoodsService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Description: 门店商品 Controller
 * Author: 向超
 * Date: 2024/11/25 15:31
 */
@RestController
@RequiredArgsConstructor
public class StoreGoodsController {
    private final StoreGoodsService storeGoodsService;

    /**
     * 根据店铺ID、渠道ID、商品名称、条码、自编码、首字母以及销售分组ID查询在售商品列表
     *
     * @param queryStoreGoodsOnSaleListDTO 查询条件
     * @return 在售商品列表
     */
    @PostMapping("/api/v1/store-goods/on-sale/list/page")
    public Result<PageRespVO<StoreGoodsOnSaleExtendVO>> queryStoreGoodsOnSaleListPage(
            @RequestBody @Validated QueryStoreGoodsOnSaleListDTO queryStoreGoodsOnSaleListDTO) {
        return Result.success(storeGoodsService.queryStoreGoodsOnSaleListPage(queryStoreGoodsOnSaleListDTO));
    }

    /**
     * 根据店铺ID、渠道ID和(店铺商品ID或者条码或者自编码)查询在售商品详情
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询条件
     * @return 在售商品列表
     */
    @PostMapping("/api/v1/store-goods/on-sale/detail")
    public Result<StoreGoodsOnSaleExtendVO> queryStoreGoodsOnSaleDetail(
            @RequestBody @Validated QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO) {
        StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO = storeGoodsService.queryStoreGoodsOnSaleDetail(queryStoreGoodsOnSaleDetailDTO);
        if (storeGoodsOnSaleExtendVO == null) {
            return Result.fail(ResponseCode.COMMON_NOT_FOUND.getValue(), "该商品当前时间不可售!");
        } else {
            return Result.success(storeGoodsOnSaleExtendVO);
        }
    }

    /**
     * 根据店铺ID、渠道ID和(店铺商品ID或者条码或者自编码)查询在售商品详情【云值守自主收银专用】
     *
     * @param queryStoreGoodsOnSaleDetailDTO 查询条件
     * @return 在售商品列表
     */
    @PostMapping("/api/v2/store-goods/on-sale/detail")
    public Result<StoreGoodsOnSaleExtendVO> queryStoreGoodsOnSaleDetailV2(
            @RequestBody @Validated QueryStoreGoodsOnSaleDetailDTO queryStoreGoodsOnSaleDetailDTO) {
        StoreGoodsOnSaleExtendVO storeGoodsOnSaleExtendVO = storeGoodsService.queryStoreGoodsOnSaleDetailV2(queryStoreGoodsOnSaleDetailDTO);
        if (storeGoodsOnSaleExtendVO == null) {
            return Result.fail(ResponseCode.COMMON_NOT_FOUND.getValue(), "该商品当前时间不可售!");
        } else {
            return Result.success(storeGoodsOnSaleExtendVO);
        }
    }

    /**
     * 根据编码(秤内自编码或者条码或者自编码)查询在售商品详情
     *
     * @param queryStoreGoodsOnSaleFilterListDTO 查询条件
     * @return 在售商品列表
     */
    @PostMapping("/api/v1/store-goods/on-sale/filter/list")
    public Result<List<StoreGoodsOnSaleExtendVO>> queryStoreGoodsOnSaleFilterList(
            @RequestBody @Validated QueryStoreGoodsOnSaleFilterListDTO queryStoreGoodsOnSaleFilterListDTO) {
        List<StoreGoodsOnSaleExtendVO> storeGoodsOnSaleExtendVOList =
                storeGoodsService.queryStoreGoodsOnSaleFilterList(queryStoreGoodsOnSaleFilterListDTO);
        if (CollectionUtils.isEmpty(storeGoodsOnSaleExtendVOList)) {
            return Result.fail(ResponseCode.COMMON_NOT_FOUND.getValue(), "该商品当前时间不可售!");
        } else {
            return Result.success(storeGoodsOnSaleExtendVOList);
        }
    }

    /**
     * 快速结账 - 校验商品信息（包含上|下架状态、价格、库存等信息）
     * 当前版本暂不考虑库存问题
     *
     * @param verifyStoreGoodsOnSaleDTO 商品信息
     * @return 在售商品列表
     */
    @PostMapping("/api/v1/store-goods/info/verify")
    public Result<VerifyStoreGoodsOnSaleVO> verifyStoreGoodsOnSaleInfo(
            @RequestBody @Validated VerifyStoreGoodsOnSaleDTO verifyStoreGoodsOnSaleDTO) {
        return Result.success(storeGoodsService.verifyStoreGoodsOnSaleInfo(verifyStoreGoodsOnSaleDTO));
    }

    /**
     * 根据店铺ID、渠道ID、商品类型以及销售分组ID查询可售商品列表 (会员)
     *
     * @param queryStoreGoodsAvailableForSaleMemberListDTO 查询条件
     * @return 可售商品列表
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/member/list/page")
    public Result<PageRespVO<StoreGoodsBaseVO>> queryStoreGoodsAvailableForSaleMemberListPage(
            @RequestBody @Validated QueryStoreGoodsAvailableForSaleMemberListDTO queryStoreGoodsAvailableForSaleMemberListDTO) {
        return Result.success(storeGoodsService.queryStoreGoodsAvailableForSaleMemberListPage(queryStoreGoodsAvailableForSaleMemberListDTO));
    }

    /**
     * 根据店铺ID、渠道ID以及销售分组ID查询可售商品列表 (POS)
     *
     * @param queryStoreGoodsAvailableForSalePosListDTO 查询条件
     * @return 可售商品列表
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/pos/list/page")
    public Result<PageRespVO<StoreGoodsAvailableForSalePOSExtendVO>> queryStoreGoodsAvailableForSalePosListPage(
            @RequestBody @Validated QueryStoreGoodsAvailableForSalePosListDTO queryStoreGoodsAvailableForSalePosListDTO) {
        return Result.success(storeGoodsService.queryStoreGoodsAvailableForSalePosListPage(queryStoreGoodsAvailableForSalePosListDTO));
    }

    /**
     * 可售商品调价
     *
     * @param storeGoodsAvailableForSaleAdjustmentPriceDTO 调价参数
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/price/adjustment")
    public Result<Void> storeGoodsAvailableForSalePriceAdjustment(
            @RequestBody @Validated StoreGoodsAvailableForSaleAdjustmentPriceDTO storeGoodsAvailableForSaleAdjustmentPriceDTO) {
        storeGoodsService.storeGoodsAvailableForSalePriceAdjustment(storeGoodsAvailableForSaleAdjustmentPriceDTO);
        return Result.success();
    }

    /**
     * 可售商品调价记录
     *
     * @param queryStoreGoodsAvailableForSalePriceAdjustmentListDTO 调价参数
     * @return 调价记录列表
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/price/adjustment/list")
    public Result<PageRespVO<StoreGoodsAvailableForSaleAdjustmentPriceVO>> queryStoreGoodsAvailableForSalePriceAdjustmentList(
            @RequestBody @Validated QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO queryStoreGoodsAvailableForSalePriceAdjustmentListDTO) {
        return Result.success(storeGoodsService.queryStoreGoodsAvailableForSalePriceAdjustmentList(
                queryStoreGoodsAvailableForSalePriceAdjustmentListDTO));
    }

    /**
     * 传秤校验商品状态 - 商品秤内码、秤内自编码、计价方法
     *
     * @param verifyStoreGoodsAvailableForSaleScaleCodeDTO 需要校验的参数
     * @return 校验结果
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/scale-code/verify")
    public Result<VerifyStoreGoodsAvailableForSaleScaleCodeVO> verifyStoreGoodsAvailableForSaleScaleCode(
            @RequestBody @Validated VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO) {
        return Result.success(storeGoodsService.verifyStoreGoodsAvailableForSaleScaleCode(verifyStoreGoodsAvailableForSaleScaleCodeDTO));
    }

    /**
     * 传秤校验商品状态 - 商品秤内码、秤内自编码、计价方法
     *
     * @param verifyStoreGoodsAvailableForSaleScaleCodeDTO 需要校验的参数
     * @return 校验结果
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/scale-code/export/verify")
    public Result<VerifyStoreGoodsAvailableForSaleScaleCodeVO> verifyStoreGoodsAvailableForSaleExportScaleCode(
            @RequestBody @Validated VerifyStoreGoodsAvailableForSaleScaleCodeDTO verifyStoreGoodsAvailableForSaleScaleCodeDTO) {
        return Result.success(storeGoodsService.verifyStoreGoodsAvailableForSaleExportScaleCode(verifyStoreGoodsAvailableForSaleScaleCodeDTO));
    }

    /**
     * 根据店铺ID、渠道ID查询可售商品列表 (传秤)
     * 这个是专属于传秤类型的接口，针对的商品是  计重和计数的商品
     *
     * @param queryStoreGoodsAvailableForSalePosListDTO 查询条件
     * @return 可售商品列表
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/weigh/list/page")
    public Result<PageRespVO<StoreGoodsExtendVO>> queryStoreGoodsAvailableForSaleWeighListPage(
            @RequestBody @Validated QueryStoreGoodsAvailableForSaleWeighListDTO queryStoreGoodsAvailableForSalePosListDTO) {
        return Result.success(storeGoodsService.queryStoreGoodsAvailableForSaleWeighListPage(queryStoreGoodsAvailableForSalePosListDTO));
    }

    /**
     * 根据店铺ID、渠道ID查询可售商品列表,以店铺商品为维度进行组装数据（即所有的方案的方案商品都会根据店铺商品进行分组组装）
     * 这个是基础的可售商品列表，不区分商品计价方式
     *
     * @param queryStoreGoodsAvailableForSaleListDTO 查询条件
     * @return 可售商品列表
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/time-section/list/page")
    public Result<PageRespVO<StoreGoodsPrintLabelExtendVO>> queryStoreGoodsAvailableForSaleTimeSectionListPage(
            @RequestBody @Validated QueryStoreGoodsAvailableForSaleListDTO queryStoreGoodsAvailableForSaleListDTO) {
        return Result.success(storeGoodsService.queryStoreGoodsAvailableForSaleTimeSectionListPage(queryStoreGoodsAvailableForSaleListDTO));
    }

    /**
     * 根据店铺ID、渠道ID、条码查询可售商品详情 (传秤)
     *
     * @param queryStoreGoodsAvailableForSaleWeighDetailDTO 查询条件
     * @return 可售商品详情
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/weigh/detail")
    public Result<StoreGoodsExtendVO> queryStoreGoodsAvailableForSaleWeighDetail(
            @RequestBody @Validated QueryStoreGoodsAvailableForSaleWeighDetailDTO queryStoreGoodsAvailableForSaleWeighDetailDTO) {
        return Result.success(storeGoodsService.queryStoreGoodsAvailableForSaleWeighDetail(queryStoreGoodsAvailableForSaleWeighDetailDTO));
    }

    /**
     * 查询商品组合类型的集合
     *
     * @return 商品组合类型的集合
     */
    @PostMapping("/api/v1/store-goods/combo-type/list")
    public Result<List<GoodsComboTypeVO>> queryStoreGoodsComboTypeList() {
        return Result.success(storeGoodsService.queryStoreGoodsComboTypeList());
    }

    /**
     * 生成单个店铺商品秤内码
     *
     * @param generateStoreGoodsPluCodeDTO 店铺商品秤内码生成参数
     */
    @PostMapping("/api/v1/store-goods/plu-code/generate")
    public Result<GoodsScaleCodeVO> generateStoreGoodsPluCode(
            @RequestBody @Validated GenerateStoreGoodsPluCodeDTO generateStoreGoodsPluCodeDTO) {
        return Result.success(storeGoodsService.generateStoreGoodsPluCode(generateStoreGoodsPluCodeDTO));
    }

    /**
     * 生成批量店铺商品秤内码
     *
     * @param generateBatchStoreGoodsPluCodeDTO 店铺商品秤内码生成批量参数
     */
    @PostMapping("/api/v1/store-goods/plu-code/generate/batch")
    public Result<List<GoodsScaleCodeVO>> generateStoreGoodsPluCodeBatch(
            @RequestBody @Validated GenerateBatchStoreGoodsPluCodeDTO generateBatchStoreGoodsPluCodeDTO) {
        return Result.success(storeGoodsService.generateStoreGoodsPluCodeBatch(generateBatchStoreGoodsPluCodeDTO));
    }

    /**
     * 查询POS渠道的可售商品列表
     *
     * @param queryDTO 查询条件
     * @return POS渠道的可售商品列表
     */
    @PostMapping("/api/v1/store-goods/pos-goods/available-for-sale/list/page")
    public Result<PageRespVO<StoreGoodsExtendVO>> queryStorePosGoodsAvailableForSaleListPage(
            @RequestBody @Validated QueryStorePosGoodsAvailableForSaleListDTO queryDTO) {
        return Result.success(storeGoodsService.queryStorePosGoodsAvailableForSaleListPage(queryDTO));
    }

    /**
     * 查询POS渠道的可售商品详情
     *
     * @param queryDTO 查询条件
     * @return POS渠道的可售商品详情
     */
    @PostMapping("/api/v1/store-goods/pos-goods/available-for-sale/detail")
    public Result<StoreGoodsExtendVO> queryStorePosGoodsAvailableForSaleDetail(
            @RequestBody @Validated QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO) {
        return Result.success(storeGoodsService.queryStorePosGoodsAvailableForSaleDetail(queryDTO));
    }
}

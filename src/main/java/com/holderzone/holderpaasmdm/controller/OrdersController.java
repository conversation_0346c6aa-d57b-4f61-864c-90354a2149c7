package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.OrderCreateDTO;
import com.holderzone.holderpaasmdm.model.dto.OrderStatusChangeDTO;
import com.holderzone.holderpaasmdm.model.dto.StoreDailySettleDTO;
import com.holderzone.holderpaasmdm.model.vo.OrderCreateVO;
import com.holderzone.holderpaasmdm.model.vo.StoreDailySettleVO;
import com.holderzone.holderpaasmdm.service.OrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * Description: 订单信息 Controller
 * Author: panqingkun
 * Date: 2024/12/18 17:50
 */
@RestController
@RequiredArgsConstructor
public class OrdersController {

    private final OrderService orderService;

    /**
     * 商家日结信息
     */
    @PostMapping("/api/v1/orders/store/daily/settle")
    public Result<List<StoreDailySettleVO>> storeDailySettle(@RequestHeader("company_id") Long companyId,
                                         @RequestBody @Validated StoreDailySettleDTO storeDailySettleDTO) {
        List<StoreDailySettleVO> storeDailySettleList = orderService.storeDailySettle(companyId, storeDailySettleDTO);
        return Result.success(storeDailySettleList);
    }

    /**
     * 创建订单
     */
    @PostMapping("/api/v1/orders/create")

    public Result<OrderCreateVO> createOrder(@RequestBody @Validated OrderCreateDTO orderCreateDTO) {
        OrderCreateVO orderCreateVO = orderService.createOrder(orderCreateDTO);
        return Result.success(orderCreateVO);
    }

    /**
     * 订单状态变更
     * @param orderStatusChangeDTO 请求参数
     * @return 变更结果
     */
    @PostMapping("/api/v1/orders/status/change")
    public Result<Boolean> changeOrderStatus(@RequestBody @Validated OrderStatusChangeDTO orderStatusChangeDTO, @RequestHeader("company_id") Long companyId) {
        Boolean result = orderService.changeOrderStatus(orderStatusChangeDTO, companyId);
        if (Boolean.TRUE.equals(result)) {
            return Result.success(result, "SUCCESS");
        } else {
            return Result.fail(500, "FAILED");
        }
    }
}

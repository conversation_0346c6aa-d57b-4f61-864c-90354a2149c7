package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.QueryCategoryPlatformPageDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryCategoryPlatformTreeDTO;
import com.holderzone.holderpaasmdm.model.vo.GoodsCategoryPlatformTreeVO;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.service.GoodsCategoryPlatformService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 平台类目controller
 *
 * <AUTHOR>
 * @date 2025/5/30 14:21
 **/
@RestController
@RequiredArgsConstructor
public class GoodsCategoryPlatformController {

    private final GoodsCategoryPlatformService goodsCategoryPlatformService;

    /**
     * 分页查询一级类目数据
     *
     * @param pageDTO 查询参数
     * @return 查询结果
     */
    @PostMapping("/api/v1/category-platform/list/page")
    public Result<PageRespVO<GoodsCategoryPlatformTreeVO>> queryGoodsCategoryPlatformPage(
            @RequestBody @Validated QueryCategoryPlatformPageDTO pageDTO) {
        return new Result<>(goodsCategoryPlatformService.queryGoodsCategoryPlatformPage(pageDTO));
    }

    /**
     * 查询平台类目树信息
     *
     * @param queryCategoryPlatformTreeDTO 查询参数
     * @return 查询结果
     */
    @PostMapping("/api/v1/category-platform/list/tree")
    public Result<List<GoodsCategoryPlatformTreeVO>> querySaleCategoryOnSaleTree(
            @RequestBody @Validated QueryCategoryPlatformTreeDTO queryCategoryPlatformTreeDTO) {
        return new Result<>(goodsCategoryPlatformService.queryGoodsCategoryPlatformTree(queryCategoryPlatformTreeDTO));
    }

    /**
     * 查询平台类目树信息
     *
     * @param queryCategoryPlatformTreeDTO 查询参数
     * @return 查询结果
     */
    @PostMapping("/api/v1/category-platform/details")
    public Result<GoodsCategoryPlatformTreeVO> queryCategoryDetails(
            @RequestBody @Validated QueryCategoryPlatformTreeDTO queryCategoryPlatformTreeDTO) {
        if (Objects.isNull(queryCategoryPlatformTreeDTO.getCategoryPlatformId())) {
            throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "平台类目id不能为空");
        }
        return new Result<>(goodsCategoryPlatformService.findTreeVoById(queryCategoryPlatformTreeDTO));
    }
}
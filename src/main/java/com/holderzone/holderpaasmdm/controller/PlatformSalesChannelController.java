package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.PlatformSalesChannelDTO;
import com.holderzone.holderpaasmdm.model.vo.PlatformSalesChannelVO;
import com.holderzone.holderpaasmdm.service.PlatformSalesChannelService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Description: 销售渠道 Controller
 * Author: 向超
 * Date: 2024/12/05 15:36
 */
@RestController
@RequiredArgsConstructor
public class PlatformSalesChannelController {
    private final PlatformSalesChannelService platformSalesChannelService;

    /**
     * 查询POS渠道的ID
     *
     * @return POS渠道的ID
     */
    @PostMapping("/api/v1/sale-channel/pos")
    public Result<Integer> querySaleChannelPOSId() {
        return Result.success(platformSalesChannelService.querySaleChannelPOSId());
    }

    /**
     * 根即渠道名称查询可用状态渠道信息
     */
    @PostMapping("/api/v1/sale-channel/list")
    public Result<List<PlatformSalesChannelVO>> querySaleChannelId(@RequestBody @Validated PlatformSalesChannelDTO platformSalesChannelDTO) {
        return Result.success(platformSalesChannelService.querySaleChannelId(platformSalesChannelDTO));
    }


}

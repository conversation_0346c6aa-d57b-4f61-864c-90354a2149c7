package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.QueryInboundListDTO;
import com.holderzone.holderpaasmdm.model.vo.InboundBaseVO;
import com.holderzone.holderpaasmdm.service.StockService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Description: 库存 Controller
 * Author: 向超
 * Date: 2025/01/16 15:31
 */
@RestController
@RequiredArgsConstructor
public class StockController {
    private final StockService stockService;

    /**
     * 查询入库单列表
     *
     * @param queryInboundListDTO 查询入库单列表入参
     * @return 入库单列表
     */
    @PostMapping("/api/v1/inbound/list")
    public Result<List<InboundBaseVO>> queryInboundsList(
            @RequestBody @Validated QueryInboundListDTO queryInboundListDTO) {
        return Result.success(stockService.queryInboundsList(queryInboundListDTO));
    }
}

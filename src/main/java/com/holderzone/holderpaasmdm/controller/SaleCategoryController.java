package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.*;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.model.vo.SaleCategoryTreeVO;
import com.holderzone.holderpaasmdm.service.SaleCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * Description: 销售分组 Controller
 * Author: 向超
 * Date: 2024/11/28 15:31
 */
@RestController
@RequiredArgsConstructor
public class SaleCategoryController {
    private final SaleCategoryService saleCategoryService;

    /**
     * 查询在售商品对应的销售分组树
     *
     * @param querySaleCategoryOnSaleTreeDTO 查询条件
     * @return 销售分组树
     */
    @PostMapping("/api/v1/sale-category/on-sale/tree")
    public Result<List<SaleCategoryTreeVO>> querySaleCategoryOnSaleTree(
            @RequestBody @Validated QuerySaleCategoryOnSaleTreeDTO querySaleCategoryOnSaleTreeDTO) {
        return Result.success(saleCategoryService.querySaleCategoryOnSaleTree(querySaleCategoryOnSaleTreeDTO));
    }

    /**
     * 查询所有的销售分组树
     *
     * @param querySaleCategoryTreeDTO 查询条件
     * @return 销售分组树
     */
    @PostMapping("/api/v1/sale-category/tree")
    public Result<List<SaleCategoryTreeVO>> querySaleCategoryTree(
            @RequestBody @Validated QuerySaleCategoryTreeDTO querySaleCategoryTreeDTO) {
        querySaleCategoryTreeDTO.setIsTree(true);
        return Result.success(saleCategoryService.querySaleCategoryTree(querySaleCategoryTreeDTO, true));
    }

    /**
     * 查询可售商品对应的销售分组树
     *
     * @param querySaleCategoryAvailableForSaleTreeDTO 查询条件
     * @return 销售分组树
     */
    @PostMapping("/api/v1/sale-category/available-for-sale/tree")
    public Result<List<SaleCategoryTreeVO>> querySaleCategoryAvailableForSaleTree(
            @RequestBody @Validated QuerySaleCategoryAvailableForSaleTreeDTO querySaleCategoryAvailableForSaleTreeDTO) {
        return Result.success(saleCategoryService.querySaleCategoryAvailableForSaleTree(querySaleCategoryAvailableForSaleTreeDTO));
    }

    /**
     * 查询可售商品对应的销售分组树 (传秤)
     *
     * @param querySaleCategoryAvailableForSaleWeighTreeDTO 查询条件
     * @return 销售分组树
     */
    @PostMapping("/api/v1/sale-category/available-for-sale/weigh/tree")
    public Result<List<SaleCategoryTreeVO>> querySaleCategoryAvailableForSaleWeighTree(
            @RequestBody @Validated QuerySaleCategoryAvailableForSaleWeighTreeDTO querySaleCategoryAvailableForSaleWeighTreeDTO) {
        return Result.success(saleCategoryService.querySaleCategoryAvailableForSaleWeighTree(querySaleCategoryAvailableForSaleWeighTreeDTO));
    }

    /**
     * 查询POS渠道的可售商品对应的销售分组树
     *
     * @param queryDTO 查询条件
     * @return 销售分组树
     */
    @PostMapping("/api/v1/sale-category/pos-goods/available-for-sale/tree")
    public Result<List<SaleCategoryTreeVO>> querySaleCategoryPosGoodsAvailableForSaleTree(
            @RequestBody @Validated QuerySaleCategoryPosGoodsAvailableForSaleTreeDTO queryDTO) {
        return Result.success(saleCategoryService.querySaleCategoryPosGoodsAvailableForSaleTree(queryDTO));
    }

    /**
     * 分页查询一级类目数据
     *
     * @param pageDTO 查询参数
     * @return 查询结果
     */
    @PostMapping("/api/v1/sale-category/list/page")
    public Result<PageRespVO<SaleCategoryTreeVO>> queryCategoryPage(@RequestBody @Validated QuerySaleCategoryPageDTO pageDTO) {
        return new Result<>(saleCategoryService.queryCategoryPage(pageDTO));
    }

    /**
     * 查询所有的销售分组树
     *
     * @param querySaleCategoryTreeDTO 查询条件
     * @return 销售分组树
     */
    @PostMapping("/api/v1/sale-category/level/tree")
    public Result<List<SaleCategoryTreeVO>> querySaleCategoryLevelTree(
            @RequestBody @Validated QuerySaleCategoryTreeDTO querySaleCategoryTreeDTO) {
        return Result.success(saleCategoryService.querySaleCategoryTree(querySaleCategoryTreeDTO, false));
    }

    /**
     * 查询销售分组详情数据
     *
     * @param querySaleCategoryTreeDTO 查询条件
     * @return 销售分组树
     */
    @PostMapping("/api/v1/sale-category/details")
    public Result<SaleCategoryTreeVO> querySaleCategoryDetails(
            @RequestBody @Validated QuerySaleCategoryTreeDTO querySaleCategoryTreeDTO) {
        if (Objects.isNull(querySaleCategoryTreeDTO.getSaleCategoryId())) {
            throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "销售分组id不能为空");
        }
        return Result.success(saleCategoryService.queryCategoryDetails(querySaleCategoryTreeDTO));
    }
}

package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreSalesProgramListDTO;
import com.holderzone.holderpaasmdm.model.vo.StoreSalesProgramExtendVO;
import com.holderzone.holderpaasmdm.service.StoreSalesProgramService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Description: 店铺销售方案 Controller
 * Author: 向超
 * Date: 2024/11/14 15:31
 */
@RestController
@RequiredArgsConstructor
public class StoreSalesProgramController {
    private final StoreSalesProgramService storeSalesProgramService;

    /**
     * 查询店铺销售方案列表
     *
     * @param queryStoreSalesProgramListDTO 查询条件
     * @return 店铺销售方案列表
     */
    @PostMapping("/api/v1/store-sales-program/list")
    public Result<List<StoreSalesProgramExtendVO>> queryStoreSalesProgramList(
            @RequestBody @Validated QueryStoreSalesProgramListDTO queryStoreSalesProgramListDTO) {
        return Result.success(storeSalesProgramService.queryStoreSalesProgramList(queryStoreSalesProgramListDTO));
    }
}

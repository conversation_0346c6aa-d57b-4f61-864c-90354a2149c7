package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundDetailsDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundListDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryOrderRefundExceptionNumberDTO;
import com.holderzone.holderpaasmdm.model.vo.OrderRefundExtendVO;
import com.holderzone.holderpaasmdm.model.vo.PageRespBaseVO;
import com.holderzone.holderpaasmdm.service.OrderRefundService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


/**
 * Description: 订单退款 Controller
 * Author: 向超
 * Date: 2025/01/14 15:31
 */
@RestController
@RequiredArgsConstructor
public class OrdersRefundController {
    private final OrderRefundService orderRefundService;

    /**
     * 查询订单退款列表
     *
     * @param queryOrderRefundListDTO 订单退款列表查询条件
     * @return 订单退款列表
     */
    @PostMapping("/api/v1/orders-refund/list")
    public Result<PageRespBaseVO<OrderRefundExtendVO>> queryOrderRefundList(
            @RequestBody @Validated QueryOrderRefundListDTO queryOrderRefundListDTO) {
        return Result.success(orderRefundService.queryOrderRefundList(queryOrderRefundListDTO));
    }

    /**
     * 查询订单异常退款单数量
     *
     * @param queryOrderRefundExceptionNumberDTO 查询订单异常退款单数量条件
     * @return 异常退款单数量
     */
    @PostMapping("/api/v1/orders-refund/exception/statistics")
    public Result<Integer> queryOrderRefundExceptionNumber(
            @RequestBody QueryOrderRefundExceptionNumberDTO queryOrderRefundExceptionNumberDTO) {
        return Result.success(orderRefundService.queryOrderRefundExceptionNumber(queryOrderRefundExceptionNumberDTO));
    }

    /**
     * 查询订单退款详情
     *
     * @param queryOrderRefundDetailsDTO 查询退款订单详情条件
     * @return 退款订单详情
     */
    @PostMapping("/api/v1/orders-refund/details")
    public Result<OrderRefundExtendVO> queryOrderRefundDetails(
            @RequestBody QueryOrderRefundDetailsDTO queryOrderRefundDetailsDTO) {
        return Result.success(orderRefundService.queryOrderRefundDetails(queryOrderRefundDetailsDTO));
    }
}

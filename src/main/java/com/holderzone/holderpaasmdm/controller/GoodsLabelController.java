package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.QueryCategoryPlatformPageDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryGoodsLabelDTO;
import com.holderzone.holderpaasmdm.model.vo.GoodsCategoryPlatformTreeVO;
import com.holderzone.holderpaasmdm.model.vo.GoodsLabelVO;
import com.holderzone.holderpaasmdm.model.vo.PageRespVO;
import com.holderzone.holderpaasmdm.service.GoodsLabelService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品标签
 *
 * <AUTHOR>
 * @date 2025/6/17 10:46
 **/
@RestController
@RequiredArgsConstructor
public class GoodsLabelController {

    private final GoodsLabelService goodsLabelService;

    /**
     * 查询所有商品标签数据
     *
     * @return 查询结果
     */
    @PostMapping("/api/v1/goods-label/list")
    public Result<List<GoodsLabelVO>> queryGoodsLabelAll() {
        return new Result<>(goodsLabelService.findAll());
    }

    /**
     * 查询标签详情数据
     *
     * @param queryGoodsLabelDTO 查询数据
     * @return 查询结果
     */
    @PostMapping("/api/v1/goods-label/details")
    public Result<GoodsLabelVO> queryGoodsLabelDetails(@RequestBody @Validated QueryGoodsLabelDTO queryGoodsLabelDTO) {
        return new Result<>(goodsLabelService.findById(queryGoodsLabelDTO.getGoodsLabelId()));
    }
}

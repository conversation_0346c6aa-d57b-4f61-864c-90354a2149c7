package com.holderzone.holderpaasmdm.controller;

import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.vo.SaleUnitMeasurementVO;
import com.holderzone.holderpaasmdm.service.SaleUnitMeasurementService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Description: 单位 Controller
 * Author: 向超
 * Date: 2025/02/06 15:31
 */
@RestController
@RequiredArgsConstructor
public class SaleUnitMeasurementController {
    private final SaleUnitMeasurementService saleUnitMeasurementService;

    /**
     * 查询销售计量单位列表
     *
     * @return 销售计量单位列表
     */
    @PostMapping("/api/v1/sale-unit-measurement/list")
    public Result<List<SaleUnitMeasurementVO>> querySaleUnitMeasurementList() {
        return Result.success(saleUnitMeasurementService.querySaleUnitMeasurementList());
    }
}

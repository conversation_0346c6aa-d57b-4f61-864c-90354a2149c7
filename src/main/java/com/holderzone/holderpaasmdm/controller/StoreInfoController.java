package com.holderzone.holderpaasmdm.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.holderzone.holderpaasmdm.common.exception.StoreBaseException;
import com.holderzone.holderpaasmdm.enumeraton.ResponseCode;
import com.holderzone.holderpaasmdm.model.Result;
import com.holderzone.holderpaasmdm.model.dto.QueryFreightTemplateDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreChannelBindDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStoreGoodsSkuInventoryDTO;
import com.holderzone.holderpaasmdm.model.dto.QueryStorePosGoodsAvailableForSaleDetailDTO;
import com.holderzone.holderpaasmdm.model.vo.*;
import com.holderzone.holderpaasmdm.service.StoreInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * Description: 门店信息控制类
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RestController
@RequiredArgsConstructor
public class StoreInfoController {

    private final StoreInfoService storeInfoService;

    /**
     * 第三方店铺查询绑定关系
     *
     * @param queryStoreChannelBindDTO 查询数据
     * @return 查询结果
     */
    @PostMapping("/api/v1/store-info/bind/find")
    public Result<StoreChannelBindVO> queryBindStore(
            @RequestBody @Validated QueryStoreChannelBindDTO queryStoreChannelBindDTO) {
        if (Objects.isNull(queryStoreChannelBindDTO.getStoreId())
                && Objects.isNull(queryStoreChannelBindDTO.getBindStoreId())) {
            throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "店铺商品ID或绑定第三方店铺ID至少传入一个值");
        }
        return Result.success(storeInfoService.queryBindStore(queryStoreChannelBindDTO));
    }

    /**
     * 查询商品详情（对外）
     *
     * @param queryDTO 查询条件
     * @return POS渠道的可售商品详情
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/detail")
    public Result<StoreGoodsExtendChannelVO> queryStoreGoodsAvailableForSaleDetail(
            @RequestHeader("company_id") Long companyId,
            @RequestBody @Validated QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO) {
        if (Objects.isNull(queryDTO.getStoreGoodsId())) {
            throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "店铺商品ID不能为空");
        }
        queryDTO.setCompanyId(companyId);
        return Result.success(storeInfoService.queryDetails(queryDTO));
    }

    /**
     * 查询商品详情（对外）
     *
     * @param queryDTO 查询条件
     * @return POS渠道的可售商品详情
     */
    @PostMapping("/api/v1/store-goods/available-for-sale/detail/batch")
    public Result<List<StoreGoodsExtendChannelVO>> queryStoreGoodsAvailableForSaleDetailBatch(
            @RequestHeader("company_id") Long companyId,
            @RequestBody @Validated QueryStorePosGoodsAvailableForSaleDetailDTO queryDTO) {
        if (CollUtil.isEmpty(queryDTO.getStoreGoodsIds())) {
            throw new StoreBaseException(ResponseCode.COMMON_INVALID_PARAMETER, "店铺商品ID数组不能为空");
        }
        queryDTO.setCompanyId(companyId);
        return Result.success(storeInfoService.queryDetailsBatch(queryDTO));
    }

    /**
     * 查询sku库存数据
     *
     * @param inventoryDTO 查询对象
     * @return 查询结果
     */
    @PostMapping("/api/v1/store-goods/inventory")
    public Result<List<StoreGoodsSkuInventoryVO>> querySkuInventory(
            @RequestHeader("company_id") Long companyId,
            @RequestBody @Validated QueryStoreGoodsSkuInventoryDTO inventoryDTO) {
        inventoryDTO.setCompanyId(companyId);
        return Result.success(storeInfoService.querySkuInventory(inventoryDTO));
    }

    /**
     * 运费模版id查询绑定的商品数量
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    @PostMapping("/api/v1/store-goods/freight-template/count")
    public Result<Integer> countByFreightTemplateId(@RequestBody @Validated QueryFreightTemplateDTO dto) {
        return Result.success(storeInfoService.countByFreightTemplateId(dto.getFreightTemplateId()));
    }
}

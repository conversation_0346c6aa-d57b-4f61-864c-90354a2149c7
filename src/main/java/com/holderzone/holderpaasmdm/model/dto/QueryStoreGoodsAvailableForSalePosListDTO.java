package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 查询门店商品可售列表（POS） DTO
 * Author: 向超
 * Date: 2024/12/03 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsAvailableForSalePosListDTO extends QueryBasePageDTO {
    /**
     * 店铺销售方案ID
     */
    @JsonProperty("store_sales_program_id")
    private Integer storeSalesProgramId;

    /**
     * 销售分组ID
     * 不传则查询全部，传则查询指定销售分组及其子分组的商品
     */
    @JsonProperty("sale_category_id")
    private Integer saleCategoryId;

    /**
     * 模糊查询关键字，商品条码、自编码、首字母
     */
    private String keywords;
}

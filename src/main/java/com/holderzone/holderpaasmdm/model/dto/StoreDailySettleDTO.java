package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.List;

/**
 * desc 门店日结信息查询接口
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
@Data
public class StoreDailySettleDTO {

    /**
     * 门店ID列表
     */
    @NotNull(message = "门店ID列表不能为空")
    @Size(min = 1, message = "门店ID列表不能为空")
    private List<Long> storeIdList;

    /**
     * 所属营业日开始日期
     * yyyy-MM-dd
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private String businessDayStart;

    /**
     * 所属营业日结束日期
     * yyyy-MM-dd
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private String businessDayEnd;

}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * desc 创建订单DTO
 *
 * <AUTHOR>
 * @date 2024/12/19
 * @since 1.8
 */
@Data
public class OrderCreateDTO {

    /**
     * 操作人ID 可传0
     */
    @JsonProperty("operater_id")
    private int operaterId;

    /**
     * 操作人姓名
     */
    @JsonProperty("operater_name")
    private String operaterName;

    /**
     * 订单来源 "1":门店收银
     */
    @JsonProperty("order_source")
    private String orderSource;

    /**
     * 零售门店id(接口获取)
     */
    @JsonProperty("store_id")
    private int storeId;

    /**
     * 零售门店名称(接口获取)
     */
    @JsonProperty("store_name")
    private String storeName;

    /**
     * 部门id(接口获取)
     */
    @JsonProperty("store_team_info_id")
    private int storeTeamInfoId;

    /**
     * 一体化平台id(接口获取)
     */
    @JsonProperty("holder_store_id")
    private int holderStoreId;

    /**
     * 企业id
     */
    @JsonProperty("enterprise_id")
    private int enterpriseId;

    /**
     * 订单应收
     */
    @JsonProperty("origin_price")
    private String originPrice;

    /**
     * 优惠金额
     */
    @JsonProperty("discount_price")
    private String discountPrice;

    /**
     * 实收金额
     */
    @JsonProperty("receive_price")
    private String receivePrice;

    /**
     * 售卖渠道id(接口获取)
     */
    @JsonProperty("channel_id")
    private int channelId;

    /**
     * 渠道名称(接口获取)
     */
    @JsonProperty("channel_name")
    private String channelName;

    /**
     * 设备编号
     */
    @JsonProperty("device_number")
    private String deviceNumber;

    /**
     * 支付信息,无组合支付场景仅传聚合支付参数
     */
    @JsonProperty("payments")
    private List<OrderPaymentVO> payments;

    /**
     * 商品信息  订单明细
     */
    @JsonProperty("items")
    private List<OrderItemVO> items;

    /**
     * 支付信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrderPaymentVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 支付方式id，传聚合支付：传-1
         */
        @JsonProperty("payment_method_id")
        private int paymentMethodId;

        /**
         * 支付方式传"聚合支付"
         */
        @JsonProperty("payment_method_name")
        private String paymentMethodName;

        /**
         * 支付方式类型枚举传3
         */
        @JsonProperty("payment_type")
        private int paymentType;

        /**
         * 手机支付条码
         */
        @JsonProperty("payment_auth_code")
        private String paymentAuthCode;

        /**
         * 终端号可传设备号
         */
        @JsonProperty("payment_terminal_id")
        private int paymentTerminalId;

        /**
         * 经纬度 经度
         */
        @JsonProperty("longitude")
        private String longitude;

        /**
         * 经纬度 纬度
         */
        @JsonProperty("latitude")
        private String latitude;

        /**
         * 支付金额比如100元传"100"
         */
        @JsonProperty("payment_amount")
        private String paymentAmount;
    }


    /**
     * 商品信息 订单明细
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrderItemVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 门店销售策略商品id
         */
        @JsonProperty("store_sales_program_goods_id")
        private int storeSalesProgramGoodsId;

        /**
         * 门店销售策略id
         */
        @JsonProperty("store_sales_program_id")
        private int storeSalesProgramId;

        /**
         * 商品id
         */
        @JsonProperty("goods_id")
        private int goodsId;

        /**
         * 商品编码
         */
        @JsonProperty("spu_code")
        private String spuCode;

        /**
         * 商品名称
         */
        @JsonProperty("goods_sale_name")
        private String goodsSaleName;

        /**
         * 条码
         */
        @JsonProperty("barcode")
        private String barcode;

        /**
         * 计价方式 1普通2计重3计数
         */
        @JsonProperty("valuation_method")
        private int valuationMethod;

        /**
         * 组合类型 1
         */
        @JsonProperty("combo_type")
        private int comboType;

        /**
         * 组合类型名称
         */
        @JsonProperty("combo_type_name")
        private String comboTypeName;

        /**
         * 成本 可传"0"
         */
        @JsonProperty("costs")
        private String costs;

        /**
         * 单位id
         */
        @JsonProperty("goods_unit_id")
        private int goodsUnitId;

        /**
         * 单位名称
         */
        @JsonProperty("goods_unit_name")
        private String goodsUnitName;

        /**
         * 商品原价
         */
        @JsonProperty("selling_price")
        private String sellingPrice;

        /**
         * 优惠金额 可传"0"
         */
        @JsonProperty("discount_total_price")
        private String discountTotalPrice;

        /**
         * 实收金额
         */
        @JsonProperty("actual_receive_price")
        private String actualReceivePrice;

        /**
         * 图片地址
         */
        @JsonProperty("picture_url")
        private String pictureUrl;

        /**
         * 购买数量
         */
        @JsonProperty("purchase_quantity")
        private String purchaseQuantity;

        /**
         * 销售分组id
         */
        @JsonProperty("category_id")
        private String categoryId;

        /**
         * 销售分组名称
         */
        @JsonProperty("category_name")
        private String categoryName;
    }

}
package com.holderzone.holderpaasmdm.model.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;
import com.holderzone.holderpaasmdm.model.po.BasePO;

import java.io.Serializable;

import static com.holderzone.holderpaasmdm.common.constant.ErrorMessageKeys.FIELD_NOT_NULL;
import static com.holderzone.holderpaasmdm.common.constant.ErrorMessageKeys.FIELD_RANGE_ERROR;

/**
 * Description: 分页DTO
 * Author: 向超
 * Date: 2024/11/13 15:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageDTO implements Serializable {

    /**
     * 当前页码
     */
    @NotNull(message = FIELD_NOT_NULL)
    @Range(min = 1, max = Integer.MAX_VALUE, message = FIELD_RANGE_ERROR)
    private Integer page;

    /**
     * 每页条数，最大一页500条数据
     */
    @NotNull(message = FIELD_NOT_NULL)
    @Range(min = 1, max = 10000, message = FIELD_RANGE_ERROR)
    private Integer limit;

    /**
     * 转换为mybatis-plus {@link Page}对象
     *
     * @param <T> 需要查询的实体类型
     * @return mybatis-plus Page对象
     */
    public <T extends BasePO> Page<T> toMpPage() {
        return new Page<>(this.page, this.limit);
    }
}

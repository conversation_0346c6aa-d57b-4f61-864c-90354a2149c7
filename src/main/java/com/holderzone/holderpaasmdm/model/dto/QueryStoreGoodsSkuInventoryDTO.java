package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询sku库存对象
 *
 * <AUTHOR>
 * @date 2025/6/17 11:16
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryStoreGoodsSkuInventoryDTO extends QueryBaseDTO {

    /**
     * 店铺商品ID集合
     */
    @JsonProperty("store_goods_ids")
    @NotNull(message = "店铺商品ID数组不能为空")
    @Size(min = 1, message = "店铺商品ID数组不能为空")
    private List<Integer> storeGoodsIds;

    /**
     * 公司id
     */
    @JsonIgnore
    private Long companyId;
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询平台对象
 *
 * <AUTHOR>
 * @date 2025/5/30 14:26
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryCategoryPlatformTreeDTO extends QueryBaseChannelDTO {

    /**
     * 是否查询商品数量
     */
    @JsonProperty("is_query_goods_count")
    private Boolean isQueryGoodsCount;

    /**
     * 是否查询图片数据
     */
    @JsonProperty("is_query_picture")
    private Boolean isQueryPicture;

    /**
     * 是否树形结构,默认true
     */
    @JsonProperty("is_tree")
    private Boolean isTree;

    /**
     * 平台类目id
     */
    @JsonProperty("category_platform_id")
    private Integer categoryPlatformId;
}

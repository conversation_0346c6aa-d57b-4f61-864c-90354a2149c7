package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description: 查询可售商品对应的销售分组树 DTO
 * Author: 向超
 * Date: 2024/12/04 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuerySaleCategoryAvailableForSaleTreeDTO extends QueryBaseDTO {
    /**
     * 店铺销售方案ID,可以为空，为空则默认查询POS渠道的默认方案
     */
    @JsonProperty("store_sales_program_id")
    private Integer storeSalesProgramId;
}

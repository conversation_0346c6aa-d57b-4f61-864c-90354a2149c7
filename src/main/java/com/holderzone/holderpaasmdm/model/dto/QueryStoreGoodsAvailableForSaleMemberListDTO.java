package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Description: 查询门店商品可售列表 （会员）DTO
 * Author: 向超
 * Date: 2024/12/02 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsAvailableForSaleMemberListDTO extends PageDTO {
    /**
     * 适用范围（按渠道下的门店） (店铺销售方案)
     */
    @NotNull(message = "店铺ID不能为空")
    @JsonProperty("store_ids")
    @Size(min = 1, message = "店铺ID不能为空")
    private List<Integer> storeIdList;

    /**
     * 适用范围（按渠道） (店铺销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private List<Integer> channelIdList;

    /**
     * 销售分组ID列表
     */
    private List<Integer> category;

    /**
     * 组合类型（单品/套餐）
     * 1：单品，2：套餐
     */
    @JsonProperty("combo_type")
    private List<Integer> comboType;

    /**
     * 关键字：商品名称\SPU编码
     */
    private String keywords;

    /**
     * Spu编码列表
     */
    @JsonProperty("spu_codes")
    private List<String> spuCodeList;
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description: 查询门店在售商品的列表(根据编码过滤) DTO
 * Author: 向超
 * Date: 2025/02/10 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsOnSaleFilterListDTO extends QueryBaseDTO {
    /**
     * 模糊查询秤内自编码、商品自编码、商品条码
     */
    @NotBlank(message = "编码不能为空")
    private String code;

    /**
     * 秤的品牌
     */
    @JsonProperty("scale_type")
    private Integer scaleType;
}

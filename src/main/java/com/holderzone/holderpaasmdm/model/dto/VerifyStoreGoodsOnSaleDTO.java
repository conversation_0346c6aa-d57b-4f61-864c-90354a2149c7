package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: 快速结账 - 校验商品状态 DTO
 * Author: 向超
 * Date: 2024/11/29 15:36
 */
@Data
public class VerifyStoreGoodsOnSaleDTO {

    /**
     * 适用范围（按渠道下的门店） (门店销售方案)
     */
    @NotNull(message = "店铺ID不能为空")
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 商品列表
     */
    @JsonProperty("goods_list")
    @NotNull(message = "商品列表不能为空")
    @Size(min = 1, message = "商品列表不能为空")
    private List<GoodsInfo> goodsList;

    /**
     * 商品信息
     */
    @Data
    public static class GoodsInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 门店销售方案商品ID
         */
        @JsonProperty("store_sales_program_goods_id")
        @NotNull(message = "门店销售方案商品ID不能为空")
        private Integer storeSalesProgramGoodsId;

        /**
         * 门店销售方案商品规格关系
         */
        @JsonProperty("goods_spec_relation_list")
        private List<GoodsSpecRelation> goodsSpecRelationList;

        /**
         * 商品SKU编码
         */
        @JsonProperty("sku_code")
        @NotNull(message = "商品SKU编码")
        private String skuCode;

        /**
         * 商品ID
         */
        @JsonProperty("goods_id")
        @NotNull(message = "商品ID不能为空")
        private Integer goodsId;

        /**
         * 商品销售名称
         */
        @JsonProperty("goods_sale_name")
        @NotBlank(message = "商品名称不能为空")
        private String goodsSaleName;

        /**
         * 商品销售价格
         */
        @JsonProperty("selling_price")
        @NotBlank(message = "销售价格不能为空")
        private BigDecimal sellingPrice;

        /**
         * 商品数量
         */
        @JsonProperty("quantity")
        @NotBlank(message = "数量不能为空")
        private String quantity;

        /**
         * 商品单位名称
         */
        @JsonProperty("goods_unit_name")
        @NotBlank(message = "商品单位名称不能为空")
        private String goodsUnitName;

        /**
         * 商品条码
         */
        @JsonProperty("barcode")
        private String barcode;
    }

    /**
     * 商品规格关系
     */
    @Data
    public static class GoodsSpecRelation implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 商品规格ID
         */
        @JsonProperty("goods_spec_id")
        private Integer goodsSpecId;

        /**
         * 商品规格名称
         */
        @JsonProperty("goods_spec_name")
        private String goodsSpecName;

        /**
         * 商品规格排序
         */
        @JsonProperty("goods_spec_name_sort")
        private Integer goodsSpecSort;

        /**
         * 商品规格值ID
         */
        @JsonProperty("goods_spec_detail_id")
        private Integer goodsSpecDetailId;

        /**
         * 商品规格值名称
         */
        @JsonProperty("goods_spec_detail_name")
        private String goodsSpecDetailName;

        /**
         * 商品规格值排序
         */
        @JsonProperty("goods_spec_detail_sort")
        private Integer goodsSpecDetailSort;
    }
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 查询商品标签对象
 *
 * <AUTHOR>
 * @date 2025/6/18 10:01
 **/
@Data
public class QueryGoodsLabelDTO {

    /**
     * 标签id
     */
    @NotNull(message = "标签id不能为空")
    @JsonProperty("goods_label_id")
    private Integer goodsLabelId;
}

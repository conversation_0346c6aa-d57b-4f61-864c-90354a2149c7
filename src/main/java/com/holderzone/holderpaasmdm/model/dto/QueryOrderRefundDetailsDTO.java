package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.OrderRefundExpand;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Description: 订单退款详情DTO
 * Author: 向超
 * Date: 2025/01/15 15:33
 */
@Data
public class QueryOrderRefundDetailsDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 退款单号
     */
    @JsonProperty("refund_number")
    private String refundNumber;

    /**
     * 退款信息扩展查询参数，传哪个就查哪个
     * See: {@link OrderRefundExpand}
     */
    @JsonProperty("refund_expand_list")
    private List<OrderRefundExpand> refundExpandList;
}



























package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * Description: 生成单个店铺商品秤内码DTO
 * Author: 向超
 * Date: 2024/12/12 15:36
 */
@Data
public class GenerateStoreGoodsPluCodeDTO {
    /**
     * 店铺商品ID
     */
    @NotNull(message = "店铺商品ID不能为空")
    @JsonProperty("store_goods_id")
    private Integer storeGoodsId;

    /**
     * 秤内码
     */
    @NotNull(message = "秤内码不能为空")
    @JsonProperty("plu_code")
    private Integer pluCode;

    /**
     * 秤的品牌
     */
    @NotNull(message = "秤的品牌不能为空")
    @JsonProperty("scale_type")
    private Integer scaleType;
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Description: 订单退款列表DTO
 * Author: 向超
 * Date: 2025/01/14 15:33
 */
@Data
public class QueryInboundListDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 退款单列表
     */
    @JsonProperty("refund_number_list")
    @NotNull
    @Size(min = 1, max = 200)
    private List<String> refundNumberList;
}



























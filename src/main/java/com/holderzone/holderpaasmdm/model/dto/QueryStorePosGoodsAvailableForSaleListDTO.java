package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.GoodsValuationMethod;
import com.holderzone.holderpaasmdm.enumeraton.StoreSource;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * Description: 查询POS渠道的可售商品列表DTO
 * Author: 向超
 * Date: 2025/02/06 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStorePosGoodsAvailableForSaleListDTO extends PageDTO {
    /**
     * 适用范围（按渠道下的门店） (门店销售方案)
     */
    @NotNull(message = "店铺ID不能为空")
    @JsonAlias({"store_id", "store_team_info_id"})
    private Integer storeId;

    /**
     * 适用范围（按渠道） (渠道销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonAlias({"channel_id"})
    private Integer channelId;

    /**
     * 关键字,商品名称（商品名称和销售名称）\商品条码\SPU编码\首字母
     */
    private String keywords;

    /**
     * 商品来源
     * See {@link StoreSource}
     */
    @JsonProperty("store_source")
    private StoreSource storeSource;

    /**
     * 商品计价方式
     * See {@link GoodsValuationMethod}
     */
    @JsonProperty("goods_valuation_method_list")
    private List<GoodsValuationMethod> goodsValuationMethodList;

    /**
     * 销售分组ID
     * 不传则查询全部，传则查询指定销售分组及其子分组的商品
     */
    @JsonProperty("sale_category_id")
    private Integer saleCategoryId;

    /**
     * 品牌ID列表
     */
    @JsonProperty("brand_id_list")
    private List<Integer> brandIdList;

    /**
     * 标签ID列表
     */
    @JsonProperty("cover_id_list")
    private List<Integer> coverIdList;

    /**
     * 商品规格：1：单规格 2：多规格
     */
    @JsonProperty("goods_spec")
    private Integer goodsSpec;
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 运费模版查询数据
 *
 * <AUTHOR>
 * @date 2025/6/18 9:47
 **/
@Data
public class QueryFreightTemplateDTO {

    /**
     * 运费模版id
     */
    @NotNull(message = "运费模版id不能为空")
    @JsonProperty("freight_template_id")
    private Long freightTemplateId;
}

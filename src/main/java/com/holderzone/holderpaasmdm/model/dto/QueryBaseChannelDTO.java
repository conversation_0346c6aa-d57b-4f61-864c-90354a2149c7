package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 渠道基础数据表
 *
 * <AUTHOR>
 * @date 2025/5/30 14:25
 **/
@Data
public class QueryBaseChannelDTO {

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private Integer channelId;
}

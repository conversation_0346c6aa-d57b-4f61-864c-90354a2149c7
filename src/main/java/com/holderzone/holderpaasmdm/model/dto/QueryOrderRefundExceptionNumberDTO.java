package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.RefundType;
import com.holderzone.holderpaasmdm.enumeraton.TimeShortcutSearchType;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description: 订单异常退款相应的数量查询DTO
 * Author: 向超
 * Date: 2025/01/15 15:33
 */
@Data
public class QueryOrderRefundExceptionNumberDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 渠道id
     */
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 日期快捷筛选，不传就是查全部
     * See: {@link TimeShortcutSearchType}
     */
    @JsonProperty("time_search")
    private TimeShortcutSearchType timeSearch;

    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 模糊查询关键字
     */
    private String keywords;

    /**
     * 支付方式，不传就是查全部
     */
    @JsonProperty("refund_method_list")
    private List<Integer> refundMethodList;

    /**
     * 退款类型，不传就是查全部
     * See: {@link RefundType}
     */
    @JsonProperty("refund_type_list")
    private List<RefundType> refundTypeList;

    /**
     * 设备号
     */
    @JsonProperty("device_number")
    private String deviceNumber;
}



























package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description: 查询门店在售商品的详情 DTO
 * Author: 向超
 * Date: 2024/11/27 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsOnSaleDetailDTO extends QueryBaseDTO {
    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 店铺商品ID
     */
    @JsonProperty("goods_id")
    private Integer storeGoodsId;

    /**
     * 门店商品自编码
     */
    @JsonProperty("goods_custom_code")
    private String goodsCustomCode;

    /**
     * 秤内自编码
     */
    @JsonProperty("scale_custom_code")
    private String scaleCustomCode;

    /**
     * 秤的品牌
     */
    @JsonProperty("scale_type")
    private Integer scaleType;
}

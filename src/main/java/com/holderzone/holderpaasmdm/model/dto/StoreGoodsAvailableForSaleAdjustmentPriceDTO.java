package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * Description: 店铺可售商品调价DTO
 * Author: 向超
 * Date: 2024/12/17 15:36
 */
@Data
public class StoreGoodsAvailableForSaleAdjustmentPriceDTO {
    /**
     * 门店ID
     */
    @NotNull(message = "门店ID不能为空")
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 门店名称
     */
    @NotBlank(message = "门店名称不能为空")
    @JsonProperty("store_name")
    private String storeName;
    /**
     * 渠道ID
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 渠道名称
     */
    @NotBlank(message = "渠道名称不能为空")
    @JsonProperty("channel_name")
    private String channelName;

    /**
     * 调价人ID
     */
    @NotNull(message = "调价人ID不能为空")
    @JsonProperty("user_id")
    private Integer userId;

    /**
     * 调价人名称
     */
    @NotBlank(message = "调价人名称不能为空")
    @JsonProperty("user_name")
    private String username;

    /**
     * 调价人账号
     */
    @NotBlank(message = "调价人账号不能为空")
    @JsonProperty("account")
    private String account;

    /**
     * 店铺商品ID
     */
    @NotNull(message = "店铺商品ID不能为空")
    @JsonProperty("store_goods_id")
    private Integer storeGoodsId;

    /**
     * 店铺商品名称
     */
    @NotBlank(message = "店铺商品名称不能为空")
    @JsonProperty("store_goods_name")
    private String storeGoodsName;

    /**
     * 店铺SPU编码
     */
    @NotBlank(message = "店铺SPU编码不能为空")
    @JsonProperty("spu_code")
    private String spuCode;

    /**
     * 店铺商品UUID
     */
    @NotBlank(message = "店铺商品UUID编码不能为空")
    @JsonProperty("goods_uuid")
    private String goodsUuid;

    /**
     * 门店商品建议售价
     */
    @JsonProperty("selling_price")
    private BigDecimal sellingPrice;

    /**
     * 1 单规格 2 多规格
     */
    @JsonProperty("goods_spec")
    private Integer goodsSpec;

    /**
     * 店铺商品的销售分组ID集合
     */
    @NotNull(message = "店铺商品的销售分组ID集合不能为空")
    @Size(min = 1, message = "店铺商品的销售分组ID集合不能为空")
    @JsonProperty("sale_category_id_list")
    private List<Integer> saleCategoryIdList;

    /**
     * 店铺商品的销售分组信息
     */
    @NotNull(message = "店铺商品的销售分组信息不能为空")
    @Size(min = 1, message = "店铺商品的销售分组信息不能为空")
    @JsonProperty("sale_category_list")
    private List<SaleCategoryDTO> saleCategoryList;

    /**
     * 店铺商品的调价明细
     */
    @NotNull(message = "店铺商品的调价明细不能为空")
    @Size(min = 1, message = "店铺商品的调价明细不能为空")
    @JsonProperty("adjustment_price_list")
    private List<TimeSectionAdjustmentDTO> timeSectionAdjustmentList;

    /**
     * 店铺商品的销售分组信息
     */
    @Data
    public static class SaleCategoryDTO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        @NotNull(message = "销售分组ID不能为空")
        private Integer id;
        /**
         * 销售分组名称
         */
        @NotBlank(message = "销售分组名称不能为空")
        private String name;
        /**
         * 父级ID
         */
        @JsonProperty("parent_id")
        @NotNull(message = "父级ID不能为空")
        private Integer parentId;
    }

    /**
     * 店铺商品的调价明细（时段）
     */
    @Data
    public static class TimeSectionAdjustmentDTO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 店铺商品封面ID
         */
        @JsonProperty("cover")
        private Integer cover;

        /**
         * 店铺商品单位ID
         */
        @JsonProperty("goods_unit")
        private Integer goodsUnit;

        /**
         * 店铺商品单位名称
         */
        @JsonProperty("goods_unit_name")
        private String goodsUnitName;

        /**
         * 门店商品成本
         */
        @JsonProperty("costs")
        private BigDecimal costs;

        /**
         * 店铺商品条码
         */
        @JsonProperty("barcode")
        private String barcode;


        /**
         * sku编码
         */
        @NotNull(message = "sku编码不能为空")
        @JsonProperty("sku_code")
        private String skuCode;

        /**
         * skuID
         */
        @NotNull(message = "skuID不能为空")
        @JsonProperty("goods_package_sku_id")
        private Integer goodsPackageSkuId;

        /**
         * 店铺商品销售方案商品对应的规格明细
         */
        @JsonProperty("store_goods_spec_relation_list")
        List<StoreGoodsSpecRelationDTO> storeGoodsSpecRelationList;

        /**
         * 销售时段数组
         */
        @JsonProperty("section_list")
        List<SectionDTO> sectionList;
    }

    /**
     * 店铺商品的规格明细
     */
    @Data
    public static class SectionDTO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 商品在该时段销售策略中的售卖名称
         */
        @NotBlank(message = "商品在该时段销售策略中的售卖名称不能为空")
        @JsonProperty("goods_sale_name")
        private String goodsSaleName;

        /**
         * 销售方案名称
         */
        @NotBlank(message = "销售方案名称不能为空")
        @JsonProperty("store_sales_program_name")
        private String storeSalesProgramName;

        /**
         * 销售方案ID
         */
        @NotNull(message = "销售方案ID不能为空")
        @JsonProperty("store_sales_program_id")
        private Integer storeSalesProgramId;

        /**
         * 销售方案商品ID
         */
        @NotNull(message = "销售方案商品ID不能为空")
        @JsonProperty("store_sales_program_goods_id")
        private Integer storeSalesProgramGoodsId;

        /**
         * 该时段销售策略原售价
         */
        @NotNull(message = "该时段销售策略原售价不能为空")
        @JsonProperty("sales_program_price")
        private BigDecimal salesProgramPrice;

        /**
         * 调价后售价
         */
        @NotNull(message = "调价后售价不能为空")
        @JsonProperty("adjustment_price")
        private BigDecimal adjustmentPrice;

        /**
         * 销售方案商品价格ID
         */
        @NotNull(message = "销售方案商品价格ID不能为空")
        @JsonProperty("store_sales_program_goods_price_id")
        private Integer storeSalesProgramGoodsPriceId;

        /**
         * 是否为默认销售策略(全时段), true: 是, false: 否
         */
        @NotNull(message = "是否为默认销售策略(全时段)不能为空")
        @JsonProperty("sales_program_is_default")
        private Boolean salesProgramIsDefault;

        /**
         * 销售策略开始时间, 例: 00:00
         */
        @NotNull(message = "销售策略开始时间不能为空")
        @JsonProperty("sales_program_start_time")
        private String salesProgramStartTime;

        /**
         * 销售策略结束时间, 例: 04:00
         */
        @NotNull(message = "销售策略结束时间不能为空")
        @JsonProperty("sales_program_end_time")
        private String salesProgramEndTime;
    }

    /**
     * 店铺商品的规格明细
     */
    @Data
    public static class StoreGoodsSpecRelationDTO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 商品规格ID
         */
        @JsonProperty("goods_spec_id")
        private Integer goodsSpecId;

        /**
         * 商品规格排序
         */
        @JsonProperty("goods_spec_sort")
        private Integer goodsSpecSort;

        /**
         * 商品规格名称
         */
        @JsonProperty("goods_spec_name")
        private String goodsSpecName;

        /**
         * 商品规格值ID
         */
        @JsonProperty("goods_spec_detail_id")
        private Integer goodsSpecDetailId;

        /**
         * 商品规格值排序
         */
        @JsonProperty("goods_spec_detail_sort")
        private Integer goodsSpecDetailSort;

        /**
         * 商品规格值名称
         */
        @JsonProperty("goods_spec_detail_name")
        private String goodsSpecDetailName;
    }
}

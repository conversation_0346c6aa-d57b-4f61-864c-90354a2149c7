package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询渠道绑定对象
 *
 * <AUTHOR>
 * @date 2025/6/6 17:59
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryStoreChannelBindDTO extends QueryBaseChannelDTO {

    /**
     * 店铺ID
     */
    @JsonProperty("store_id")
    private Long storeId;

    /**
     * 绑定第三方店铺ID
     */
    @JsonProperty("bind_store_id")
    private Long bindStoreId;

    /**
     * 绑定类型 1. 私域商城 业务枚举 StoreChannelBindEnum
     */
    @NotNull(message = "类型不能为空")
    @JsonProperty("type")
    private Integer bindType;
}

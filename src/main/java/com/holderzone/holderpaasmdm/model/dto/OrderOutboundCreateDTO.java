package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * desc 创建订单DTO
 *
 * <AUTHOR>
 * @date 2024/12/19
 * @since 1.8
 */
@Data
public class OrderOutboundCreateDTO {

    /**
     * 商品集合
     */
    @JsonProperty("goods_list")
    private List<Integer> goodsList;

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 渠道id
     */
    @JsonProperty("channel_id")
    private int channelId;

    /**
     * 销售单单号
     */
    @JsonProperty("related_code")
    private String relatedCode;


    @JsonProperty("related_id")
    private Integer relatedId;

    /**
     * 客户名称
     */
    @JsonProperty("customer_name")
    private String customerName;

    /**
     * 创建人名称
     */
    @JsonProperty("creator_name")
    private String creatorName;

    /**
     * 创建人id
     */
    @JsonProperty("creator_id")
    private Integer creatorId;

    @JsonProperty("document_creation_time")
    private String documentCreationTime;

    @JsonProperty("goods_detail")
    private List<GoodsInfoDTO> goodsDetail;

    /**
     * 商品信息 订单明细
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GoodsInfoDTO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 商品id
         */
        @JsonProperty("id")
        private Integer id;

        /**
         * 销售数量
         */
        @JsonProperty("quantity")
        private Integer quantity;

        /**
         * 销售金额
         */
        @JsonProperty("amount")
        private BigDecimal amount;

        @JsonProperty("spu_code")
        private String spuCode;

        @JsonProperty("barcode")
        private String barcode;

        @JsonProperty("goods_name")
        private String goodsName;

        @JsonProperty("pinyin_code")
        private String pinyinCode;

        @JsonProperty("goods_unit")
        private Integer goodsUnit;

        @JsonProperty("goods_unit_name")
        private String goodsUnitName;

        @JsonProperty("costs")
        private BigDecimal costs;
    }
}



























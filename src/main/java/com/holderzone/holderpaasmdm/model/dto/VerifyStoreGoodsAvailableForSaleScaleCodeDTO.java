package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpand;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * Description: 传秤校验商品状态 DTO
 * Author: 向超
 * Date: 2024/12/13 15:36
 */
@Data
public class VerifyStoreGoodsAvailableForSaleScaleCodeDTO {
    /**
     * 店铺商品ID集合
     */
    @NotNull(message = "店铺商品ID列表不能为空")
    @Size(min = 1, message = "店铺商品ID列表不能为空")
    @JsonProperty("store_goods_id_list")
    private List<Integer> storeGoodsIdList;

    /**
     * 店铺ID
     */
    @NotNull(message = "店铺ID不能为空")
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 秤的品牌
     */
    @NotNull(message = "秤的品牌不能为空")
    @JsonProperty("scale_type")
    private Integer scaleType;

    /**
     * 需要查询哪些扩展信息
     * 销售分组、品牌、标签、单位、封面等
     * see {@link StoreGoodsExpand}
     */
    @JsonProperty("store_goods_expand_list")
    private List<StoreGoodsExpand> storeGoodsExpandList;
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * desc 门店信息查询参数
 * <AUTHOR>
 * @date 2024/12/23
 */
@Data
public class StoreInfoDetailsInfoVO {

    /**
     * 厂商
     */
    @NotNull(message = "厂商不能为空")
    @JsonProperty("manufacturer")
    private String manufacturer;

    /**
     * 设备码
     */
    @NotNull(message = "设备码不能为空")
    @JsonProperty("device_number")
    private String deviceNumber;

}

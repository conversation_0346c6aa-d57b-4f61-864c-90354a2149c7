package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.OrderRefundExpand;
import com.holderzone.holderpaasmdm.enumeraton.RefundStatus;
import com.holderzone.holderpaasmdm.enumeraton.RefundType;
import com.holderzone.holderpaasmdm.enumeraton.TimeShortcutSearchType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description: 订单退款列表DTO
 * Author: 向超
 * Date: 2025/01/14 15:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryOrderRefundListDTO extends PageDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 渠道id
     */
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 日期快捷筛选，不传就是查全部
     * See: {@link TimeShortcutSearchType}
     */
    @JsonProperty("time_search")
    private TimeShortcutSearchType timeSearch;

    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 模糊查询关键字
     */
    private String keywords;

    /**
     * 退款类型，不传就是查全部
     * See: {@link RefundType}
     */
    @JsonProperty("refund_type_list")
    private List<RefundType> refundTypeList;

    /**
     * 退款状态，不传就是查全部
     * See: {@link RefundStatus}
     */
    @JsonProperty("refund_status")
    private RefundStatus refundStatus;

    /**
     * 支付方式，不传就是查全部
     */
    @JsonProperty("refund_method_list")
    private List<Integer> refundMethodList;

    /**
     * 设备号
     */
    @JsonProperty("device_number")
    private String deviceNumber;

    /**
     * 退款信息扩展查询参数，传哪个就查哪个
     * See: {@link OrderRefundExpand}
     */
    @JsonProperty("refund_expand_list")
    private List<OrderRefundExpand> refundExpandList;

    /**
     * 订单来源列表，不传就是查全部
     */
    @JsonProperty("order_source_list")
    private List<String> orderSourceList;
}



























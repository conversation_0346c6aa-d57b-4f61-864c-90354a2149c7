package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpand;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * Description: 查询门店可售商品商品（传秤） DTO
 * Author: 向超
 * Date: 2024/12/14 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsAvailableForSaleWeighDetailDTO extends QueryBaseDTO {

    /**
     * 商品条码
     */
    @NotBlank(message = "商品条码不能为空")
    private String barcode;

    /**
     * 秤的品牌
     */
    @NotNull(message = "秤的品牌不能为空")
    @JsonProperty("scale_type")
    private Integer scaleType;

    /**
     * 需要查询哪些扩展信息
     * 销售分组、品牌、标签、单位、封面等
     * see {@link StoreGoodsExpand}
     */
    @JsonProperty("store_goods_expand_list")
    private List<StoreGoodsExpand> storeGoodsExpandList;
}

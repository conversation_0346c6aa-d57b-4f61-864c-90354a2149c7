package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpand;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * Description: 查询门店商品可售列表（传秤） DTO
 * Author: 向超
 * Date: 2024/12/09 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsAvailableForSaleListDTO extends QueryBasePageDTO {
    /**
     * 店铺策略单商品ID集合
     * 不传则查询全部
     */
    @JsonProperty("store_sales_program_goods_id_list")
    private List<Integer> storeSalesProgramGoodsIdList;

    /**
     * 销售分组ID集合
     * 不传则查询全部，传则查询指定销售分组(不包含其子分组)的商品
     */
    @JsonProperty("category_ids")
    private List<Integer> saleCategoryIdList;

    /**
     * 模糊查询关键字：商品条码、商品名称、商品售卖名称、首字母
     */
    private String keywords;

    /**
     * 秤的品牌
     */
    private Integer scaleType;

    /**
     * 销售方案ID
     * 不传则查询全部
     */
    @JsonProperty("sales_program_id_list")
    private List<Integer> salesProgramIdList;

    /**
     * 需要查询哪些扩展信息
     * 销售分组、品牌、标签、单位、封面等
     * see {@link StoreGoodsExpand}
     */
    @JsonProperty("store_goods_expand_list")
    private List<StoreGoodsExpand> storeGoodsExpandList;
}

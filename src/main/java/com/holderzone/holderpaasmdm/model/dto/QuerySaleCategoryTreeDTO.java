package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description: 查询所有销售分组树 DTO
 * Author: 向超
 * Date: 2024/12/03 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuerySaleCategoryTreeDTO extends QueryBaseDTO {

    /**
     * 是否树形结构
     */
    @JsonProperty("is_tree")
    private Boolean isTree;

    /**
     * 是否查询图片数据
     */
    @JsonProperty("is_query_picture")
    private Boolean isQueryPicture;

    /**
     * 销售分组id
     */
    @JsonProperty("sale_category_id")
    private Integer saleCategoryId;
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.TimeShortcutSearchType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * Description: 查询调价记录列表 DTO
 * Author: 向超
 * Date: 2024/12/17 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsAvailableForSalePriceAdjustmentListDTO extends QueryBasePageDTO {
    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 模糊搜索
     */
    @JsonProperty("keywords")
    private String keywords;

    /**
     * 时间快捷搜索类型
     */
    @JsonProperty("time_search")
    private TimeShortcutSearchType timeSearch;
}

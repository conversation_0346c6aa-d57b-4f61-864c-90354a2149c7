package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * desc 销售渠道查询
 *
 * <AUTHOR>
 * @date 2024/12/24
 * @since 1.8
 */
@Data
public class PlatformSalesChannelDTO {

    /**
     * 销售渠道名称
     */
    @JsonProperty("channel_name")
    @NotNull(message = "销售渠道名称不能为空")
    private String channelName;

}

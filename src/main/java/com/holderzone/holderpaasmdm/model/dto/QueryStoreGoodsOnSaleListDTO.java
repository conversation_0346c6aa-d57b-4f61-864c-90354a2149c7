package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Description: 查询门店商品在售列表 DTO
 * Author: 向超
 * Date: 2024/11/27 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryStoreGoodsOnSaleListDTO extends QueryBasePageDTO {
    /**
     * 关键字,商品名称\商品条码\自编码\首字母
     */
    private String keywords;

    /**
     * 分组ID
     */
    @JsonProperty("category_id")
    private List<Integer> categoryIdList;

    /**
     * Spu编码列表
     */
    @JsonProperty("spu_codes")
    private List<String> spuCodeList;
}

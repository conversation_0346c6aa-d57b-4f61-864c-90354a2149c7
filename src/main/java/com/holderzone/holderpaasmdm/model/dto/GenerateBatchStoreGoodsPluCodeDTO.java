package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;


/**
 * Description: 生成批量店铺商品秤内码DTO
 * Author: 向超
 * Date: 2024/12/12 15:36
 */
@Data
public class GenerateBatchStoreGoodsPluCodeDTO {
    /**
     * 店铺商品ID集合
     */
    @NotNull(message = "店铺商品ID列表不能为空")
    @Size(min = 1, message = "店铺商品ID列表不能为空")
    @JsonProperty("store_goods_id_list")
    private List<Integer> storeGoodsIdList;

    /**
     * 秤内码起始值
     */
    @NotNull(message = "秤内码不能为空")
    @JsonProperty("plu_code_start")
    private Integer pluCodeStart;

    /**
     * 秤的品牌
     */
    @NotNull(message = "秤的品牌不能为空")
    @JsonProperty("scale_type")
    private Integer scaleType;
}

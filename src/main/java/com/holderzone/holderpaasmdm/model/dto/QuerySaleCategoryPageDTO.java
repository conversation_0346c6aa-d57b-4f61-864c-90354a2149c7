package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 销售分类请求对象
 *
 * <AUTHOR>
 * @date 2025/6/3 15:01
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QuerySaleCategoryPageDTO extends PageDTO {

    /**
     * 适用范围（按渠道下的门店） (门店销售方案)
     */
    @NotNull(message = "店铺ID不能为空")
    @JsonProperty("store_id")
    @JsonAlias({"store_ids", "store_team_info_id"})
    private Integer storeId;

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 是否查询图片数据
     */
    @JsonProperty("is_query_picture")
    private Boolean isQueryPicture;
}

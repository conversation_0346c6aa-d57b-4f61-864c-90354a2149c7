package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * desc 订单状态变更DTO
 *
 * <AUTHOR>
 * @date 2024/12/19
 * @since 1.8
 */
@Data
public class OrderStatusChangeDTO {

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    @JsonProperty("order_code")
    private String orderCode;

    /**
     * 订单支付状态
     */
    @NotNull(message = "支付状态不能为空")
    @JsonProperty("pay_status")
    private Boolean payStatus;



}
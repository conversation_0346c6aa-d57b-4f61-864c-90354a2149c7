package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description: 查询基础 DTO
 * Author: 向超
 * Date: 2024/11/27 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBasePageDTO extends PageDTO {

    /**
     * 适用范围（按渠道下的门店） (门店销售方案)
     */
    @NotNull(message = "店铺ID不能为空")
    @JsonAlias({"store_id", "store_team_info_id"})
    private Integer storeId;

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private Integer channelId;
}

package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;


/**
 * Description: 查询POS渠道的可售商品详情DTO
 * Author: 向超
 * Date: 2025/02/06 15:36
 */
@Data
public class QueryStorePosGoodsAvailableForSaleDetailDTO {
    /**
     * 适用范围（按渠道下的门店） (店铺销售方案)
     */
    @NotNull(message = "店铺ID不能为空")
    @JsonAlias({"store_id", "store_team_info_id"})
    private Integer storeId;

    /**
     * 适用范围（按渠道） (渠道销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonAlias({"channel_id"})
    private Integer channelId;

    /**
     * 店铺商品ID
     */
    @JsonProperty("store_goods_id")
    private Integer storeGoodsId;

    /**
     * 是否查询库存
     */
    @JsonProperty("is_query_inventory")
    private Boolean isQueryInventory;

    /**
     * 店铺商品ID集合
     */
    @JsonProperty("store_goods_ids")
    private List<Integer> storeGoodsIds;

    /**
     * 企业id
     */
    @JsonIgnore
    private Long companyId;
}

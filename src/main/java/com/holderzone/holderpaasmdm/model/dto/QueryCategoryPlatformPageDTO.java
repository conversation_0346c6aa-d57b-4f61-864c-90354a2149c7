package com.holderzone.holderpaasmdm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/6/3 14:41
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryCategoryPlatformPageDTO extends PageDTO {

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    @NotNull(message = "渠道ID不能为空")
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 是否查询图片数据
     */
    @JsonProperty("is_query_picture")
    private Boolean isQueryPicture;
}

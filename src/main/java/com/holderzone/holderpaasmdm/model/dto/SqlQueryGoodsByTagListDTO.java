package com.holderzone.holderpaasmdm.model.dto;

import com.holderzone.holderpaasmdm.model.po.StoreGoodsPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalTime;

/**
 * 查询商品列表，标签内查询用
 *
 * <AUTHOR>
 * @date 2025/4/15 16:23
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class SqlQueryGoodsByTagListDTO extends StoreGoodsPO {

    /**
     * 套餐skuId
     */
    private Integer goodsPackageSkuId;

    /**
     * sku条形码
     */
    private String goodsPackageSkuBarCode;

    /**
     * 套餐商品关联表id
     */
    private Integer storeSalesProgramGoodsId;

    /**
     * sku商品销售时段名称
     */
    private String storeSalesProgramGoodsName;

    /**
     * 套餐商品价格关联表id
     */
    private Integer storeSalesProgramGoodsPriceId;

    /**
     * 套餐id
     */
    private Integer storeSalesProgramId;

    /**
     * 套餐是否默认
     */
    private Boolean storeSalesProgramIsDefault;

    /**
     * 套餐时间段
     */
    private String storeSalesProgramTimeSection;

    /**
     * 策略开始时间
     */
    private LocalTime start;
}

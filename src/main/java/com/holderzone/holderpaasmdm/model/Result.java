package com.holderzone.holderpaasmdm.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import static com.holderzone.holderpaasmdm.enumeraton.ResponseCode.SUCCESSFUL;

/**
 * Description: 响应结果封装
 * Author: 向超
 * Date: 2024/11/13 15:33
 */
@Data
@NoArgsConstructor
public class Result<T> {

    /**
     * 状态码
     **/
    private int returnCode;

    /**
     * 消息体
     **/
    private String returnMessage;

    /**
     * 返回数据
     **/
    private T data;

    public Result(T data) {
        this.data = data;
    }

    public Result(int returnCode, T data) {
        this.returnCode = returnCode;
        this.data = data;
    }

    public Result(int returnCode, String returnMessage, T data) {
        this.returnCode = returnCode;
        this.returnMessage = returnMessage;
        this.data = data;
    }

    public Result(int returnCode, String returnMessage) {
        this.returnCode = returnCode;
        this.returnMessage = returnMessage;
    }

    /**
     * 成功返回结果
     *
     * @param <T> T 泛型，返回的 data结构体
     * @return result 返回 Result 对象
     */
    public static <T> Result<T> success() {
        return new Result<>();
    }

    /**
     * 成功返回结果
     *
     * @param <T>  T 泛型，返回的data结构体
     * @param data 获取的数据
     * @return result  返回 Result 对象
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(SUCCESSFUL.getValue(), data);
    }

    /**
     * 成功返回结果
     *
     * @param <T>  T 泛型，返回的data结构体
     * @param data 获取的数据
     * @return result  返回 Result 对象
     */
    public static <T> Result<T> success(T data, String message) {
        return new Result<>(SUCCESSFUL.getValue(), message, data);
    }

    /**
     * 失败返回指定Code和结果
     *
     * @param <T>  T 泛型，返回的data结构体
     * @param code 响应code
     * @param data 获取的数据
     * @return result  返回 Result 对象
     */
    public static <T> Result<T> fail(int code, T data) {
        return new Result<>(code, data);
    }

    /**
     * 失败返回指定Code和结果
     *
     * @param <T>  T 泛型，返回的data结构体
     * @param code 响应code
     * @return result  返回 Result 对象
     */
    public static <T> Result<T> fail(int code) {
        return new Result<>(code, null);
    }

    /**
     * 失败返回指定Code和结果
     *
     * @param <T>  T 泛型，返回的data结构体
     * @param code 响应code
     * @return result  返回 Result 对象
     */
    public static <T> Result<T> fail(int code, String message) {
        return new Result<>(code, message, null);
    }
}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 店铺渠道绑定扩展表
 *
 * <AUTHOR>
 * @date 2025/6/6 17:51
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "store_channel_bind", autoResultMap = true)
public class StoreChannelBindPO extends BasePO {

    /**
     * 门店ID
     */
    private Integer storeId;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 绑定第三方店铺ID
     */
    private Long bindStoreId;

    /**
     * 绑定第三方店铺名称
     */
    private String bindStoreName;

    /**
     * 绑定类型 1. 私域商城 业务枚举 StoreChannelBindEnum
     */
    private Integer bindType;

}

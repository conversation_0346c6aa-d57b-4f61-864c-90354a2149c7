package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * Description: SKU商品表
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_package_sku", autoResultMap = true)
public class GoodsPackageSkuPO extends BasePO {

    /**
     * SKU类型枚举
     * - 企业   1
     * - 门店   2
     */
    @TableField(value = "spec_type")
    private Integer specType;

    /**
     * 企业商品ID
     */
    @TableField(value = "goods_id")
    private Integer goodsId;

    /**
     * 店铺商品ID
     */
    @TableField(value = "store_goods_id")
    private Integer storeGoodsId;

    /**
     * 门店ID
     */
    @TableField(value = "store_id")
    private Integer storeId;

    /**
     * SKU子关联ID
     */
    @TableField(value = "origin_sku_id")
    private Integer originSkuId;

    /**
     * 规格商品图片
     */
    private Integer cover;

    /**
     * 门店商品成本
     */
    private BigDecimal costs;

    /**
     * 门店商品售价
     */
    private BigDecimal sellingPrice;

    /**
     * 门店商品条码
     */
    private String barcode;

    /**
     * 规格商品SKU码
     */
    private String skuCode;

    /**
     * 商品单位
     */
    private Integer goodsUnit;

    /**
     * 规格商品UUID
     */
    private String goodsUuid;

    /**
     * SKU排序（排序越小越靠前）
     */
    private Integer sort;

    /**
     * 划线价
     */
    private BigDecimal linePrice;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 是否限购
     */
    private Boolean isLimitBuy;

    /**
     * 划线价
     */
    private Integer limitBuyNum;

    /**
     * 线上初始销量
     */
    private Integer onlineInitSales;
}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品渠道信息扩展表
 *
 * <AUTHOR>
 * @date 2025/6/9 9:21
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_channel_external", autoResultMap = true)
public class GoodsChannelExternalPO extends BasePO {

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 门店商品id
     */
    private Integer storeGoodsId;

    /**
     * 企业商品ID
     */
    private Integer goodsId;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * 视频id
     */
    private Integer videoId;

    /**
     * 是否开通同城配送
     */
    private Boolean isCityDelivery;

    /**
     * 是否开通到点自取
     */
    private Boolean isSelfPickup;

    /**
     * 运费模板ID
     */
    private Long freightTemplateId;

    /**
     * 服务保障 业务枚举是ServiceGuaranteeEnum
     * 数组形式保存
     * 1:包邮
     * 2:7天退换
     * 3:48小时发货
     * 4:假一赔十
     * 5:正品保障
     */
    private String serviceGuarantee;

    /**
     * 是否开通快速发货
     */
    @TableField("is_express_delivery")
    private Boolean isExpressDelivery;
}

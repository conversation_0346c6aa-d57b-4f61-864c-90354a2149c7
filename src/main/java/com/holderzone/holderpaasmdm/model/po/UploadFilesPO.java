package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 上传文件表
 * Author: 向超
 * Date: 2024/11/27 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "upload_files")
public class UploadFilesPO extends BasePO {

    /**
     * 文件名称
     */
    private String filename;

    /**
     * 文件URL
     */
    private String url;

    /**
     * 预览地址
     */
    private String previewUrl;

    /**
     * 文件大小
     */
    private Integer fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * md5
     */
    private String md5;

    /**
     * 备注
     */
    private String note;

    /**
     * 用户ID
     */
    private String userId;
}

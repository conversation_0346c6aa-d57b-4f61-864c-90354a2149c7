package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * Description: 门店销售方案中的商品表
 * Author: 向超
 * Date: 2024/11/14 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "store_sales_program_goods", autoResultMap = true)
public class StoreSalesProgramGoodsPO extends BasePO {

    /**
     * 门店销售方案id
     */
    private Integer storeSalesProgramId;

    /**
     * 商品ID (门店) store_goods.id
     *
     * @see StoreGoodsPO
     */
    private Integer goodsId;

    /**
     * 商品在该销售方案中的售卖名称
     */
    private String goodsSaleName;

    /**
     * 售卖标签
     */
    private Integer tag;

    /**
     * 封面
     */
    private Integer coverPicture;

    /**
     * 商品SKU ID
     */
    private Integer goodsPackageSkuId;

    /**
     * 默认售价
     */
    private BigDecimal sellingPrice;

    /**
     * 起售数量，默认0
     */
    private BigDecimal startingSaleQuantity;
}

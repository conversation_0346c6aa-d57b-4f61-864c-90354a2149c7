package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 商品标签
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_label", autoResultMap = true)
public class GoodsLabelPO extends BasePO {

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签颜色
     */
    private String color;

    /**
     * 状态启用禁用
     */
    private Boolean isEnable;
}
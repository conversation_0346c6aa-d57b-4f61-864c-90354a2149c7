package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.holderpaasmdm.common.handler.JacksonTypeForListHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.IOException;
import java.io.Serial;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Description: 订单表
 * <AUTHOR>
 * Date: 2024/12/18 17:30
 */
@Data
@TableName(value = "orders", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class OrdersPO extends BasePO {

    /**
     * 零售记录id
     */
    @TableField("record_id")
    private Integer recordId;

    /**
     * 销售订单号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 版本号
     */
    @TableField("version")
    private Integer version;

    /**
     * 订单之前的状态
     */
    @TableField("before_state")
    private Integer beforeState;

    /**
     * 下单客户
     */
    @TableField("member_name")
    private String memberName;

    /**
     * 下单用户会员中心手机号
     */
    @TableField("member_phone")
    private String memberPhone;

    /**
     *
     */
    @TableField("member_guid")
    private String memberGuid;

    /**
     *
     */
    @TableField("business_day")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date businessDay;

    /**
     * 工具字段-所属营业日开始
     */
    @TableField("business_day_start")
    private Timestamp businessDayStart;

    /**
     * 工具字段-所属营业日结束
     */
    @TableField("business_day_end")
    private Timestamp businessDayEnd;

    /**
     * 操作员id
     */
    @TableField("operater_id")
    private Integer operaterId;

    /**
     * 操作员名称
     */
    @TableField("operater_name")
    private String operaterName;

    /**
     * 操作员手机号
     */
    @TableField("operater_phone")
    private String operaterPhone;

    /**
     * 导购员id
     */
    @TableField("shopping_guide_id")
    private Integer shoppingGuideId;

    /**
     * 导购员名字
     */
    @TableField("shopping_guide_name")
    private String shoppingGuideName;

    /**
     * 门店id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 门店部门id-权限控制
     */
    @TableField("store_team_info_id")
    private Integer storeTeamInfoId;

    /**
     * 三大中心门店id
     */
    @TableField("holder_store_id")
    private Integer holderStoreId;

    /**
     * 门店名称
     */
    @TableField("store_name")
    private String storeName;

    /**
     * 渠道id
     */
    @TableField("channel_id")
    private Integer channelId;

    /**
     * 渠道名称
     */
    @TableField("channel_name")
    private String channelName;

    /**
     * 企业id
     */
    @TableField("company_id")
    private Integer companyId;

    /**
     * 实收金额
     */
    @TableField("receive_price")
    private String receivePrice;

    /**
     * 订单应收金额
     */
    @TableField("origin_price")
    private String originPrice;

    /**
     * 订单原始总金额
     */
    @TableField("total_origin_price")
    private String totalOriginPrice;

    /**
     * 订单所有优惠金额
     */
    @TableField("discount_price")
    private String discountPrice;

    /**
     * 订单完成支付时间
     */
    @TableField("paid_at")
    private Timestamp paidAt;

    /**
     * 找零金额
     */
    @TableField("change_money")
    private String changeMoney;

    /**
     * 实付金额合计
     */
    @TableField("actually_paid_amount")
    private String actuallyPaidAmount;

    /**
     * 当前订单内所有商品小计总和（优惠后），需求中为订单详情中的订单金额
     */
    @TableField("item_total_price")
    private String itemTotalPrice;

    /**
     * 下单设备的设备编号
     */
    @TableField("device_number")
    private String deviceNumber;

    /**
     *
     */
    @TableField(value = "external_discounts", typeHandler = JacksonTypeForListHandler.class)
    private List<String> externalDiscounts;

    /**
     * bff原始购物车数据
     */
    @TableField("external_shopcar")
    private Object externalShopcar;

    /**
     * 订单状态
     */
    @TableField("state")
    private Integer state;

    /**
     * 订单来源
     */
    @TableField("order_source")
    private String orderSource;

    /**
     * 零售使用状态
     */
    @TableField("create_at")
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    @TableField("update_at")
    private Timestamp updateAt;

    /**
     * 工具字段-创建时间开始
     */
    @TableField("create_at_start")
    private Timestamp createAtStart;

    /**
     * 工具字段-创建时间结束
     */
    @TableField("create_at_end")
    private Timestamp createAtEnd;

    /**
     * 下单用户会员卡信息guid
     */
    @TableField("member_card_info_guid")
    private String memberCardInfoGuid;

    /**
     * 优惠券回滚 1 回滚 0 不回滚
     */
    @TableField("coupon_rollback")
    private Integer couponRollback;

    @Serial
    private void writeObject(java.io.ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
    }

    @Serial
    private void readObject(java.io.ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
    }

}

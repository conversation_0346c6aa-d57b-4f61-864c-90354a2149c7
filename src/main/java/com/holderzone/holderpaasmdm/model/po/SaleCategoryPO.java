package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 销售分组
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sale_category", autoResultMap = true)
public class SaleCategoryPO extends BasePO {

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级分类ID
     */
    private Integer parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 资源id
     */
    private Integer coverId;
}

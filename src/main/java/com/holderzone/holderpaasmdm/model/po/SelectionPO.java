package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * Description: 存放各种用于选择的列表、树结构数据
 * Author: 向超
 * Date: 2024/11/14 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "selection", autoResultMap = true)
public class SelectionPO extends BasePO {

    /**
     * 选项名称
     */
    private String displayName;

    /**
     * 选项在组内的序号
     */
    private Long selectionOrder;

    /**
     * 所在组ID
     */
    private Integer selectionGroup;

    /**
     * 注释说明
     */
    private String selectionComment;

    /**
     * 附加属性
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private SelectionProperty selectionProperty;

    @Data
    public static class SelectionProperty implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 分类名称
         */
        private String categoryName;
        /**
         * 分类ID
         */
        private Integer recordId;
        /**
         * 模块名称
         */
        private String tableName;
    }
}

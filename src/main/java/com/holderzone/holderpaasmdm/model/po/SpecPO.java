package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 规格表
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "spec", autoResultMap = true)
public class SpecPO extends BasePO {

    /**
     * 规格名称
     */
    private String name;

    /**
     * 规格类型枚举
     * - 系统级   1
     * - 企业级   2
     * - 门店级   3
     */
    @TableField(value = "spec_type")
    private Integer specType;

    /**
     * 门店ID
     */
    @TableField(value = "store_id")
    private Integer storeId;

    /**
     * 商品排序（排序越小越靠前）
     */
    private Integer sort;
}

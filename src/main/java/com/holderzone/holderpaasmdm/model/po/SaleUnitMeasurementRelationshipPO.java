package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 销售单位计量关系
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sale_unit_measurement_relationship", autoResultMap = true)
public class SaleUnitMeasurementRelationshipPO extends BasePO {

    /**
     * 计量单位类别id
     */
    private Integer leftUnitId;

    /**
     * 计量单位名称id
     */
    private Integer unitId;

    /**
     * 换算单位
     */
    private Integer conversionUnit;

    /**
     * 计量单位分类id
     */
    private Integer categoryId;
}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: SKU商品规格中间表
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sku_spec_details", autoResultMap = true)
public class SkuSpecDetailsPO extends BasePO {

    /**
     * SKU ID
     */
    @TableField(value = "sku_id")
    private Integer skuId;

    /**
     * 商品规格明细ID
     */
    @TableField(value = "goods_spec_detail_id")
    private Integer goodsSpecDetailId;
}

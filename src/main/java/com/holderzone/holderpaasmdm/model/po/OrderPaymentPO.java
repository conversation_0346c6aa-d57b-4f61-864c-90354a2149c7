package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.IOException;
import java.io.Serial;
import java.sql.Timestamp;

/**
 * Description: 订单付款表
 * <AUTHOR>
 * @since 2024年12月18日
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_payment", autoResultMap = true)
public class OrderPaymentPO extends BasePO {

    /**
     * 零售记录id
     */
    @TableField("record_id")
    private Integer recordId;

    /**
     * 订单id，并不是销售订单的id
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 订单号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 企业id
     */
    @TableField("company_id")
    private Integer companyId;

    /**
     * 支付号
     */
    @TableField("payment_number")
    private String paymentNumber;

    /**
     * 支付方式类型[0、自定义；1、现金支付；2、会员支付；3、聚合支付]
     */
    @TableField("payment_type")
    private Integer paymentType;

    /**
     * 支付方式名称
     */
    @TableField("payment_method_name")
    private String paymentMethodName;

    /**
     * 支付方式id
     */
    @TableField("payment_method_id")
    private Integer paymentMethodId;

    /**
     * 支付金额
     */
    @TableField("payment_amount")
    private String paymentAmount;

    /**
     * 支付条码（聚合支付）
     */
    @TableField("payment_auth_code")
    private String paymentAuthCode;

    /**
     * 设备终端号（聚合支付）
     */
    @TableField("payment_terminal_id")
    private String paymentTerminalId;

    /**
     * 是否支付成功
     */
    @TableField("is_pay_success")
    private Boolean isPaySuccess;

    /**
     * 是否预支付成功
     */
    @TableField("is_pre_pay_success")
    private Boolean isPrePaySuccess;

    /**
     * 找零金额
     */
    @TableField("change_amount")
    private String changeAmount;

    /**
     * 会员支付参数
     */
    @TableField("member_payment_args")
    private Object memberPaymentArgs;

    /**
     * 支付成功时间
     */
    @TableField("pay_success_time")
    private Timestamp paySuccessTime;

    /**
     * 工具字段-支付成功时间开始
     */
    @TableField("pay_success_time_start")
    private Timestamp paySuccessTimeStart;

    /**
     * 工具字段-支付成功时间结束
     */
    @TableField("pay_success_time_end")
    private Timestamp paySuccessTimeEnd;

    /**
     * 零售创建时间
     */
    @TableField("create_at")
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    @TableField("update_at")
    private Timestamp updateAt;

    /**
     * 支付方式排序
     */
    @TableField("sort")
    private Integer sort;

    @Serial
    private void writeObject(java.io.ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
    }

    @Serial
    private void readObject(java.io.ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
    }

}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 销售分组和门店商品关系表
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sale_category_and_store_goods", autoResultMap = true)
public class SaleCategoryAndStoreGoodsPO extends BasePO {

    /**
     * 店铺商品ID
     */
    private Integer storeGoodsId;

    /**
     * 销售分组ID
     */
    private Integer saleCategoryId;
}

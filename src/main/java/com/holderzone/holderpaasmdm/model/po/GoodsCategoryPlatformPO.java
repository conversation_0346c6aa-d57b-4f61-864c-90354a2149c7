package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 平台商品分类
 *
 * <AUTHOR>
 * @date 2025/5/30 9:34
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_category_platform", autoResultMap = true)
public class GoodsCategoryPlatformPO extends BasePO {

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级分类id
     */
    private Integer parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 资源id
     */
    private Integer coverId;

    /**
     * 类目扣除比例
     */
    private BigDecimal deductionRatio;
}

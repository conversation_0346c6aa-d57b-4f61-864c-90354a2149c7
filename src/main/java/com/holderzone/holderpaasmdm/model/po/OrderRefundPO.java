package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

/**
 * Description: 订单退款表
 * <AUTHOR>
 * Date: 2024/12/18 17:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_refund", autoResultMap = true)
public class OrderRefundPO extends BasePO {

    /**
     * 零售记录id
     */
    private Long recordId;

    /**
     * 销售订单id
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 退款号
     */
    private String refundNumber;

    /**
     * 退款金额
     */
    private String refundAmount;

    /**
     * 实际退款金额
     */
    private String actuallyRefundAmount;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 操作员id
     */
    private Integer operaterId;

    /**
     * 操作员名称
     */
    private String operaterName;

    /**
     * 操作员手机号
     */
    private String operaterPhone;

    /**
     * 操作退款时设备的门店id，注意和订单的门店id不一定一样
     */
    private Integer storeId;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款类型别名
     */
    private String refundTypeAlias;

    /**
     * 下单设备的设备编号
     */
    private String deviceNumber;

    /**
     * 退款类型[1、整单退款；2、部分退款；3、现金退款；4、部分现金退款]
     */
    private Integer refundType;

    /**
     * 退款支付方式，仅在部分退款中作为标记[0、自定义；1、现金支付；2、会员支付；3、聚合支付]
     */
    private Integer refundPaymentMethod;

    /**
     * 零售创建时间
     */
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    private Timestamp updateAt;

}

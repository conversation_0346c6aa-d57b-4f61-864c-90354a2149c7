package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

/**
 * Description: 订单退款商品表
 * Author: 向超
 * Date: 2025/01/14 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "order_refund_item")
public class OrderRefundItemPO extends BasePO {
    /**
     * 零售记录id
     */
    private Integer recordId;

    /**
     * 订单退款id
     */
    private Integer orderRefundId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 退款号
     */
    private String refundNumber;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 订单商品id
     */
    private Integer orderItemId;

    /**
     * 退款数量
     */
    private String refundQuantity;

    /**
     * 商品小计
     */
    private String refundOriginPrice;

    /**
     * 商品退款价格
     */
    private String refundPrice;

    /**
     * 商品实际退款金额
     */
    private String actuallyRefundItemAmount;

    /**
     * 是否退成功
     */
    private Boolean isRefundSuccess;

    /**
     * 商品原价若存在改价则这里是改后价
     */
    private String sellingPrice;

    /**
     * 零售创建时间
     */
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    private Timestamp updateAt;
}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description: 店铺商品与秤内码的对应关系
 * Author: 向超
 * Date: 2024/12/12 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_scale_code", autoResultMap = true)
public class GoodsScaleCodePO extends BasePO {

    /**
     * 店铺商品ID
     *
     * @see StoreGoodsPO#id
     */
    private Integer goodsId;

    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 秤内码
     */
    private String pluCode;

    /**
     * 秤内自编码
     */
    private String scaleCustomCode;

    /**
     * 商品传秤，秤的品牌
     */
    private Integer scaleType;
}
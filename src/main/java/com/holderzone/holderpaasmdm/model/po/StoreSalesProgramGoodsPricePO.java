package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * Description: 门店销售方案中的商品时段价格表
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "store_sales_program_goods_price", autoResultMap = true)
public class StoreSalesProgramGoodsPricePO extends BasePO {

    /**
     * 门店销售方案id
     */
    private Integer storeSalesProgramId;

    /**
     * 商品ID (门店) store_goods.id
     */
    private Integer goodsId;

    /**
     * 销售方案商品表主键id
     */
    private Integer storeSalesProgramGoodsId;

    /**
     * 销售方案时段类型下的时间段序号
     */
    private Integer serialNumber;

    /**
     * 时段售价类型
     * 1：固定
     * 2：浮动比例
     */
    private Integer priceType;

    /**
     * 时段售价设置，固定价或百分比
     */
    private BigDecimal timeSectionPrice;

    /**
     * 时段售价，注意对外取这个售价
     */
    private BigDecimal timeSectionPriceCompute;
}

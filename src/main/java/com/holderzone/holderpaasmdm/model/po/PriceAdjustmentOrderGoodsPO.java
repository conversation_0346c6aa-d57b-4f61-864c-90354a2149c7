package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.holderzone.holderpaasmdm.common.handler.JacksonTypeForListHandler;
import com.holderzone.holderpaasmdm.common.handler.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * Description: 调价单商品信息
 * Author: 向超
 * Date: 2024/12/17 15:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "price_adjustment_order_goods", autoResultMap = true)
public class PriceAdjustmentOrderGoodsPO extends BasePO {

    /**
     * 调价单表ID
     */
    private Integer priceAdjustmentOrderId;

    /**
     * 门店商品ID
     */
    private Integer goodsId;

    /**
     * 门店商品名字
     */
    private String goodsName;

    /**
     * 门店商品条码
     */
    private String barcode;

    /**
     * 门店商品封面ID
     */
    private Integer cover;

    /**
     * 门店商品成本
     */
    private BigDecimal costs;

    /**
     * 门店商品建议售价
     */
    private BigDecimal sellingPrice;

    /**
     * 门店商品单位
     */
    private Integer goodsUnit;

    /**
     * 门店商品单位名
     */
    private String goodsUnitName;

    /**
     * 门店商品SPU码
     */
    private String spuCode;

    /**
     * 销售分组名称
     * 使用 JSON 字符串处理
     */
    @TableField(typeHandler = JacksonTypeForListHandler.class)
    private List<String> saleCategoryName;

    /**
     * 销售分组节点
     * 使用 JSON 字符串处理
     */
    @TableField(typeHandler = JacksonTypeSaleCategoryNodeHandler.class)
    private List<SaleCategoryNode> saleCategoryNode;

    /**
     * 门店商品UUID
     */
    private String goodsUuid;

    /**
     * 商品规格信息
     * 使用 JSON 字符串处理
     */
    @TableField(typeHandler = JacksonTypeGoodsSpecificationHandler.class)
    private List<GoodsSpecification> goodsSpecification;

    /**
     * 1 单规格 2 多规格
     */
    private Integer goodsSpec;

    /**
     * 商品SKU ID
     */
    private Integer goodsPackageSkuId;

    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 销售分组节点
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SaleCategoryNode implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 节点ID
         */
        private Integer id;

        /**
         * 节点名称
         */
        private String name;

        /**
         * 父节点ID
         */
        private Integer parentId;

        /**
         * 是否绑定, true: 绑定, false: 未绑定
         */
        private Boolean isBind = false;
    }

    /**
     * 销售分组节点
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GoodsSpecification implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 规格ID
         */
        private Integer goodsSpecId;

        /**
         * 规格名称
         */
        private String goodsSpecName;

        /**
         * 规格值ID
         */
        private Integer goodsSpecDetailId;

        /**
         * 规格值名称
         */
        private String goodsSpecDetailName;
    }

    public static class JacksonTypeSaleCategoryNodeHandler extends JacksonTypeHandler<PriceAdjustmentOrderGoodsPO.SaleCategoryNode> {
        public JacksonTypeSaleCategoryNodeHandler() {
            setTagClass(PriceAdjustmentOrderGoodsPO.SaleCategoryNode.class);
        }
    }

    public static class JacksonTypeGoodsSpecificationHandler extends JacksonTypeHandler<PriceAdjustmentOrderGoodsPO.GoodsSpecification> {
        public JacksonTypeGoodsSpecificationHandler() {
            setTagClass(PriceAdjustmentOrderGoodsPO.GoodsSpecification.class);
        }
    }
}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.holderzone.holderpaasmdm.common.handler.JsonTypIntegerListHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Description: 门店商品表（SPU）
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@Data
@TableName(value = "store_goods", autoResultMap = true)
public class StoreGoodsPO extends BasePO {

    /**
     * 门店商品名称
     */
    private String goodsName;

    /**
     * 拼音简码
     */
    private String pinyinCode;

    /**
     * 门店商品SPU码
     */
    private String spuCode;

    /**
     * 组合类型（单品/套餐）
     * 1：单品，2：套餐
     */
    private Integer comboType;

    /**
     * 属性（门店商品特征）
     * 1：可销售，2：可生产，3：可采购
     */
    @TableField(typeHandler = JsonTypIntegerListHandler.class)
    private List<Integer> goodsFeatures;

    /**
     * 门店商品类型
     */
    private Integer goodsType;

    /**
     * 计价方式（普通/计数/计重）
     * 1：普通，2：计数，3：计重
     */
    private Integer valuationMethod;

    /**
     * 库存属性(单品/称重单品)
     * 1：可库存产品，2：虚拟产品，3：服务类产品
     */
    private Integer inventoryProperty;

    /**
     * 门店商品分类
     */
    private Integer category;

    /**
     * 门店商品单位
     */
    private Integer goodsUnit;

    /**
     * 门店商品品牌
     */
    private Integer brand;

    /**
     * 门店商品条码
     */
    private String barcode;

    /**
     * 门店商品标签
     */
    private Integer goodsLabel;

    /**
     * 门店商品成本
     */
    private BigDecimal costs;

    /**
     * 门店商品售价
     */
    private BigDecimal sellingPrice;

    /**
     * 供应商
     */
    private String provider;

    /**
     * 生产日期
     */
    private LocalDateTime productionDate;

    /**
     * 保质日期
     */
    private Integer warrantyDate;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 产地
     */
    private String origin;

    /**
     * 门店商品介绍
     */
    private String introductionText;

    /**
     * 商品状态，可选值：1 正常 2 禁用
     */
    private Integer status;

    /**
     * 上下架状态，可选值：1 已上架、2 已下架
     */
    private Integer listingStatus;

    /**
     * 门店商品唯一id，后续其他业务使用
     */
    private String goodsUuid;

    /**
     * 企业商品唯一id，后续其他业务使用
     */
    private String relatedGoodsUuid;

    /**
     * 商品排序
     */
    private Integer sort;

    /**
     * 门店ID
     */
    private Integer storeId;

    /**
     * 企业商品ID
     */
    private Integer goodsId;

    /**
     * 门店商品自编码
     */
    private String goodsCustomCode;

    /**
     * 店铺商品封面
     */
    private Integer cover;

    /**
     * 商品规格：1：单规格 2：多规格
     */
    private Integer goodsSpec;

    /**
     * 平台分类id
     *
     * @see GoodsCategoryPlatformPO
     */
    private Integer categoryPlatform;

    /**
     * 供应商商品id
     */
    private Integer providerGoodsId;

    @Override
    public int hashCode() {
        return Objects.hash(spuCode);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        StoreGoodsPO that = (StoreGoodsPO) obj;
        return Objects.equals(spuCode, that.spuCode);
    }
}
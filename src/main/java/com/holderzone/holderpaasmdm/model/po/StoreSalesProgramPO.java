package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.common.handler.JacksonTypeForListHandler;
import com.holderzone.holderpaasmdm.common.handler.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

/**
 * Description: 门店销售方案表
 * Author: 向超
 * Date: 2024/11/13 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "store_sales_program", autoResultMap = true)
public class StoreSalesProgramPO extends BasePO {

    /**
     * 门店销售方案名称
     */
    private String storeSalesProgramName;

    /**
     * 门店Id
     */
    private Integer storeId;

    /**
     * 渠道Id
     */
    private Integer channelId;

    /**
     * 启用禁用状态，True启用，False禁用
     */
    private Boolean isEnable;

    /**
     * true默认全时段销售方案，false自定义销售方案
     */
    private Boolean isDefault;

    /**
     * 循环类型
     * 1: 天
     * 2: 周
     * 3: 月
     */
    private Integer cycleType;

    /**
     * 指定的类型区间 （days暂时用不上）
     * 例如：
     * 天：1-31
     * 周：0-6
     * 月：1-12
     */
    @TableField(typeHandler = JacksonTypeForListHandler.class)
    private List<Integer> days;

    /**
     * 销售方案时段Json
     */
    @TableField(typeHandler = JacksonTypeSectionHandler.class)
    private List<Section> timeSection;

    /**
     * 销售时段
     */
    @Data
    public static class Section implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 序号
         */
        @JsonProperty("serial_number")
        private Integer serialNumber;
        /**
         * 开始时刻
         * 02:00
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
        private LocalTime start;
        /**
         * 结束时刻
         * 06:00
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
        private LocalTime end;
    }

    /**
     * 门店销售方案时段Json处理器
     */
    public static class JacksonTypeSectionHandler extends JacksonTypeHandler<StoreSalesProgramPO.Section> {
        public JacksonTypeSectionHandler() {
            setTagClass(StoreSalesProgramPO.Section.class);
        }
    }
}

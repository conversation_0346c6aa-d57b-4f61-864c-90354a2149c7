package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 商品品牌
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_brand", autoResultMap = true)
public class GoodsBrandPO extends BasePO {

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 图片logo
     */
    private Integer logo;

    /**
     * 状态启用禁用
     */
    private Boolean isEnable;
}

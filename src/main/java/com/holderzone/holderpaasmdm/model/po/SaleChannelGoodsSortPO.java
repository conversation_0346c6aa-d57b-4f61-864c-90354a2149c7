package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 店铺渠道商品排序
 * Author: 向超
 * Date: 2024/12/18 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sale_channel_goods_sort")
public class SaleChannelGoodsSortPO extends BasePO {
    /**
     * 父级分类ID
     */
    private Integer storeGoodsId;

    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 排序
     */
    private Integer sort;
}

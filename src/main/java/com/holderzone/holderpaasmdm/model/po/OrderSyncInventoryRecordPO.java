package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: 订单出入库同步记录
 * <AUTHOR>
 * @since 2024年12月20日
 */
@Data
@TableName("order_sync_inventory_record")
public class OrderSyncInventoryRecordPO implements Serializable {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 退款单号
     */
    @TableField("refund_number")
    private String refundNumber;

    /**
     * 出入库类型：OUTBOUND:出库，PARTIAL_INBOUND:部分入库，ALL_INBOUND:全部入库
     */
    @TableField("type")
    private String type;

    /**
     * 请求参数
     */
    @TableField("param")
    private String param;

    /**
     * 成功标志：FAILED：失败的，SUCCESSFUL：成功的
     */
    @TableField("flag")
    private String flag;

    /**
     * 调用库存同步接口的响应参数
     */
    @TableField("response")
    private String response;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 商品计量单位
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sale_unit_measurement", autoResultMap = true)
public class SaleUnitMeasurementPO extends BasePO {

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 计量单位类别ID
     */
    private Integer categoryId;

    /**
     * 商品单位类别枚举，1：计重，2：其他
     * 新建单位默认为其他
     */
    private Integer categoryEnum;

    /**
     * 是否内置数据
     */
    private Boolean isBuiltIn;
}

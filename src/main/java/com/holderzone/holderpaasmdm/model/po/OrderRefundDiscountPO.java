package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

/**
 * Description: 订单退款折扣表
 * Author: 向超
 * Date: 2025/01/14 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "order_refund_discount")
public class OrderRefundDiscountPO extends BasePO {
    /**
     * 零售记录id
     */
    private Integer recordId;

    /**
     * 订单退款id
     */
    private Integer orderRefundId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 退款号
     */
    private String refundNumber;

    /**
     * 订单优惠id
     */
    private Integer orderDiscountId;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 优惠金额
     */
    private String discountAmount;

    /**
     * 优惠名称
     */
    private String discountName;

    /**
     * 是否退成功
     */
    private Boolean isRefundSuccess;

    /**
     * 零售创建时间
     */
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    private Timestamp updateAt;
}

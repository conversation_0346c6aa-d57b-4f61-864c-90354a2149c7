package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * Description: 门店销售方案的商品的分类表
 * Author: 向超
 * Date: 2024/11/14 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "store_sales_program_goods_category", autoResultMap = true)
public class StoreSalesProgramGoodsCategoryPO extends BasePO {

    /**
     * 销售方案商品表ID（商品价格策略ID）
     */
    private Integer salesProgramGoodsId;

    /**
     * 销售分类ID（enums表）
     */
    private Integer categoryId;
}

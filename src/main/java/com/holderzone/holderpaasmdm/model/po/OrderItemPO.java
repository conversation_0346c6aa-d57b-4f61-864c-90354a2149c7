package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.IOException;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: 订单明细表
 * <AUTHOR>
 * @since 2024年12月19日
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("order_item")
public class OrderItemPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1;

    /**
     * 零售记录id
     */
    @TableField("record_id")
    private Integer recordId;

    /**
     * 订单id，并不是销售订单的id
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 订单号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 企业id
     */
    @TableField("company_id")
    private Integer companyId;

    /**
     * 购买数量
     */
    @TableField("purchase_quantity")
    private String purchaseQuantity;

    /**
     * 商品原始售价金额小计
     */
    @TableField("origin_total_price_in_shopcaritem")
    private String originTotalPriceInShopcaritem;

    /**
     * 商品在购物车优惠过后的价格(含所有优惠)
     */
    @TableField("shop_price")
    private String shopPrice;

    /**
     * 商品实收金额（销售金额）
     */
    @TableField("actual_receive_price")
    private String actualReceivePrice;

    /**
     * 门店销售策略ID
     */
    @TableField("store_sales_program_id")
    private Integer storeSalesProgramId;

    /**
     * 门店销售策略商品ID
     */
    @TableField("store_sales_program_goods_id")
    private Integer storeSalesProgramGoodsId;

    /**
     * 商品id
     */
    @TableField("goods_id")
    private Integer goodsId;

    /**
     * 商品在购物车中的uuid编码，用于和订单商品表关联
     */
    @TableField("goods_uuid_in_shopcar")
    private String goodsUuidInShopcar;

    /**
     * 商品零售优惠金额
     */
    @TableField("retail_discount_amount")
    private String retailDiscountAmount;

    /**
     * 商品会员优惠金额
     */
    @TableField("member_discount_amount")
    private String memberDiscountAmount;

    /**
     * 商品优惠总金额
     */
    @TableField("discount_amount_all")
    private String discountAmountAll;

    /**
     * 商品系统分类[1、来自销售中心的商品；2、无码商品；3、快速收银商品]
     */
    @TableField("mercandise_type")
    private Integer mercandiseType;

    /**
     * 商品成本价格
     */
    @TableField("costs")
    private String costs;

    /**
     * 会员价
     */
    @TableField("vip_price")
    private String vipPrice;

    /**
     * 门店商品spu码
     */
    @TableField("spu_code")
    private String spuCode;

    /**
     * 所属SPU ID
     */
    @TableField("spu_id")
    private Integer spuId;

    /**
     * 商品分类ID
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 商品分类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 商品图片URL
     */
    @TableField("picture_url")
    private String pictureUrl;

    /**
     * 商品销售名称
     */
    @TableField("goods_sale_name")
    private String goodsSaleName;

    /**
     * 商品条码
     */
    @TableField("barcode")
    private String barcode;

    /**
     *
     */
    @TableField("goods_custom_code")
    private String goodsCustomCode;

    /**
     * 商品组合类型ID 41:单品 42:套餐 43:称重单品
     */
    @TableField("combo_type")
    private Integer comboType;

    /**
     * 商品组合类型名称
     */
    @TableField("combo_type_name")
    private String comboTypeName;

    /**
     * 商品原价若存在改价则这里是改后价
     */
    @TableField("selling_price")
    private String sellingPrice;

    /**
     * 改价后商品售价
     */
    @TableField("discount_price_in_shopcar")
    private String discountPriceInShopcar;

    /**
     * 商品库存
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 商品单位
     */
    @TableField("goods_unit_name")
    private String goodsUnitName;

    /**
     * 商品单位ID
     */
    @TableField("goods_unit_id")
    private Integer goodsUnitId;

    /**
     * 商品规格
     */
    @TableField("goods_specification")
    private Object goodsSpecification;

    /**
     * 商品属性
     */
    @TableField("goods_property")
    private Object goodsProperty;

    /**
     * 商品标签
     */
    @TableField("tag_name")
    private Object tagName;

    /**
     * PLU码，称内码
     */
    @TableField("plu_code")
    private Object pluCode;

    /**
     * 上架状态 true:上架 false:下架
     */
    @TableField("is_upsell")
    private Boolean isUpsell;

    /**
     * 上架时间
     */
    @TableField("upsell_time")
    private LocalDateTime upsellTime;

    /**
     * 商品改价优惠小计
     */
    @TableField("discount_total_price_item")
    private String discountTotalPriceItem;

    /**
     * 商品额外数据
     */
    @TableField("extra")
    private String extra;

    /**
     * 零售创建时间
     */
    @TableField("create_at")
    private LocalDateTime createAt;

    /**
     * 零售更新时间
     */
    @TableField("update_at")
    private LocalDateTime updateAt;

    @Serial
    private void writeObject(java.io.ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
    }

    @Serial
    private void readObject(java.io.ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
    }

}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 规格明细表
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "spec_detail", autoResultMap = true)
public class SpecDetailPO extends BasePO {

    /**
     * 规格ID
     *
     * @see SpecPO
     */
    @TableField(value = "spec_id")
    private Integer specId;

    /**
     * 规格值名称
     */
    private String name;

    /**
     * 商品排序（排序越小越靠前）
     */
    private Integer sort;

    /**
     * 是否为内置
     */
    @TableField(value = "built_in")
    private Boolean builtIn;
}

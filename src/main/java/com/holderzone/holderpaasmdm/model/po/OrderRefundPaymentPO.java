package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;

/**
 * Description: 订单退款付款表
 * <AUTHOR>
 * @since 2024年12月18日
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_refund_payment", autoResultMap = true)
public class OrderRefundPaymentPO extends BasePO {

    /**
     * 零售记录id
     */
    @TableField("record_id")
    private Integer recordId;

    /**
     * 订单退款id
     */
    @TableField("order_refund_id")
    private Integer orderRefundId;

    /**
     * 订单号
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * 退款号
     */
    @TableField("refund_number")
    private String refundNumber;

    /**
     * 企业id
     */
    @TableField("company_id")
    private Integer companyId;

    /*
     * 支付方式类型[0、自定义；1、现金支付；2、会员支付；3、聚合支付]
     */
    @TableField("refund_payment_type")
    private Integer refundPaymentType;

    /**
     * 支付平台返回的退款ID
     */
    @TableField("refund_payment_id")
    private String refundPaymentId;

    /**
     * 退款的支付方式id
     */
    @TableField("refund_payment_method_id")
    private Integer refundPaymentMethodId;

    /**
     * 退款支付方式
     */
    @TableField("refund_payment_name")
    private String refundPaymentName;

    /**
     * 退款支付号
     */
    @TableField("refund_payment_number")
    private String refundPaymentNumber;

    /**
     * 退款支付金额
     */
    @TableField("refund_payment_amount")
    private String refundPaymentAmount;

    /**
     * 是否退成功
     */
    @TableField("is_refund_success")
    private Boolean isRefundSuccess;

    /**
     * 是否预退款成功
     */
    @TableField("is_pre_refund_success")
    private Boolean isPreRefundSuccess;

    /**
     * 退款成功时间
     */
    @TableField("refund_success_time")
    private Timestamp refundSuccessTime;

    /**
     * 工具字段-退款成功时间开始
     */
    @TableField("refund_success_time_start")
    private Timestamp refundSuccessTimeStart;

    /**
     * 工具字段-退款成功时间结束
     */
    @TableField("refund_success_time_end")
    private Timestamp refundSuccessTimeEnd;

    /**
     * 零售创建时间
     */
    @TableField("create_at")
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    @TableField("update_at")
    private Timestamp updateAt;

    /**
     * 支付方式排序
     */
    @TableField("sort")
    private Integer sort;

}

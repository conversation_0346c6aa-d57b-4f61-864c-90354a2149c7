package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;


/**
 * Description: 调价单商品调价信息
 * Author: 向超
 * Date: 2024/12/17 15:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "price_adjustment_order_goods_change_info", autoResultMap = true)
public class PriceAdjustmentOrderGoodsChangeInfoPO extends BasePO {

    /**
     * 调价单表ID
     */
    private Integer priceAdjustmentOrderId;

    /**
     * 调价商品信息表ID
     */
    private Integer priceAdjustmentOrderGoodsId;

    /**
     * 门店商品ID
     */
    private Integer goodsId;

    /**
     * 调价(销售策略)时段 (例如: "00:00～3:00" 或 "全天")
     */
    private String adjustmentTime;

    /**
     * 该时段销售策略原售价
     */
    private BigDecimal salesProgramPrice;

    /**
     * 调价后售价
     */
    private BigDecimal adjustmentPrice;

    /**
     * 商品在该时段销售策略中的售卖名称
     */
    private String goodsSaleName;

    /**
     * 销售策略名称
     */
    private String storeSalesProgramName;

    /**
     * 销售策略表ID
     */
    private Integer storeSalesProgramId;

    /**
     * 销售策略商品信息表ID
     */
    private Integer storeSalesProgramGoodsId;

    /**
     * 销售策略商品时段售价表ID
     */
    private Integer storeSalesProgramGoodsPriceId;

    /**
     * 是否为默认销售策略(全时段)
     * true: 是，false: 否
     */
    private boolean salesProgramIsDefault;

    /**
     * 销售策略开始时间 (例如: "00:00")
     */
    private String salesProgramStartTime;

    /**
     * 销售策略结束时间 (例如: "04:00")
     */
    private String salesProgramEndTime;
}

package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 店铺商品图片
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "store_goods_picture", autoResultMap = true)
public class StoreGoodsPicturePO extends BasePO {

    /**
     * 企业商品ID
     */
    private Integer goodsId;

    /**
     * 图片ID
     */
    private Integer imageId;

    /**
     * 图片顺序
     */
    private Integer sort;

    /**
     * 此图片的类型
     */
    private Integer type;
}

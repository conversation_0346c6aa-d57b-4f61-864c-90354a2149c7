package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Description: 基础PO
 * Author: 向超
 * Date: 2024/11/13 15:47
 */
@Data
public class BasePO implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 该条记录在本数据库创建时间
     */
    private Timestamp createdAt;

    /**
     * 该条记录在本数据库最后修改时间
     */
    private Timestamp updatedAt;

    /**
     * 该条记录是否在本数据库失效
     */
    private Boolean disabled;
}

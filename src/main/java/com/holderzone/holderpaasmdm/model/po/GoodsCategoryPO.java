package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 企业商品分类表
 * Author: 向超
 * Date: 2024/11/27 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_category", autoResultMap = true)
public class GoodsCategoryPO extends BasePO {

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级分类ID
     */
    private Integer parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分类层级
     */
    private Integer level;
}

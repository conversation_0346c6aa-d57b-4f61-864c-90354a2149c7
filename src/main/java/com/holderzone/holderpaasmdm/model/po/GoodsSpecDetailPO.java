package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 商品规格明细表
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_spec_detail", autoResultMap = true)
public class GoodsSpecDetailPO extends BasePO {

    /**
     * 商品规格ID
     */
    @TableField(value = "goods_spec_id")
    private Integer goodsSpecId;

    /**
     * 规格ID
     */
    @TableField(value = "spec_id")
    private Integer specId;

    /**
     * 规格明细ID
     */
    @TableField(value = "spec_detail_id")
    private Integer specDetailId;

    /**
     * 店铺商品封面
     */
    @TableField(value = "img_id")
    private Integer imgId;

    /**
     * 商品排序（排序越小越靠前）
     */
    private Integer sort;
}

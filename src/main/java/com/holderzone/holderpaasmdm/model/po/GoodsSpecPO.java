package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 商品规格表（SPU）
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "goods_spec", autoResultMap = true)
public class GoodsSpecPO extends BasePO {

    /**
     * 企业商品ID
     */
    private Integer goodsId;

    /**
     * 店铺商品ID
     */
    private Integer storeGoodsId;

    /**
     * 规格ID
     *
     * @see SpecPO
     */
    private Integer specId;

    /**
     * 商品排序（排序越小越靠前）
     */
    private Integer sort;
}

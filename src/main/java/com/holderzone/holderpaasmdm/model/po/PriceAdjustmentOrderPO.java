package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 门店商品的调价单
 * Author: 向超
 * Date: 2024/12/17 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "price_adjustment_order", autoResultMap = true)
public class PriceAdjustmentOrderPO extends BasePO {

    /**
     * 门店Id
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 渠道Id
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 调价来源
     * PriceAdjustmentOrderSourceTypeEnum枚举: {1: '移动端', 2: 'POS'}
     */
    private Integer sourceType;

    /**
     * 调价人Id
     */
    private Integer userId;

    /**
     * 调价人名称
     */
    private String username;

    /**
     * 调价人手机号
     */
    private String account;
}

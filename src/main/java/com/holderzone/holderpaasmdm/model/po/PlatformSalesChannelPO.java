package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.holderpaasmdm.common.handler.JsonTypIntegerListHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Description: 销售渠道表
 * Author: 向超
 * Date: 2024/12/05 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "platform_sales_channel_fsgen", autoResultMap = true)
public class PlatformSalesChannelPO extends BasePO {

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道启用状态
     */
    private Boolean isEnable;

    /**
     * 商品同步地址
     */
    private String goodsSyncUrl;

    /**
     * 订单同步地址
     */
    private String orderSyncUrl;

    /**
     * 店铺ID
     */
    @TableField(typeHandler = JsonTypIntegerListHandler.class)
    private List<Integer> storeId;

    /**
     * 渠道类型
     */
    private String channelType;
}
package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 商品计量单位分类
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sale_unit_measurement_category", autoResultMap = true)
public class SaleUnitMeasurementCategoryPO extends BasePO {

    /**
     * 计量单位类别名称
     */
    private String categoryName;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 是否内置数据
     */
    private Boolean isBuiltIn;
}

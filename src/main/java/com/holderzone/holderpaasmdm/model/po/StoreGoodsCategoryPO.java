package com.holderzone.holderpaasmdm.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Description: 店铺商品分类表
 * Author: 向超
 * Date: 2024/11/22 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "store_goods_category", autoResultMap = true)
public class StoreGoodsCategoryPO extends BasePO {

    /**
     * 关联企业商品分类(是否是企业分类同步过来的)，空则是店铺自建
     */
    private Integer originId;

    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级分类ID
     */
    private Integer parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分类层级
     */
    private Integer level;
}

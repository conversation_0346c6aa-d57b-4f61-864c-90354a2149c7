package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 入库单信息VO类
 * Author: 向超
 * Date: 2025/01/16 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InboundBaseVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 代码
     */
    @JsonProperty("code")
    private String code;

    /**
     * 退款编号
     */
    @JsonProperty("refund_number")
    private String refundNumber;

    /**
     * 退款ID
     */
    @JsonProperty("refund_id")
    private String refundId;

    /**
     * 店铺ID
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 店铺名称
     */
    @JsonProperty("store_name")
    private String storeName;

    /**
     * 仓库ID
     */
    @JsonProperty("warehouse_id")
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    @JsonProperty("warehouse_name")
    private String warehouseName;

    /**
     * 来源类型
     */
    @JsonProperty("source_type")
    private String sourceType;

    /**
     * 来源ID
     */
    @JsonProperty("source_id")
    private String sourceId;

    /**
     * 来源名称
     */
    @JsonProperty("source_name")
    private String sourceName;

    /**
     * 文档类型
     */
    @JsonProperty("document_type")
    private Integer documentType;

    /**
     * 数量
     */
    @JsonProperty("quantity")
    private String quantity;

    /**
     * 金额
     */
    @JsonProperty("amount")
    private String amount;

    /**
     * 创建者ID
     */
    @JsonProperty("creator_id")
    private Integer creatorId;

    /**
     * 创建者名称
     */
    @JsonProperty("creator_name")
    private String creatorName;

    /**
     * 文档创建时间
     */
    @JsonProperty("document_creation_time")
    private String documentCreationTime;

    /**
     * 状态
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 审批者ID
     */
    @JsonProperty("approver_id")
    private String approverId;

    /**
     * 审批者名称
     */
    @JsonProperty("approver_name")
    private String approverName;

    /**
     * 审批时间
     */
    @JsonProperty("approval_time")
    private String approvalTime;

    /**
     * 备注
     */
    @JsonProperty("note")
    private String note;

    /**
     * 销售代码
     */
    @JsonProperty("sales_code")
    private String salesCode;
}
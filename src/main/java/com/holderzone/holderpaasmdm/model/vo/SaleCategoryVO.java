package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;


/**
 * Description: 销售分组基础VO
 * Author: 向超
 * Date: 2024/11/26 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaleCategoryVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * id
     */
    @JsonProperty("category_id")
    private List<Integer> categoryId;

    /**
     * 销售分组Name
     */
    @JsonProperty("category_name")
    private String name;

    /**
     * 父级ID
     */
    @JsonProperty("parent_id")
    private Integer parentId;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 封面信息
     */
    @JsonProperty("picture")
    private GoodsPictureVO picture;

    /**
     * 图片id
     */
    private Integer coverId;

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        SaleCategoryVO that = (SaleCategoryVO) obj;
        return Objects.equals(id, that.getId());
    }

}
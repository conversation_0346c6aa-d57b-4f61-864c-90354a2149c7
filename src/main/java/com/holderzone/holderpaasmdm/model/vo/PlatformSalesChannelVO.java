package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Description: 销售渠道表
 * Author: 向超
 * Date: 2024/12/05 15:36
 */
@Data
public class PlatformSalesChannelVO {

    /**
     * 主键ID
     */
    @JsonProperty("id")
    private Integer id;


    /**
     * 渠道名称
     */
    @JsonProperty("channel_name")
    private String channelName;

    /**
     * 渠道启用状态
     */
    @JsonProperty("is_enable")
    private Boolean isEnable;

    /**
     * 商品同步地址
     */
    @JsonProperty("goods_sync_url")
    private String goodsSyncUrl;

    /**
     * 订单同步地址
     */
    @JsonProperty("order_sync_url")
    private String orderSyncUrl;

    /**
     * 店铺ID
     */
    @JsonProperty("store_ids")
    private List<Integer> storeId;

    /**
     * 渠道类型
     */
    @JsonProperty("channel_type")
    private String channelType;
}
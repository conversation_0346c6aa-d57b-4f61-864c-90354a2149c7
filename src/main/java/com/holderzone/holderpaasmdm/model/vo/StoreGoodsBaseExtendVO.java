package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;


/**
 * Description: 门店商品基础扩展vo
 * Author: 向超
 * Date: 2024/12/19 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsBaseExtendVO extends StoreGoodsBaseVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 门店商品单位名称
     */
    @JsonProperty("goods_unit_name")
    private String goodsUnitName;

    /**
     * 标签名称
     */
    @JsonProperty("tag_name")
    private String tagName;

    /**
     * 销售分组列表
     */
    @JsonProperty("category")
    private List<SaleCategoryVO> categoryList;

    /**
     * 商品品牌详情
     */
    @JsonProperty("brand_info")
    private GoodsBrandVO brandInfo;

    /**
     * 秤内码信息集合
     */
    @JsonProperty("goods_scale_code_list")
    private List<GoodsScaleCodeVO> goodsScaleCodeList;

    /**
     * 销售分组
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SaleCategoryVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * id
         */
        private Integer id;

        /**
         * id
         */
        @JsonProperty("category_id")
        private List<Integer> categoryId;

        /**
         * 销售分组Name
         */
        @JsonProperty("name")
        private String name;

        /**
         * 父级ID
         */
        @JsonProperty("parent_id")
        private Integer parentId;

        /**
         * 排序字段
         */
        private Integer sort;

        /**
         * 分类层级
         */
        private Integer level;

        /**
         * 子分类列表
         */
        @JsonProperty("child_list")
        private List<SaleCategoryVO> childList;
    }

    /**
     * 秤内码信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GoodsScaleCodeVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 主键ID
         */
        private Integer id;

        /**
         * 店铺商品ID
         */
        @JsonProperty("goods_id")
        private Integer goodsId;

        /**
         * 店铺ID
         */
        @JsonProperty("store_id")
        private Integer storeId;

        /**
         * 秤内码
         */
        @JsonProperty("plu_code")
        private String pluCode;

        /**
         * 秤内自编码
         */
        @JsonProperty("scale_custom_code")
        private String scaleCustomCode;

        /**
         * 商品传秤，秤的品牌
         */
        @JsonProperty("scale_type")
        private Integer scaleType;

        /**
         * 创建时间
         */
        @JsonProperty("created_at")
        private Timestamp createdAt;

        /**
         * 最后修改时间
         */
        @JsonProperty("updated_at")
        private Timestamp updatedAt;
    }
}
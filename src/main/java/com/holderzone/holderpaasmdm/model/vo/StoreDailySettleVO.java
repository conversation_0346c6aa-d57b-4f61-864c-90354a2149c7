package com.holderzone.holderpaasmdm.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * desc 门店日结信息扩展
 *
 * <AUTHOR>
 * @date 2024/12/18
 * @since 1.8
 */
@Data
public class StoreDailySettleVO {

    /**
     * 门店ID
     */
    private Long storeId;


    /**
     * 营业实收
     */
    private BigDecimal businessActualReceipt = BigDecimal.ZERO;

    /**
     * 销售实付金额
     */
    private BigDecimal salesTotalActualReceipt = BigDecimal.ZERO;

    /**
     * 销售实退金额
     */
    private BigDecimal salesTotalActualRefund = BigDecimal.ZERO;

    /**
     * 销售单数
     */
    private long salesOrderCount = 0;

}

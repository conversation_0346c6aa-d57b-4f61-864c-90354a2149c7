package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Set;


/**
 * Description: 门店可售商品扩展vo （POS端）
 * Author: 向超
 * Date: 2024/12/03 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsAvailableForSalePOSExtendVO extends StoreGoodsBaseVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品在销售方案中的售卖信息,
     * 当组装这个VO时，StoreGoodsBaseVO中的属性就只需要取店铺商品原本的属性即可
     */
    @JsonProperty("store_goods_sales_info")
    private StoreGoodsSalesInfoVO storeGoodsSalesInfoVO;
    /**
     * 商品图片列表
     */
    @JsonProperty("picture")
    private List<GoodsPictureVO> picture;

    /**
     * 店铺销售方案信息
     */
    @JsonProperty("store_sales_program_info")
    private StoreSalesProgramVO storeSalesProgramInfo;

    /**
     * 销售分组ID列表
     */
    @JsonProperty("sale_category_id_list")
    private List<Integer> saleCategoryIdList;

    /**
     * 销售分组信息列表
     */
    @JsonProperty("sale_category_list")
    private Set<SaleCategoryVO> saleCategoryList;

    /**
     * 销售分组
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class StoreGoodsSalesInfoVO implements Serializable {

        /**
         * 商品在该销售方案中的售卖名称
         */
        @JsonProperty("goods_sale_name")
        private String goodsSaleName;

        /**
         * 售卖标签
         */
        private Integer tag;

        /**
         * 封面ID
         */
        @JsonProperty("cover_picture")
        private Integer coverPicture;

        /**
         * 商品封面
         */
        @JsonProperty("cover_picture_info")
        private GoodsPictureVO coverPictureInfo;

        /**
         * 默认售价
         */
        private String sellingPrice;

        /**
         * 门店商品规格详情列表
         */
        @JsonProperty("store_goods_spec_details_list")
        List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList;
    }
}
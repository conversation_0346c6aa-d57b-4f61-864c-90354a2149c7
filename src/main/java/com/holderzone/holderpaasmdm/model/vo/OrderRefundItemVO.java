package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Description: 订单退款商品VO类
 * Author: 向超
 * Date: 2025/01/14 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderRefundItemVO implements Serializable {

    /**
     * 数据ID
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 零售记录id
     */
    @JsonProperty("record_id")
    private Integer recordId;

    /**
     * 商品goodsId
     */
    @JsonProperty("goods_id")
    private Integer goodsId;

    /**
     * 商品售卖名称
     */
    @JsonProperty("goods_sale_name")
    private String goodsSaleName;

    /**
     * 条码
     */
    @JsonProperty("barcode")
    private String barcode;

    /**
     * 单位id
     */
    @JsonProperty("goods_unit_id")
    private Integer goodsUnitId;

    /**
     * 单位名称
     */
    @JsonProperty("goods_unit_name")
    private String goodsUnitName;

    /**
     * 购买数量
     */
    @JsonProperty("purchase_quantity")
    private String purchaseQuantity;

    /**
     * 商品实收金额（销售金额）
     */
    @JsonProperty("actual_receive_price")
    private String actualReceivePrice;

    /**
     * 订单退款id
     */
    @JsonProperty("order_refund_id")
    private Integer orderRefundId;

    /**
     * 订单号
     */
    @JsonProperty("order_number")
    private String orderNumber;

    /**
     * 退款号
     */
    @JsonProperty("refund_number")
    private String refundNumber;

    /**
     * 企业id
     */
    @JsonProperty("company_id")
    private Integer companyId;

    /**
     * 订单商品id
     */
    @JsonProperty("order_item_id")
    private Integer orderItemId;

    /**
     * 退款数量
     */
    @JsonProperty("refund_quantity")
    private String refundQuantity;

    /**
     * 商品小计
     */
    @JsonProperty("refund_origin_price")
    private String refundOriginPrice;

    /**
     * 商品退款价格
     */
    @JsonProperty("refund_price")
    private String refundPrice;

    /**
     * 商品实际退款金额
     */
    @JsonProperty("actually_refund_item_amount")
    private String actuallyRefundItemAmount;

    /**
     * 是否退成功
     */
    @JsonProperty("is_refund_success")
    private Boolean isRefundSuccess;

    /**
     * 商品原价若存在改价则这里是改后价
     */
    @JsonProperty("selling_price")
    private String sellingPrice;

    /**
     * 商品图片URL
     */
    @JsonProperty("picture_url")
    private String pictureUrl;

    /**
     * 零售创建时间
     */
    @JsonProperty("create_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    @JsonProperty("update_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateAt;
}

package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 店铺商品库存对象
 *
 * <AUTHOR>
 * @date 2025/6/17 11:09
 **/
@Data
public class StoreGoodsSkuInventoryVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 店铺商品id
     */
    @JsonProperty("store_goods_id")
    private Integer storeGoodsId;

    /**
     * SKU编码
     */
    @JsonProperty("sku_code")
    private String skuCode;

    /**
     * SKUId
     * 不对外展示
     */
    @JsonProperty("goods_package_sku_id")
    private Integer skuId;

    /**
     * 渠道库存
     */
    @JsonProperty("inventory")
    private Integer inventory;
}

package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * Description: 门店在售商品扩展vo
 * Author: 向超
 * Date: 2024/11/26 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsOnSaleExtendVO extends StoreGoodsBaseVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 店铺销售方案ID
     */
    @JsonProperty("store_sales_program_id")
    private Integer storeSalesProgramId;

    /**
     * 标签名称
     */
    @JsonProperty("tag_name")
    private String tagName;

    /**
     * 销售分组列表
     */
    @JsonProperty("category")
    private List<SaleCategoryVO> categoryList;

    /**
     * 商品图片列表
     */
    @JsonProperty("picture")
    private List<GoodsPictureVO> picture;

    /**
     * 商品品牌详情
     */
    @JsonProperty("brand_info")
    private GoodsBrandVO brandInfo;

    /**
     * 门店商品单位名称
     */
    @JsonProperty("goods_unit_name")
    private String goodsUnitName;

    /**
     * 商品标签详情
     */
    @JsonProperty("label_info")
    private GoodsLabelVO labelInfo;

    /**
     * 门店商品规格详情列表
     */
    @JsonProperty("store_goods_spec_details_list")
    List<StoreGoodsSpecAndDetailsVO> storeGoodsSpecDetailsList;

    /**
     * 门店商品规格全量列表
     */
    @JsonProperty("store_goods_spec_list")
    List<StoreGoodsSpecVO> storeGoodsSpecList;

    /**
     * 秤内自编码
     */
    @JsonProperty("scale_custom_code")
    private String scaleCustomCode;

    /**
     * 商品SKU ID
     */
    @JsonProperty("goods_package_sku_id")
    private Integer goodsPackageSkuId;

    /**
     * 销售分组
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SaleCategoryVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * id
         */
        private Integer id;

        /**
         * id
         */
        @JsonProperty("category_id")
        private List<Integer> categoryId;

        /**
         * 销售分组Name
         */
        @JsonProperty("name")
        private String name;

        /**
         * 父级ID
         */
        @JsonProperty("parent_id")
        private Integer parentId;

        /**
         * 排序字段
         */
        private Integer sort;

        /**
         * 分类层级
         */
        private Integer level;
    }
}
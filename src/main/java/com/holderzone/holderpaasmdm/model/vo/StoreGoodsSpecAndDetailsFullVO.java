package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;


/**
 * Description: 门店商品规格和规格明细vo
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsSpecAndDetailsFullVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品规格关系列表
     */
    @JsonProperty("goods_spec_relation_list")
    private List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList;

    /**
     * 商品该规格SKU对应的成本
     */
    @JsonProperty("costs")
    private String costs;

    /**
     * SKU编码
     */
    @JsonProperty("sku_code")
    private String skuCode;

    /**
     * SKUId
     * 不对外展示
     */
    @JsonProperty("goods_package_sku_id")
    private Integer skuId;

    /**
     * 店铺销售方案商品ID
     */
    @JsonProperty("store_sales_program_goods_id")
    private Integer storeSalesProgramGoodsId;

    /**
     * 门店商品单位名称
     */
    @JsonProperty("goods_unit_name")
    private String goodsUnitName;

    /**
     * 门店商品单位
     */
    @JsonProperty("goods_unit")
    private Integer goodsUnit;

    /**
     * 门店商品条码
     */
    @JsonProperty("barcode")
    private String barcode;

    /**
     * 划线价
     */
    @JsonProperty("line_price")
    private BigDecimal linePrice;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 是否限购
     */
    @JsonProperty("is_limit_buy")
    private Boolean isLimitBuy;

    /**
     * 划线价
     */
    @JsonProperty("limit_buy_num")
    private Integer limitBuyNum;

    /**
     * 线上初始销量
     */
    @JsonProperty("online_init_sales")
    private Integer onlineInitSales;

    /**
     * 商品规格值名称
     */
    @JsonProperty("goods_spec_detail_image")
    private GoodsPictureVO image;

    /**
     * 渠道库存
     */
    @JsonProperty("inventory")
    private Integer inventory;

    /**
     * 销售方案时段
     */
    @JsonProperty("section_list")
    private List<SectionVO> sectionList;

    /**
     * Description: 店铺销售方案时间段VO
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SectionVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 序号
         */
        @JsonProperty("serial_number")
        private Integer serialNumber;
        /**
         * 开始时刻
         * 02:00
         */
        private LocalTime start;
        /**
         * 结束时刻
         * 06:00
         */
        private LocalTime end;

        /**
         * 门店商品时段售价
         */
        @JsonProperty("selling_price")
        private BigDecimal sellingPrice;

        /**
         * 门店商品时段售价的Id
         */
        @JsonProperty("store_sales_program_goods_price_id")
        private Integer storeSalesProgramGoodsPriceId;
    }
}
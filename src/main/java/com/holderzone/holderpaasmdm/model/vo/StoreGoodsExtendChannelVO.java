package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品扩展对外渠道对象
 *
 * <AUTHOR>
 * @date 2025/6/9 14:24
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsExtendChannelVO extends StoreGoodsExtendVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品渠道信息扩展
     */
    @JsonProperty("goods_channel_external")
    private GoodsChannelExternalVO goodsChannelExternal;

    /**
     * 平台分类数据
     */
    private CategoryLevelVO categoryPlatforms;

    /**
     * 销售分类数据
     */
    private CategoryLevelVO saleCategories;

    /**
     * 其他图片集合
     */
    @JsonProperty("other_pictures")
    private Map<Integer, List<GoodsPictureVO>> otherPictures;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CategoryLevelVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 第一层
         */
        private IdNameVO one;

        /**
         * 第二层
         */
        private IdNameVO two;

        /**
         * 第三层
         */
        private IdNameVO three;
    }

    /**
     * id和名称对象
     */
    @Data
    @AllArgsConstructor
    public static class IdNameVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Integer id;
        private String name;
    }
}

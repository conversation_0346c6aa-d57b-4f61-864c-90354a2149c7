package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;


/**
 * Description: 门店商品扩展(打印标签、价签类的扩展类)vo
 * Author: 向超
 * Date: 2024/12/19 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsPrintLabelExtendVO extends StoreGoodsBaseExtendVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售方案及商品信息集合
     */
    @JsonProperty("store_sales_program_goods_list")
    private List<StoreSalesProgramGoodVO> storeSalesProgramGoodList;

    /**
     * 销售方案及商品信息(以时段为维度组装，一个时段一个对象）
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class StoreSalesProgramGoodVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 店铺销售方案商品ID
         */
        @JsonProperty("store_sales_program_goods_id")
        private Integer storeSalesProgramGoodsId;

        /**
         * 店铺销售方案商品名称
         */
        @JsonProperty("store_sales_program_goods_name")
        private String storeSalesProgramGoodsName;

        /**
         * 店铺销售方案ID
         */
        @JsonProperty("store_sales_program_id")
        private Integer storeSalesProgramId;

        /**
         * 店铺销售方案名称
         */
        @JsonProperty("store_sales_program_name")
        private String storeSalesProgramName;

        /**
         * 是否为默认销售策略(全时段), true: 是, false: 否
         */
        @JsonProperty("sales_program_is_default")
        private Boolean salesProgramIsDefault;

        /**
         * 店铺销售方案商品标签
         */
        private Integer label;

        /**
         * 商品标签详情
         */
        @JsonProperty("label_info")
        private GoodsLabelVO labelInfo;

        /**
         * 店铺销售方案商品封面
         */
        @JsonProperty("cover_picture")
        private Integer coverPicture;

        /**
         * 商品图片列表
         */
        @JsonProperty("picture")
        private List<GoodsPictureVO> picture;

        /**
         * 时段序号
         */
        @JsonProperty("serial_number")
        private Integer serialNumber;
        /**
         * 开始时刻
         * 02:00
         */
        private LocalTime start;
        /**
         * 结束时刻
         * 06:00
         */
        private LocalTime end;

        /**
         * 门店商品时段售价
         */
        @JsonProperty("selling_price")
        private String sellingPrice;
    }

    /**
     * 店铺销售方案商品ID
     */
    @JsonProperty("store_sales_program_goods_id")
    private Integer storeSalesProgramGoodsId;

    /**
     * 店铺销售方案商品名称
     */
    @JsonProperty("store_sales_program_goods_name")
    private String storeSalesProgramGoodsName;

    /**
     * 店铺销售方案ID
     */
    @JsonProperty("store_sales_program_id")
    private Integer storeSalesProgramId;

    /**
     * 店铺销售方案名称
     */
    @JsonProperty("store_sales_program_name")
    private String storeSalesProgramName;

    /**
     * 是否为默认销售策略(全时段), true: 是, false: 否
     */
    @JsonProperty("sales_program_is_default")
    private Boolean salesProgramIsDefault;

    /**
     * 店铺销售方案商品标签
     */
    private Integer label;

    /**
     * 商品标签详情
     */
    @JsonProperty("label_info")
    private GoodsLabelVO labelInfo;

    /**
     * 店铺销售方案商品封面
     */
    @JsonProperty("cover_picture")
    private Integer coverPicture;

    /**
     * 商品图片列表
     */
    @JsonProperty("picture")
    private List<GoodsPictureVO> picture;

    /**
     * 时段序号
     */
    @JsonProperty("serial_number")
    private Integer serialNumber;
    /**
     * 开始时刻
     * 02:00
     */
    private LocalTime start;
    /**
     * 结束时刻
     * 06:00
     */
    private LocalTime end;

    /**
     * 门店商品时段售价
     */
    @JsonProperty("selling_price")
    private String sellingPrice;

    /**
     * 套餐skuId
     */
    @JsonProperty("goods_package_sku_id")
    private Integer goodsPackageSkuId;

    /**
     * 套餐商品价格关联表id
     */
    @JsonProperty("store_sales_program_goods_priceId")
    private Integer storeSalesProgramGoodsPriceId;

    /**
     * 规格名称
     */
    @JsonProperty("spec_name")
    private String specName;
}
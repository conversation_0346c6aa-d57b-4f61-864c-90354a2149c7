package com.holderzone.holderpaasmdm.model.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.holderzone.holderpaasmdm.model.dto.PageDTO;
import com.holderzone.holderpaasmdm.model.po.BasePO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * Description: 分页响应VO
 * Author: 向超
 * Date: 2024/4/9 15:33
 */
@Getter
@Setter
@NoArgsConstructor
public class PageRespVO<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    private Integer total;
    /**
     * 每页记录数
     */
    private Integer pageSize;
    /**
     * 总页数
     */
    private Integer totalPage;
    /**
     * 当前页数
     */
    private Integer pageNum;
    /**
     * 列表数据
     */
    private List<T> list;

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     * @param pageSize 每页记录数
     * @param pageNum 当前页数
     * @param totalPage 总页数
     */
    public PageRespVO(List<T> list, Integer total, Integer pageSize, Integer pageNum, Integer totalPage) {
        this.list = list;
        this.total = total;
        this.pageSize = pageSize;
        this.pageNum = pageNum;
        this.totalPage = totalPage;
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     * @param pageSize 每页记录数
     * @param pageNum 当前页数
     */
    public PageRespVO(List<T> list, Integer total, Integer pageSize, Integer pageNum) {
        this.list = list;
        this.total = total;
        this.pageSize = pageSize;
        this.pageNum = pageNum;
        this.totalPage = (int)Math.ceil((double) total / pageSize);
    }

    /**
     * 分页
     *
     * @param page mybatis-plus 分页对象
     */
    public PageRespVO(IPage<T> page) {
        this.list = page.getRecords();
        this.total = Math.toIntExact(page.getTotal());
        this.pageSize = Math.toIntExact(page.getSize());
        this.pageNum = Math.toIntExact(page.getCurrent());
        this.totalPage = Math.toIntExact(page.getPages());
    }

    /**
     * 分页响应VO构造器，方便通过MP Page构造PageRespVO
     *
     * @param page 通过mybatis-plus查询得到的Page对象
     * @param list VO列表数据
     * @param <P> PO数据类型
     */
    public <P extends BasePO> PageRespVO(IPage<P> page, List<T> list) {
        this.total = Math.toIntExact(page.getTotal());
        this.pageSize = Math.toIntExact(page.getSize());
        this.pageNum = Math.toIntExact(page.getCurrent());
        this.totalPage = Math.toIntExact(page.getPages());
        this.list = list;
    }

    /**
     * 手动分页
     *
     * @param list 全部列表数据
     * @param pageSize 每页记录数
     * @param pageNum 当前页数
     */
    public PageRespVO(List<T> list, int pageSize, int pageNum) {
        this.total = list.size();
        this.pageSize = pageSize;
        this.pageNum = pageNum;
        this.totalPage = (int)Math.ceil((double) total / pageSize);
        this.list = list;
        if (CollectionUtils.isNotEmpty(list)) {
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            // 分页超过总数返回 空集合
            if (startIndex > total) {
                this.list = List.of();
                return;
            }
            this.list = list.subList(startIndex, endIndex);
        }
    }

    /**
     * 空数据分页结构
     *
     * @param queryDTO 分页配置
     */
    public PageRespVO(PageDTO queryDTO) {
        this.total = 0;
        this.pageSize = queryDTO.getLimit();
        this.pageNum = queryDTO.getPage();
        this.totalPage = (int)Math.ceil((double) total / pageSize);
        this.list = Collections.emptyList();
    }
}

package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/6/6 17:58
 **/
@Data
public class StoreChannelBindVO {

    /**
     * 门店ID
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 渠道ID
     */
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 绑定第三方店铺ID
     */
    @JsonProperty("bind_store_id")
    private Long bindStoreId;

    /**
     * 绑定第三方店铺名称
     */
    @JsonProperty("bind_store_name")
    private String bindStoreName;

    /**
     * 绑定类型 1. 私域商城 业务枚举 StoreChannelBindEnum
     */
    @JsonProperty("bind_type")
    private Integer bindType;
}

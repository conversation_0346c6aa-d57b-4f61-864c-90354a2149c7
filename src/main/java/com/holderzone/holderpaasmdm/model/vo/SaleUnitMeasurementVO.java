package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 单位VO
 * Author: 向超
 * Date: 2025/02/06 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaleUnitMeasurementVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 计量单位名称
     */
    @JsonProperty("unit_name")
    private String unitName;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 计量单位类别ID
     */
    @JsonProperty("category_id")
    private Integer categoryId;

    /**
     * 商品单位类别枚举，1：计重，2：其他
     * 新建单位默认为其他
     */
    @JsonProperty("category_enum")
    private Integer categoryEnum;

    /**
     * 计量单位类别名称
     */
    @JsonProperty("category_name")
    private String categoryName;

}
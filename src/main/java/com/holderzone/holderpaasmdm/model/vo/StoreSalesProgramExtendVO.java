package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 店铺销售方案扩展VO
 * Author: 向超
 * Date: 2024/12/04 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreSalesProgramExtendVO extends StoreSalesProgramVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售方案是否生效状态：true-生效，false-未生效
     * 默认值：false
     */
    @JsonProperty("is_active")
    private Boolean isActive = false;
}
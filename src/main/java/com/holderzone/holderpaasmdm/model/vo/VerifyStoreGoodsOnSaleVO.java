package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsChangeReminderType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Description: 快速结账 - 校验商品状态vo
 * Author: 向超
 * Date: 2024/11/29 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VerifyStoreGoodsOnSaleVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonProperty("goods_change_reminder")
    private List<GoodsChangeReminderInfo> goodsChangeReminder;

    /**
     * 主键ID
     */
    @JsonProperty("goods_list")
    private List<StoreGoodsExtendInfo> goodsList;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GoodsChangeReminderInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 变更类型
         * See: {@link StoreGoodsChangeReminderType}
         * 商品的售卖价格: selling_price
         * 商品上下架状态: listing_status
         */
        @JsonProperty("change_type")
        private String changeType;

        /**
         * 已变更的商品列表信息
         */
        @JsonProperty("goods_list")
        private List<StoreGoodsBaseInfo> goodsList;
    }

    /**
     * 店铺商品基础信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class StoreGoodsBaseInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 店铺销售方案商品ID
         */
        @JsonProperty("store_sales_program_goods_id")
        private Integer storeSalesProgramGoodsId;

        /**
         * 商品规格关系
         */
        @JsonProperty("goods_spec_relation_list")
        private List<GoodsSpecRelation> goodsSpecRelationList;

        /**
         * 店铺商品ID
         */
        @JsonProperty("goods_id")
        private Integer goodsId;

        /**
         * 商品销售名称
         */
        @JsonProperty("goods_sale_name")
        private String goodsName;

        /**
         * 商品销售价格
         */
        @JsonProperty("selling_price")
        private String sellingPrice;

        /**
         * 数量
         */
        @JsonProperty("quantity")
        private String quantity;

        /**
         * 商品单位名称
         */
        @JsonProperty("goods_unit_name")
        private String goodsUnitName;

        /**
         * 商品条码
         */
        @JsonProperty("barcode")
        private String barcode;

        /**
         * 商品SKUCode
         */
        @JsonProperty("sku_code")
        private String skuCode;

        /**
         * 商品SPUCode
         */
        @JsonProperty("spu_code")
        private String spuCode;
    }

    /**
     * 店铺商品扩展信息
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class StoreGoodsExtendInfo extends StoreGoodsBaseInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 店铺销售方案ID
         */
        @JsonProperty("store_sales_program_id")
        private Integer storeSalesProgramId;

        /**
         * 封面信息
         */
        @JsonProperty("picture")
        private List<GoodsPictureVO> picture;

        /**
         * 自编码
         */
        @JsonProperty("goods_custom_code")
        private String goodsCustomCode;

        /**
         * 评估方式
         */
        @JsonProperty("valuation_method")
        private Integer valuationMethod;

        /**
         * 组合类型（单品/套餐）
         * 1：单品，2：套餐
         */
        @JsonProperty("combo_type")
        private Integer comboType;

        /**
         * 组合类型（单品/套餐） 名称
         * 1：单品，2：套餐
         */
        @JsonProperty("combo_type_name")
        private String comboTypeName;

        /**
         * 组合类型（单品/套餐）
         * 1：单品，2：套餐
         */
        @JsonProperty("spu_code")
        private String spuCode;

        /**
         * 店铺商品单位ID
         */
        @JsonProperty("goods_unit")
        private Integer goodsUnit;

        /**
         * 店铺商品单位名称
         */
        @JsonProperty("goods_unit_name")
        private String goodsUnitName;

        /**
         * 店铺商品规格
         */
        @JsonProperty("goods_specification")
        private String goodsSpecification;

        /**
         * 店铺商品属性
         */
        @JsonProperty("goods_property")
        private String goodsProperty;

        /**
         * 店铺商品标签
         */
        @JsonProperty("tag_name")
        private String tagName;
    }

    /**
     * 商品规格关系
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GoodsSpecRelation implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 商品规格ID
         */
        @JsonProperty("goods_spec_id")
        private Integer goodsSpecId;

        /**
         * 商品规格名称
         */
        @JsonProperty("goods_spec_name")
        private String goodsSpecName;

        /**
         * 商品规格排序
         */
        @JsonProperty("goods_spec_sort")
        private Integer goodsSpecSort;

        /**
         * 商品规格值ID
         */
        @JsonProperty("goods_spec_detail_id")
        private Integer goodsSpecDetailId;

        /**
         * 商品规格值名称
         */
        @JsonProperty("goods_spec_detail_name")
        private String goodsSpecDetailName;

        /**
         * 商品规格值排序
         */
        @JsonProperty("goods_spec_detail_sort")
        private Integer goodsSpecDetailSort;
    }
}
package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * Description: 商品分类VO
 * Author: 向超
 * Date: 2025/02/08 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsCategoryVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 父级ID
     */
    @JsonProperty("parent_id")
    private Integer parentId;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 销售分组Name
     */
    @JsonProperty("category_name")
    private String name;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 关联企业商品分类，空则是自建
     */
    @JsonProperty("origin_id")
    private Integer originId;

    /**
     * 店铺ID
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 商品分类子级列表
     */
    private List<StoreGoodsCategoryVO> children;
}
package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Description: 传秤校验商品状态vo
 * Author: 向超
 * Date: 2024/12/13 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VerifyStoreGoodsAvailableForSaleScaleCodeVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 异常数据列表，列表元素格式为：{”error_type“: "", "goods_list": []}
     * 没有错误数据时为空列表
     */
    @JsonProperty("error_goods_list")
    private List<GoodsChangeReminderInfo> errorGoodsList;
    /**
     * 正常商品数据列表
     */
    @JsonProperty("goods_list")
    private List<StoreGoodsExtendVO> goodsList;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GoodsChangeReminderInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 错误类型
         * See: {@link com.holderzone.holderpaasmdm.enumeraton.ScaleCodeChangeReminderType}
         * 商品计价方式变更: valuation_error
         * 秤内自编码变更: custom_code_error
         * 秤内码变更: plu_code_error
         */
        @JsonProperty("error_type")
        private String errorType;

        /**
         * 已变更的商品列表信息
         */
        @JsonProperty("goods_list")
        private List<StoreGoodsExtendVO> goodsList;
    }

}
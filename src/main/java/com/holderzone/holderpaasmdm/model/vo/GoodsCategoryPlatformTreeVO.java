package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 平台类目分组树对象
 *
 * <AUTHOR>
 * @date 2025/5/30 14:35
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsCategoryPlatformTreeVO extends GoodsCategoryPlatformVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售分组商品数量
     */
    @JsonProperty("goods_count")
    private Integer goodsCount;

    /**
     * 销售分组子节点
     */
    private List<GoodsCategoryPlatformTreeVO> children;

    /**
     * 封面信息
     */
    @JsonProperty("picture")
    private GoodsPictureVO picture;
}

package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Description: 订单退款折扣VO类
 * Author: 向超
 * Date: 2025/01/14 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderRefundDiscountVO implements Serializable {

    /**
     * 数据ID
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 零售记录id
     */
    @JsonProperty("record_id")
    private Integer recordId;

    /**
     * 订单退款id
     */
    @JsonProperty("order_refund_id")
    private Integer orderRefundId;

    /**
     * 订单号
     */
    @JsonProperty("order_number")
    private String orderNumber;

    /**
     * 退款号
     */
    @JsonProperty("refund_number")
    private String refundNumber;

    /**
     * 订单优惠id
     */
    @JsonProperty("order_discount_id")
    private Integer orderDiscountId;

    /**
     * 企业id
     */
    @JsonProperty("company_id")
    private Integer companyId;

    /**
     * 优惠金额
     */
    @JsonProperty("discount_amount")
    private String discountAmount;

    /**
     * 优惠名称
     */
    @JsonProperty("discount_name")
    private String discountName;

    /**
     * 是否退成功
     */
    @JsonProperty("is_refund_success")
    private Boolean isRefundSuccess;

    /**
     * 零售创建时间
     */
    @JsonProperty("create_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    @JsonProperty("update_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateAt;
}

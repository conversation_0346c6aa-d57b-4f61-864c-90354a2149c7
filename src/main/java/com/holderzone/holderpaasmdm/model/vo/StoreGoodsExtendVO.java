package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;


/**
 * Description: 门店商品扩展vo
 * Author: 向超
 * Date: 2024/12/11 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsExtendVO extends StoreGoodsBaseExtendVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品图片列表
     */
    @JsonProperty("picture")
    private List<GoodsPictureVO> picture;

    /**
     * 商品标签详情
     */
    @JsonProperty("label_info")
    private GoodsLabelVO labelInfo;

    /**
     * 店铺销售方案商品ID
     */
    @JsonProperty("store_sales_program_goods_id")
    private Integer storeSalesProgramGoodsId;

    /**
     * 店铺销售方案ID
     */
    @JsonProperty("store_sales_program_id")
    private Integer storeSalesProgramId;

    /**
     * 店铺销售方案名称
     */
    @JsonProperty("store_sales_program_name")
    private String storeSalesProgramName;

    /**
     * 建议售价
     */
    @JsonProperty("suggested_selling_price")
    private BigDecimal suggestedSellingPrice;

    /**
     * 门店商品规格详情列表
     */
    @JsonProperty("store_goods_spec_details_list")
    List<StoreGoodsSpecAndDetailsFullVO> storeGoodsSpecDetailsList;

    /**
     * 商品分类树列表
     */
    @JsonProperty("store_goods_category_list")
    private List<StoreGoodsCategoryVO> storeGoodsCategoryList;

    /**
     * 时段信息集合
     */
    @JsonProperty("section_list")
    private List<SectionVO> sectionList;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SectionVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 序号
         */
        @JsonProperty("serial_number")
        private Integer serialNumber;
        /**
         * 开始时刻
         * 02:00
         */
        private LocalTime start;
        /**
         * 结束时刻
         * 06:00
         */
        private LocalTime end;

        /**
         * 门店商品时段售价
         */
        @JsonProperty("selling_price")
        private BigDecimal sellingPrice;
    }
}
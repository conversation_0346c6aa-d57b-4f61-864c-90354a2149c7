package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * Description: 门店商品规格和规格明细vo
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsSpecAndDetailsVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品规格关系列表
     */
    @JsonProperty("goods_spec_relation_list")
    private List<StoreGoodsSpecRelationVO> storeGoodsSpecRelationList;

    /**
     * 商品该规格SKU对应销售方案中的售价
     */
    @JsonProperty("goods_spec_selling_price")
    private String goodsSpecSellingPrice;

    /**
     * 商品该规格SKU对应的成本
     */
    @JsonProperty("costs")
    private String costs;

    /**
     * SKU编码
     */
    @JsonProperty("sku_code")
    private String skuCode;

    /**
     * SKUId
     * 不对外展示
     */
    @JsonProperty("goods_package_sku_id")
    private Integer skuId;

    /**
     * 店铺销售方案商品ID
     */
    @JsonProperty("store_sales_program_goods_id")
    private Integer storeSalesProgramGoodsId;

    /**
     * 门店商品单位名称
     */
    @JsonProperty("goods_unit_name")
    private String goodsUnitName;

    /**
     * 门店商品单位
     */
    @JsonProperty("goods_unit")
    private Integer goodsUnit;

    /**
     * 门店商品条码
     */
    @JsonProperty("barcode")
    private String barcode;
}
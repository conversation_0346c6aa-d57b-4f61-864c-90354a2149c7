package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 商品渠道信息扩展表
 *
 * <AUTHOR>
 * @date 2025/6/9 11:24
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsChannelExternalVO {

    /**
     * 门店商品id
     */
    @JsonProperty("store_goods_id")
    private Integer storeGoodsId;

    /**
     * 商品描述
     */
    @JsonProperty("goods_desc")
    private String goodsDesc;

    /**
     * 视频id
     */
    @JsonProperty("video_id")
    private Integer videoId;

    /**
     * 视频资源数据
     */
    private GoodsPictureVO video;

    /**
     * 是否开通同城配送
     */
    @JsonProperty("is_city_delivery")
    private Boolean isCityDelivery;

    /**
     * 是否开通到点自取
     */
    @JsonProperty("is_self_pickup")
    private Boolean isSelfPickup;

    /**
     * 运费模板ID
     */
    @JsonProperty("freight_template_id")
    private Long freightTemplateId;

    /**
     * 服务保障 业务枚举是ServiceGuaranteeEnum
     * 数组形式保存
     * 1:包邮
     * 2:7天退换
     * 3:48小时发货
     * 4:假一赔十
     * 5:正品保障
     */
    @JsonProperty("service_guarantee")
    private String serviceGuarantee;

    /**
     * 是否开通快速发货
     */
    @JsonProperty("is_express_delivery")
    private Boolean isExpressDelivery;
}

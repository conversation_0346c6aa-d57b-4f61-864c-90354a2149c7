package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Description: 订单退款VO类
 * Author: 向超
 * Date: 2025/01/14 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderRefundVO implements Serializable {

    /**
     * 数据ID
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 零售记录id
     */
    @JsonProperty("record_id")
    private Long recordId;

    /**
     * 销售订单id
     */
    @JsonProperty("order_id")
    private Long orderId;

    /**
     * 订单号
     */
    @JsonProperty("order_number")
    private String orderNumber;

    /**
     * 退款号
     */
    @JsonProperty("refund_number")
    private String refundNumber;

    /**
     * 退款金额
     */
    @JsonProperty("refund_amount")
    private String refundAmount;

    /**
     * 实际退款金额
     */
    @JsonProperty("actually_refund_amount")
    private String actuallyRefundAmount;

    /**
     * 企业id
     */
    @JsonProperty("company_id")
    private Integer companyId;

    /**
     * 操作员id
     */
    @JsonProperty("operater_id")
    private Integer operaterId;

    /**
     * 操作员名称
     */
    @JsonProperty("operater_name")
    private String operaterName;

    /**
     * 操作员手机号
     */
    @JsonProperty("operater_phone")
    private String operaterPhone;

    /**
     * 操作退款时设备的门店id，注意和订单的门店id不一定一样
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 退款原因
     */
    @JsonProperty("refund_reason")
    private String refundReason;

    /**
     * 退款类型别名
     */
    @JsonProperty("refund_type_alias")
    private String refundTypeAlias;

    /**
     * 下单设备的设备编号
     */
    @JsonProperty("device_number")
    private String deviceNumber;

    /**
     * 订单来源
     */
    @JsonProperty("order_source")
    private String orderSource;

    /**
     * 退款类型[1、整单退款；2、部分退款；3、现金退款；4、部分现金退款]
     */
    @JsonProperty("refund_type")
    private Integer refundType;

    /**
     * 退款支付方式，仅在部分退款中作为标记[0、自定义；1、现金支付；2、会员支付；3、聚合支付]
     */
    @JsonProperty("refund_payment_method")
    private Integer refundPaymentMethod;

    /**
     * 零售创建时间
     */
    @JsonProperty("create_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    @JsonProperty("update_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateAt;
}

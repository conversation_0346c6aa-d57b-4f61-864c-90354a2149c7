package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 商品规格值基础vo
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsSpecDetailVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品规格值ID
     */
    @JsonProperty("goods_spec_detail_id")
    private Integer id;

    /**
     * 商品规格值名称
     */
    @JsonProperty("goods_spec_detail_name")
    private String specDetailName;

    /**
     * 商品规格值排序
     */
    @JsonProperty("goods_spec_detail_sort")
    private Integer specDetailSort;

    /**
     * 商品规格值图片
     */
    @JsonProperty("goods_spec_detail_image")
    private GoodsPictureVO image;

    /**
     * 商品规格值图片Id,不返前端
     */
    @JsonIgnore
    private Integer imgId;
}
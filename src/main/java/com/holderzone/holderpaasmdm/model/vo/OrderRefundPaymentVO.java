package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Description: 订单退款支付VO类
 * Author: 向超
 * Date: 2025/01/14 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderRefundPaymentVO implements Serializable {

    /**
     * 数据ID
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 零售记录id
     */
    @JsonProperty("record_id")
    private Integer recordId;

    /**
     * 订单退款id
     */
    @JsonProperty("order_refund_id")
    private Integer orderRefundId;

    /**
     * 订单号
     */
    @JsonProperty("order_number")
    private String orderNumber;

    /**
     * 退款号
     */
    @JsonProperty("refund_number")
    private String refundNumber;

    /**
     * 企业id
     */
    @JsonProperty("company_id")
    private Integer companyId;

    /**
     * 支付方式类型[0、自定义；1、现金支付；2、会员支付；3、聚合支付]
     */
    @JsonProperty("refund_payment_type")
    private Integer refundPaymentType;

    /**
     * 支付平台返回的退款ID
     */
    @JsonProperty("refund_payment_id")
    private String refundPaymentId;

    /**
     * 退款的支付方式id
     */
    @JsonProperty("refund_payment_method_id")
    private Integer refundPaymentMethodId;

    /**
     * 退款支付方式
     */
    @JsonProperty("refund_payment_name")
    private String refundPaymentName;

    /**
     * 退款支付号
     */
    @JsonProperty("refund_payment_number")
    private String refundPaymentNumber;

    /**
     * 退款支付金额
     */
    @JsonProperty("refund_payment_amount")
    private String refundPaymentAmount;

    /**
     * 是否退成功
     */
    @JsonProperty("is_refund_success")
    private Boolean isRefundSuccess;

    /**
     * 是否预退款成功
     */
    @JsonProperty("is_pre_refund_success")
    private Boolean isPreRefundSuccess;

    /**
     * 退款成功时间
     */
    @JsonProperty("refund_success_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp refundSuccessTime;

    /**
     * 工具字段-退款成功时间开始
     */
    @JsonProperty("refund_success_time_start")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp refundSuccessTimeStart;

    /**
     * 工具字段-退款成功时间结束
     */
    @JsonProperty("refund_success_time_end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp refundSuccessTimeEnd;

    /**
     * 零售创建时间
     */
    @JsonProperty("create_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createAt;

    /**
     * 零售更新时间
     */
    @JsonProperty("update_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateAt;

    /**
     * 支付方式排序
     */
    @JsonProperty("sort")
    private Integer sort;
}

package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * desc 门店信息响应VO
 * <AUTHOR>
 * @date 2024/12/23
 */
@Data
public class StoreInfoDetailsVO {

    /**
     * 零售门店id
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 门店名称
     */
    @JsonProperty("store_name")
    private String storeName;

    /**
     * 渠道id
     */
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 部门id
     */
    @JsonProperty("store_team_info_id")
    private Integer storeTeamInfoId;

    /**
     * 一体化平台id
     */
    @JsonProperty("holder_store_id")
    private String holderStoreId;

    /**
     *
     * 支付id
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 支付密钥
     */
    @JsonProperty("app_secret")
    private String appSecret;

}























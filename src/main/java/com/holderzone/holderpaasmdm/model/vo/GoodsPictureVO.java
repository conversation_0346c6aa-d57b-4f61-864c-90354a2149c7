package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 商品图片VO
 * Author: 向超
 * Date: 2024/11/26 15:36
 */
@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsPictureVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 文件名称
     */
    private String filename;

    /**
     * 图片URL
     */
    private String url;

    /**
     * 预览地址
     */
    private String previewUrl;

    /**
     * 图片顺序
     */
    private Integer sort;
}
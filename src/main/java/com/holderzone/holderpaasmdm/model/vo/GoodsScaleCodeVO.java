package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 店铺商品与秤内码的对应关系
 * Author: 向超
 * Date: 2024/12/12 15:36
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class GoodsScaleCodeVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 店铺商品ID
     */
    @JsonProperty("goods_id")
    private Integer goodsId;

    /**
     * 秤内码
     */
    @JsonProperty("plu_code")
    private String pluCode;

    /**
     * 商品传秤，秤的品牌
     */
    @JsonProperty("scale_type")
    private Integer scaleType;
}
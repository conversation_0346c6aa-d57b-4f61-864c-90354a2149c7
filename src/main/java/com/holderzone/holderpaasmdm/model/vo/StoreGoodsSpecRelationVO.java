package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 门店商品规格关系vo
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsSpecRelationVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品规格ID
     */
    @JsonProperty("goods_spec_id")
    private Integer goodsSpecId;

    /**
     * 商品规格排序
     */
    @JsonProperty("goods_spec_sort")
    private Integer goodsSpecSort;

    /**
     * 商品规格名称
     */
    @JsonProperty("goods_spec_name")
    private String goodsSpecName;

    /**
     * 商品规格值ID
     */
    @JsonProperty("goods_spec_detail_id")
    private Integer goodsSpecDetailId;

    /**
     * 商品规格值排序
     */
    @JsonProperty("goods_spec_detail_sort")
    private Integer goodsSpecDetailSort;

    /**
     * 商品规格值名称
     */
    @JsonProperty("goods_spec_detail_name")
    private String goodsSpecDetailName;
}
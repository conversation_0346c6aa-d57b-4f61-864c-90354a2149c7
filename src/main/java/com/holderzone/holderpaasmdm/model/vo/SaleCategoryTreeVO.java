package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * Description: 销售分组VO
 * Author: 向超
 * Date: 2024/11/26 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaleCategoryTreeVO extends SaleCategoryVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售分组商品数量
     */
    @JsonProperty("goods_count")
    private Integer goodsCount;

    /**
     * 销售分组子节点
     */
    private List<SaleCategoryTreeVO> children;
}
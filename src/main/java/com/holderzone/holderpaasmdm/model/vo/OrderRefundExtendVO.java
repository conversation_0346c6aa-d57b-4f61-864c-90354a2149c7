package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Description: 订单退款扩展VO类
 * Author: 向超
 * Date: 2025/01/14 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderRefundExtendVO extends OrderRefundVO implements Serializable {
    /**
     * 客户名称
     */
    @JsonProperty("member_name")
    private String memberName;

    /**
     * 客户手机号
     */
    @JsonProperty("member_phone")
    private String memberPhone;

    /**
     * 入库单信息集合
     */
    @JsonProperty("inbound_number_list")
    private List<InboundInfo> inboundNumberList;

    /**
     * 退款表对应的退款商品表信息
     */
    @JsonProperty("refund_goods_list")
    private List<OrderRefundItemVO> refundGoodsList;

    /**
     * 退款表对应的退款支付表信息
     */
    @JsonProperty("refund_payment_list")
    private List<OrderRefundPaymentVO> refundPaymentList;

    /**
     * 退款表对应的退款折扣表信息
     */
    @JsonProperty("refund_discount_list")
    private List<OrderRefundDiscountVO> refundDiscountList;

    /**
     * Description: 店铺销售方案时间段VO
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class InboundInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 入库单ID
         */
        private Integer id;
        /**
         * 入库单号
         */
        private String code;
    }
}

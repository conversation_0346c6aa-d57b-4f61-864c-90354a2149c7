package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 平台类目分组对象
 *
 * <AUTHOR>
 * @date 2025/5/30 11:25
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsCategoryPlatformVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 分类名称
     */
    @JsonProperty("platform_name")
    private String name;

    /**
     * 父级分类id
     */
    @JsonProperty("parent_id")
    private Integer parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 分类图片URL
     */
    @JsonProperty("category_img")
    private String categoryImg;

    /**
     * 类目扣除比例
     */
    @JsonProperty("deduction_ratio")
    private BigDecimal deductionRatio;

    /**
     * 封面信息
     */
    @JsonProperty("picture")
    private GoodsPictureVO picture;
}

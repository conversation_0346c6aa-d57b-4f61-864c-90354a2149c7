package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * Description: 商品规格基础vo
 * Author: 向超
 * Date: 2025/03/21 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreGoodsSpecVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商品规格ID
     */
    @JsonProperty("goods_spec_id")
    private Integer id;

    /**
     * 商品规格名称
     */
    @JsonProperty("goods_spec_name")
    private String specName;

    /**
     * 商品规格排序
     */
    @JsonProperty("goods_spec_sort")
    private Integer specSort;

    /**
     * 商品规格详情（规格值）列表
     */
    @JsonProperty("goods_spec_detail_list")
    private List<StoreGoodsSpecDetailVO> storeGoodsSpecDetailList;
}
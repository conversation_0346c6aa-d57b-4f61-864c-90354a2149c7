package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 商品组合类型VO
 * Author: 向超
 * Date: 2024/11/26 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsComboTypeVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 商品品牌名称
     */
    private String name;
}
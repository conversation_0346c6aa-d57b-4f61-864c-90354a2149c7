package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Description: 商品标签VO
 * Author: 向超
 * Date: 2024/11/26 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsLabelVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签颜色
     */
    private String color;
}
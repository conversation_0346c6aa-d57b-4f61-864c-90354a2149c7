package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.util.List;


/**
 * Description: 店铺销售方案VO
 * Author: 向超
 * Date: 2024/12/03 15:36
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreSalesProgramVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 销售方案名称
     */
    @JsonProperty("store_sales_program_name")
    private String storeSalesProgramName;

    /**
     * 销售方案状态：true-启用，false-停用
     */
    @JsonProperty("is_enable")
    private Boolean isEnable;

    /**
     * 是否默认销售方案：true-默认，false-自定义
     */
    @JsonProperty("is_default")
    private Boolean isDefault;

    /**
     * 销售方案时段
     */
    @JsonProperty("section_list")
    private List<SectionVO> sectionList;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private Timestamp createdAt;

    /**
     * Description: 店铺销售方案时间段VO
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SectionVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 序号
         */
        @JsonProperty("serial_number")
        private Integer serialNumber;
        /**
         * 开始时刻
         * 02:00
         */
        private LocalTime start;
        /**
         * 结束时刻
         * 06:00
         */
        private LocalTime end;

        /**
         * 门店商品时段售价
         */
        @JsonProperty("selling_price")
        private BigDecimal sellingPrice;

        /**
         * 门店商品时段售价的Id
         */
        @JsonProperty("store_sales_program_goods_price_id")
        private Integer storeSalesProgramGoodsPriceId;
    }
}
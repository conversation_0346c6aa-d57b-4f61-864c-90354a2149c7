package com.holderzone.holderpaasmdm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;


/**
 * Description: 店铺可售商品调价VO
 * Author: 向超
 * Date: 2024/12/17 15:36
 */
@Data
public class StoreGoodsAvailableForSaleAdjustmentPriceVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;

    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createdAt;

    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updatedAt;

    /**
     * 门店ID
     */
    @JsonProperty("store_id")
    private Integer storeId;

    /**
     * 门店名称
     */
    @JsonProperty("store_name")
    private String storeName;
    /**
     * 渠道ID
     */
    @JsonProperty("channel_id")
    private Integer channelId;

    /**
     * 渠道名称
     */
    @JsonProperty("channel_name")
    private String channelName;

    /**
     * 调价来源: 1、移动端；2、POS
     */
    @JsonProperty("source_type")
    private Integer sourceType;

    /**
     * 调价人ID
     */
    @JsonProperty("user_id")
    private Integer userId;

    /**
     * 调价人名称
     */
    @JsonProperty("user_name")
    private String username;

    /**
     * 调价人账号
     */
    @JsonProperty("account")
    private String account;

    /**
     * 店铺商品ID
     */
    @JsonProperty("store_goods_id")
    private Integer storeGoodsId;

    /**
     * 店铺商品名称
     */
    @JsonProperty("store_goods_name")
    private String storeGoodsName;

    /**
     * 店铺SPU编码
     */
    @JsonProperty("spu_code")
    private String spuCode;

    /**
     * 店铺商品UUID
     */
    @JsonProperty("goods_uuid")
    private String goodsUuid;

    /**
     * 门店商品建议售价
     */
    @JsonProperty("selling_price")
    private BigDecimal sellingPrice;

    /**
     * 店铺商品的销售分组ID集合
     */
    @JsonProperty("sale_category_name_list")
    private List<String> saleCategoryNameList;

    /**
     * 店铺商品的销售分组信息
     */
    @JsonProperty("sale_category_info_list")
    private List<SaleCategoryNode> saleCategoryInfoList;

    /**
     * 店铺商品封面ID
     */
    @JsonProperty("cover")
    private Integer cover;

    /**
     * 店铺商品封面
     */
    @JsonProperty("picture")
    private GoodsPictureVO picture;

    /**
     * 店铺商品单位ID
     */
    @JsonProperty("goods_unit")
    private Integer goodsUnit;

    /**
     * 店铺商品单位名称
     */
    @JsonProperty("goods_unit_name")
    private String goodsUnitName;

    /**
     * 门店商品成本
     */
    @JsonProperty("costs")
    private BigDecimal costs;

    /**
     * 店铺商品条码
     */
    @JsonProperty("barcode")
    private String barcode;

    /**
     * 1 单规格 2 多规格
     */
    @JsonProperty("goods_spec")
    private Integer goodsSpec;

    /**
     * skuId
     */
    @JsonProperty("goods_package_sku_id")
    private Integer goodsPackageSkuId;

    /**
     * skuCode
     */
    @JsonProperty("sku_code")
    private String skuCode;

    /**
     * 店铺商品规格明细
     */
    @JsonProperty("spec_relation_list")
    private List<StoreGoodsSpecRelationVO> specRelationList;

    /**
     * 调价时段
     */
    @JsonProperty("adjustment_time")
    private String adjustmentTime;

    /**
     * 该时段销售策略原售价
     */
    @JsonProperty("sales_program_price")
    private BigDecimal salesProgramPrice;

    /**
     * 调价后售价
     */
    @JsonProperty("adjustment_price")
    private BigDecimal adjustmentPrice;

    /**
     * 商品在该时段销售策略中的售卖名称
     */
    @JsonProperty("goods_sale_name")
    private String goodsSaleName;

    /**
     * 销售方案名称
     */
    @JsonProperty("store_sales_program_name")
    private String storeSalesProgramName;

    /**
     * 销售方案ID
     */
    @JsonProperty("store_sales_program_id")
    private Integer storeSalesProgramId;

    /**
     * 是否为默认销售策略(全时段), true: 是, false: 否
     */
    @JsonProperty("sales_program_is_default")
    private Boolean salesProgramIsDefault;

    /**
     * 销售策略开始时间, 例: 00:00
     */
    @JsonProperty("sales_program_start_time")
    private String salesProgramStartTime;

    /**
     * 销售策略结束时间, 例: 04:00
     */
    @JsonProperty("sales_program_end_time")
    private String salesProgramEndTime;

    /**
     * 店铺商品的销售分组信息
     */
    @Data
    public static class SaleCategoryNode implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 节点ID
         */
        private Integer id;

        /**
         * 节点名称
         */
        private String name;

        /**
         * 父节点ID
         */
        private Integer parentId;

        /**
         * 是否绑定, true: 绑定, false: 未绑定
         */
        private Boolean isBind = false;
    }

    /**
     * 店铺商品的规格明细
     */
    @Data
    public static class StoreGoodsSpecRelationVO implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 商品规格ID
         */
        @JsonProperty("goods_spec_id")
        private Integer goodsSpecId;

        /**
         * 商品规格名称
         */
        @JsonProperty("goods_spec_name")
        private String goodsSpecName;

        /**
         * 商品规格值ID
         */
        @JsonProperty("goods_spec_detail_id")
        private Integer goodsSpecDetailId;

        /**
         * 商品规格值名称
         */
        @JsonProperty("goods_spec_detail_name")
        private String goodsSpecDetailName;
    }
}

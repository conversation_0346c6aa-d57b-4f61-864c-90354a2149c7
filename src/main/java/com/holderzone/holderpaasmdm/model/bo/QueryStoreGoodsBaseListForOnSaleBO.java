package com.holderzone.holderpaasmdm.model.bo;

import com.holderzone.holderpaasmdm.enumeraton.StoreGoodsExpand;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * Description: 查询门店商品基础信息列表（在售商品）BO
 * Author: 向超
 * Date: 2024/11/29 15:36
 */
@Data
@Builder
public class QueryStoreGoodsBaseListForOnSaleBO {
    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 每页条数，最大一页500条数据
     */
    private Integer limit;

    /**
     * 适用范围（按渠道下的门店） (门店销售方案)
     */
    private Integer storeId;

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    private Integer channelId;

    /**
     * 关键字 (商品名称、商品自编码、商品条码、首字母等模糊查询)
     */
    private String keyword;

    /**
     * 销售分组ID (用于指定某个销售分组的查询，如果为0，则查询所有销售分组的商品)
     */
    private List<Integer> selaCategoryIdList;

    /**
     * 店铺商品ID列表 (用于指定某些商品的查询，如果为空，则查询所有销售方案商品)
     */
    private List<Integer> storeGoodsIdList;

    /**
     * 该字段用于指定店铺商品需要额外查询哪些扩展信息，如：销售分组、商品品牌等。
     */
    private List<StoreGoodsExpand> storeGoodsExpandList;

    /**
     * Sku编码列表
     */
    private List<String> skuCodeList;
}

package com.holderzone.holderpaasmdm.model.bo;

import com.holderzone.holderpaasmdm.enumeraton.RefundStatus;
import com.holderzone.holderpaasmdm.enumeraton.RefundType;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Description: 订单退款列表BO
 * Author: 向超
 * Date: 2025/01/14 15:33
 */
@Data
public class QueryOrderRefundListBO {
    /**
     * 页大小
     */
    private Integer limit;
    /**
     * 偏移量
     */
    private Integer offset;
    /**
     * 门店id
     */
    private Integer storeId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 模糊查询关键字
     */
    private String keywords;

    /**
     * 根据Keywords模糊查询的订单退单号列表
     */
    private List<String> orderRefundNumberList;

    /**
     * 退款类型，不传就是查全部
     * See: {@link RefundType}
     */
    private List<RefundType> refundTypeList;

    /**
     * 退款状态，不传就是查全部
     * See: {@link RefundStatus}
     */
    private RefundStatus refundStatus;

    /**
     * 支付方式，不传就是查全部
     */
    private List<Integer> refundMethodList;

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 订单来源列表，不传就是查全部
     */
    private List<String> orderSourceList;

    /**
     * 判断是否需要查询异常退款
     * （目前该方法是在SQL的XML文件中引用的）
     * 不要删除
     *
     * @return true：需要，false：不需要
     */
    public boolean hasExceptionRefund() {
        return this.refundTypeList.contains(RefundType.EXCEPTION_REFUND);
    }

    /**
     * 获取全部退款类型，1：FULL, 2：PART, 3：CASH, 4：PART_CASH
     * （目前该方法是在SQL的XML文件中引用的）
     * 不要删除
     *
     * @return 全部退款类型
     */
    public List<Integer> getRefundTypeIdList() {
        List<Integer> result = new ArrayList<>();
        if (this.refundTypeList == null) {
            return result;
        }
        if (this.refundTypeList.contains(RefundType.FULL_REFUND)) {
            result.add(1);
            result.add(3);
        }
        if (this.refundTypeList.contains(RefundType.PARTIAL_REFUND)) {
            result.add(2);
            result.add(4);
        }
        return result;
    }

    /**
     * 获取异常退款类型
     * （目前该方法是在SQL的XML文件中引用的）
     * 不要删除
     *
     * @return 异常退款类型
     */
    public String getExceptionRefundType() {
        return "异常退款";
    }
}



























package com.holderzone.holderpaasmdm.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalTime;

/**
 * Description: 店铺销售方案时段信息BO
 * Author: 向超
 * Date: 2024/12/11 15:36
 */
@Data
@Builder
@AllArgsConstructor
public class StoreSalesProgramSectionBO {
    /**
     * 店铺销售方案ID
     */
    private Integer storeSalesProgramId;
    /**
     * 店铺销售方案名称
     */
    private String storeSalesProgramName;

    /**
     * 店铺销售方案中时段序号
     */
    private Integer serialNumber;

    /**
     * 开始时刻
     * 02:00
     */
    private LocalTime start;
    /**
     * 结束时刻
     * 06:00
     */
    private LocalTime end;
}

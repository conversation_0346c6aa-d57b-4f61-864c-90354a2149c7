package com.holderzone.holderpaasmdm.model.bo;

import com.holderzone.holderpaasmdm.model.po.GoodsPackageSkuPO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecPO;
import com.holderzone.holderpaasmdm.model.po.SpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.SpecPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPO;
import com.holderzone.holderpaasmdm.model.po.StoreSalesProgramGoodsPricePO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Description: 用于组装商品规格详情列表的业务对象 (针对在售商品过滤接口)
 * Author: 向超
 * Date: 2025/03/26 15:36
 */
@Data
@Builder
@AllArgsConstructor
public class AssembleForFilterStoreGoodsSpecDetailListBO {
    /**
     * 门店商品ID和销售方案商品时段价格信息的映射
     */
    private Map<Integer, StoreSalesProgramGoodsPricePO> storeGoodsIdAndSalesProgramGoodsPricePOMap;
    /**
     * 门店商品ID和商品SKU信息的映射
     */
    private Map<Integer, List<GoodsPackageSkuPO>> storeGoodsIdAndGoodsPackageSkuPOListMap;

    /**
     * 每个SKU商品对应的销售方案商品信息
     */
    private List<StoreSalesProgramGoodsPO> storeSalesProgramGoodsPOList;

    /**
     * 商品单位ID和单位名称的映射
     */
    private Map<Integer, String> unitIdAndNameMap;

    /**
     * 商品SkuID和商品规格值ID的映射
     */
    private Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap;

    /**
     * 商品规格值的ID和商品规格值的信息的映射
     */
    private Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap;

    /**
     * 商品规格ID和商品规格信息的映射
     */
    private Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap;

    /**
     * 规格ID和规格信息的映射
     */
    private Map<Integer, SpecPO> specIdAndSpecInfoMap;

    /**
     * 规格值ID和规格值信息的映射
     */
    private Map<Integer, SpecDetailPO> specDetailIdAndInfoMap;
}

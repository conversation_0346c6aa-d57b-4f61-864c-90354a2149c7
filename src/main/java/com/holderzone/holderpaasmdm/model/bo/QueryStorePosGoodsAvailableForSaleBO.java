package com.holderzone.holderpaasmdm.model.bo;

import com.holderzone.holderpaasmdm.enumeraton.GoodsValuationMethod;
import com.holderzone.holderpaasmdm.enumeraton.StoreSource;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * Description: 查询POS渠道的可售商品列表BO
 * Author: 向超
 * Date: 2025/02/08 15:36
 */
@Data
@Builder
public class QueryStorePosGoodsAvailableForSaleBO {
    /**
     * 关键字,商品名称（商品名称和销售名称）\商品条码\SPU编码\首字母
     */
    private String keywords;

    /**
     * 销售分组ID
     * 不传则查询全部，传则查询指定销售分组及其子分组的商品
     */
    private Integer saleCategoryId;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 商品来源
     * See {@link StoreSource}
     */
    private StoreSource storeSource;

    /**
     * 商品计价方式
     * See {@link GoodsValuationMethod}
     */
    private List<GoodsValuationMethod> goodsValuationMethodList;

    /**
     * 品牌ID列表
     */
    private List<Integer> brandIdList;

    /**
     * 标签ID列表
     */
    private List<Integer> coverIdList;

    /**
     * 商品规格：1：单规格 2：多规格
     */
    private Integer goodsSpec;
}

package com.holderzone.holderpaasmdm.model.bo;

import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * Description: 查询店铺的(传秤即：计重、计数且自编码在5位的)可售商品列表BO
 * Author: 向超
 * Date: 2024/11/29 15:36
 */
@Data
@Builder
public class QueryStoreGoodsForAvailableForSaleWeighListBO {
    /**
     * 适用范围（按渠道下的门店） (门店销售方案)
     */
    private Integer storeId;

    /**
     * 适用范围（按渠道） (门店销售方案)
     */
    private Integer channelId;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 店铺商品ID列表
     */
    private List<Integer> storeGoodsIdList;
}

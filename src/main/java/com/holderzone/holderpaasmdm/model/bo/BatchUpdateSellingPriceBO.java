package com.holderzone.holderpaasmdm.model.bo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;


/**
 * Description: 批量修改售价的BO
 * Author: 向超
 * Date: 2025/04/02 15:33
 */
@Data
@Builder
public class BatchUpdateSellingPriceBO {
    /**
     * 修改的价格
     */
    private BigDecimal adjustmentPrice;
    /**
     * 条件ID，可以是 skuId，也可以是销售方案商品价格表的ID
     */
    private Integer conditionId;
}



























package com.holderzone.holderpaasmdm.model.bo;

import com.holderzone.holderpaasmdm.model.po.GoodsPackageSkuPO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.GoodsSpecPO;
import com.holderzone.holderpaasmdm.model.po.SpecDetailPO;
import com.holderzone.holderpaasmdm.model.po.SpecPO;
import com.holderzone.holderpaasmdm.model.vo.GoodsPictureVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Description: 用于组装商品规格详情列表的业务对象(针对商品调价列表接口)
 * Author: 向超
 * Date: 2025/03/26 15:36
 */
@Data
@Builder
@AllArgsConstructor
public class AssembleStoreGoodsSpecDetailListForAvailableForSaleBO {

    /**
     * 商品SKUId和商品规格详情ID的映射
     */
    private Map<Integer, List<Integer>> skuIdAndGoodsSpecDetailIdListMap;

    /**
     * 商品规格详情ID和商品规格详情信息的映射
     */
    private Map<Integer, GoodsSpecDetailPO> goodsSpecDetailIdAndInfoMap;

    /**
     * 商品规格ID和商品规格信息的映射
     */
    private Map<Integer, GoodsSpecPO> goodsSpecIdAndInfoMap;

    /**
     * 商品单位ID和商品单位名称的映射
     */
    private Map<Integer, String> storeGoodsUnitIdAndNameMap;

    /**
     * 规格ID和规格信息的映射
     */
    private Map<Integer, SpecPO> specIdAndSpecInfoMap;

    /**
     * 规格详情ID和规格详情信息的映射
     */
    private Map<Integer, SpecDetailPO> specDetailIdAndInfoMap;

    /**
     * 封面ID和封面信息的映射
     */
    private Map<Integer, GoodsPictureVO> coverIdAndPictureVOMap;

    /**
     * skuId和sku信息的映射
     */
    private Map<Integer, GoodsPackageSkuPO> goodsPackageSkuIdAndInfoMap;
}
